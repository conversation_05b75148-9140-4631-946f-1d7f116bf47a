# Git
.git
.gitignore
.github/

# Documentation
*.md
docs/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Data files (exclude from container)
nse_data/
bse_data/
*.csv
*.html

# Test files
tests/
test_*.py
*_test.py

# Terraform
terraform/
*.tfstate
*.tfstate.*
.terraform/

# Secrets
secrets/
*.key
*.pem
*.p12

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# Node modules (from tests)
node_modules/
package-lock.json
