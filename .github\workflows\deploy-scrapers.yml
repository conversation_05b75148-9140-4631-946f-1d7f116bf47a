name: Deploy Scrapers to Google Cloud Platform

on:
  push:
    branches:
      - main
    paths:
      - 'nse_scraper/**'
      - 'bse_scraper/**'
      - 'shared/**'
      - 'config/**'
      - 'requirements.txt'
      - '.github/workflows/deploy-scrapers.yml'
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if no changes detected'
        required: false
        default: 'false'
        type: boolean
      deploy_scheduler:
        description: 'Update Cloud Scheduler jobs'
        required: false
        default: 'true'
        type: boolean

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: asia-south1
  REPOSITORY_NAME: scrapers

jobs:
  setup:
    name: Setup and Validate
    runs-on: ubuntu-latest
    outputs:
      project_id: ${{ steps.config.outputs.project_id }}
      region: ${{ steps.config.outputs.region }}
      registry_url: ${{ steps.config.outputs.registry_url }}
      scrapers: ${{ steps.config.outputs.scrapers }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Load configuration
        id: config
        run: |
          # Extract configuration from config file
          PROJECT_ID=$(jq -r '.global.project_id' config/scrapers-config.json)
          REGION=$(jq -r '.global.region' config/scrapers-config.json)
          REGISTRY_URL="${REGION}-docker.pkg.dev/${PROJECT_ID}/scrapers"
          SCRAPERS=$(jq -c '.scrapers | keys' config/scrapers-config.json)
          
          echo "project_id=${PROJECT_ID}" >> $GITHUB_OUTPUT
          echo "region=${REGION}" >> $GITHUB_OUTPUT
          echo "registry_url=${REGISTRY_URL}" >> $GITHUB_OUTPUT
          echo "scrapers=${SCRAPERS}" >> $GITHUB_OUTPUT
          
          echo "Configuration loaded:"
          echo "  Project ID: ${PROJECT_ID}"
          echo "  Region: ${REGION}"
          echo "  Registry URL: ${REGISTRY_URL}"
          echo "  Scrapers: ${SCRAPERS}"

      - name: Validate configuration
        run: |
          # Validate required files exist
          required_files=(
            "config/scrapers-config.json"
            "requirements.txt"
            "nse_scraper/Dockerfile"
            "bse_scraper/Dockerfile"
          )
          
          for file in "${required_files[@]}"; do
            if [[ ! -f "$file" ]]; then
              echo "❌ Required file missing: $file"
              exit 1
            fi
          done
          
          echo "✅ All required files present"

  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: setup
    strategy:
      matrix:
        scraper: ${{ fromJson(needs.setup.outputs.scrapers) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker ${{ needs.setup.outputs.region }}-docker.pkg.dev --quiet

      - name: Extract scraper configuration
        id: scraper_config
        run: |
          SCRAPER_NAME=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].name" config/scrapers-config.json)
          IMAGE_NAME=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].image_name" config/scrapers-config.json)
          DOCKERFILE_PATH=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].dockerfile_path" config/scrapers-config.json)
          BUILD_CONTEXT=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].build_context" config/scrapers-config.json)

          echo "scraper_name=${SCRAPER_NAME}" >> $GITHUB_OUTPUT
          echo "image_name=${IMAGE_NAME}" >> $GITHUB_OUTPUT
          echo "dockerfile_path=${DOCKERFILE_PATH}" >> $GITHUB_OUTPUT
          echo "build_context=${BUILD_CONTEXT}" >> $GITHUB_OUTPUT

      - name: Build and push Docker image
        run: |
          # Generate image tags
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          COMMIT_SHA=$(echo ${{ github.sha }} | cut -c1-8)

          # Read project ID directly from config to avoid secret masking issues
          PROJECT_ID=$(jq -r '.global.project_id' config/scrapers-config.json)
          REGISTRY_URL="${{ needs.setup.outputs.region }}-docker.pkg.dev/${PROJECT_ID}/scrapers"
          IMAGE_TAG="${REGISTRY_URL}/${{ steps.scraper_config.outputs.image_name }}:${TIMESTAMP}-${COMMIT_SHA}"
          LATEST_TAG="${REGISTRY_URL}/${{ steps.scraper_config.outputs.image_name }}:latest"
          
          echo "Building image: ${IMAGE_TAG}"
          
          # Build the Docker image
          docker build \
            -f ${{ steps.scraper_config.outputs.dockerfile_path }} \
            -t "${IMAGE_TAG}" \
            -t "${LATEST_TAG}" \
            ${{ steps.scraper_config.outputs.build_context }}
          
          # Push the images
          docker push "${IMAGE_TAG}"
          docker push "${LATEST_TAG}"
          
          echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV
          echo "LATEST_TAG=${LATEST_TAG}" >> $GITHUB_ENV

      - name: Save image URL
        run: |
          echo "${{ env.LATEST_TAG }}" > ${{ matrix.scraper }}-image-url.txt

      - name: Upload image URL artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.scraper }}-image-url
          path: ${{ matrix.scraper }}-image-url.txt
          retention-days: 1

  deploy-jobs:
    name: Deploy Cloud Run Jobs
    runs-on: ubuntu-latest
    needs: [setup, build-and-push]
    strategy:
      matrix:
        scraper: ${{ fromJson(needs.setup.outputs.scrapers) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Download image URL artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.scraper }}-image-url

      - name: Extract configuration
        id: config
        run: |
          # Load scraper configuration
          SCRAPER_NAME=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].name" config/scrapers-config.json)
          IMAGE_URL=$(cat ${{ matrix.scraper }}-image-url.txt)

          # Load Cloud Run configuration
          CPU=$(jq -r '.environment.cloud_run.cpu' config/scrapers-config.json)
          MEMORY=$(jq -r '.environment.cloud_run.memory' config/scrapers-config.json)
          MAX_INSTANCES=$(jq -r '.environment.cloud_run.max_instances' config/scrapers-config.json)
          TIMEOUT=$(jq -r '.environment.cloud_run.timeout' config/scrapers-config.json)
          EXECUTION_ENV=$(jq -r '.environment.cloud_run.execution_environment' config/scrapers-config.json)

          # Service account - read project ID directly to avoid secret masking
          SERVICE_ACCOUNT_NAME=$(jq -r '.security.service_accounts.scraper_runner.name' config/scrapers-config.json)
          PROJECT_ID=$(jq -r '.global.project_id' config/scrapers-config.json)
          SERVICE_ACCOUNT="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

          echo "scraper_name=${SCRAPER_NAME}" >> $GITHUB_OUTPUT
          echo "image_url=${IMAGE_URL}" >> $GITHUB_OUTPUT
          echo "cpu=${CPU}" >> $GITHUB_OUTPUT
          echo "memory=${MEMORY}" >> $GITHUB_OUTPUT
          echo "max_instances=${MAX_INSTANCES}" >> $GITHUB_OUTPUT
          echo "timeout=${TIMEOUT}" >> $GITHUB_OUTPUT
          echo "execution_env=${EXECUTION_ENV}" >> $GITHUB_OUTPUT
          echo "service_account=${SERVICE_ACCOUNT}" >> $GITHUB_OUTPUT

      - name: Build environment variables
        id: env_vars
        run: |
          # Build environment variables from config
          ENV_VARS=""

          # Add scraper-specific env vars
          ENV_VAR_KEYS=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].env_vars | keys[]" config/scrapers-config.json 2>/dev/null || echo "")
          for key in $ENV_VAR_KEYS; do
            value=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].env_vars[\"$key\"]" config/scrapers-config.json)
            ENV_VARS="$ENV_VARS --set-env-vars $key=$value"
          done

          # Add secrets
          ENV_VARS="$ENV_VARS --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest"
          ENV_VARS="$ENV_VARS --set-secrets SUPABASE_SERVICE_KEY=supabase-service-key:latest"

          echo "env_vars=${ENV_VARS}" >> $GITHUB_OUTPUT

      - name: Build command arguments
        id: args
        run: |
          # Build command arguments from config
          CMD_ARGS=""

          ARG_KEYS=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].args | keys[]" config/scrapers-config.json 2>/dev/null || echo "")
          for key in $ARG_KEYS; do
            value=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].args[\"$key\"]" config/scrapers-config.json)
            if [[ "$value" == "true" ]]; then
              CMD_ARGS="$CMD_ARGS --$key"
            elif [[ "$value" != "false" ]]; then
              CMD_ARGS="$CMD_ARGS --$key $value"
            fi
          done

          echo "cmd_args=${CMD_ARGS}" >> $GITHUB_OUTPUT

      - name: Deploy Cloud Run job
        run: |
          echo "Deploying ${{ steps.config.outputs.scraper_name }}..."
          
          # Try to create the job first
          gcloud run jobs create "${{ steps.config.outputs.scraper_name }}" \
            --image="${{ steps.config.outputs.image_url }}" \
            --region="${{ needs.setup.outputs.region }}" \
            --service-account="${{ steps.config.outputs.service_account }}" \
            --cpu="${{ steps.config.outputs.cpu }}" \
            --memory="${{ steps.config.outputs.memory }}" \
            --max-retries=3 \
            --parallelism=1 \
            --task-timeout="${{ steps.config.outputs.timeout }}s" \
            --execution-environment="${{ steps.config.outputs.execution_env }}" \
            --labels="scraper-type=${{ matrix.scraper }},environment=prod,managed-by=github-actions" \
            ${{ steps.env_vars.outputs.env_vars }} \
            $(if [ -n "${{ steps.args.outputs.cmd_args }}" ]; then echo "--args=${{ steps.args.outputs.cmd_args }}"; fi) \
            --quiet || \
          
          # If creation fails, update instead
          gcloud run jobs update "${{ steps.config.outputs.scraper_name }}" \
            --image="${{ steps.config.outputs.image_url }}" \
            --region="${{ needs.setup.outputs.region }}" \
            --service-account="${{ steps.config.outputs.service_account }}" \
            --cpu="${{ steps.config.outputs.cpu }}" \
            --memory="${{ steps.config.outputs.memory }}" \
            --max-retries=3 \
            --parallelism=1 \
            --task-timeout="${{ steps.config.outputs.timeout }}s" \
            --execution-environment="${{ steps.config.outputs.execution_env }}" \
            ${{ steps.env_vars.outputs.env_vars }} \
            --args="${{ steps.args.outputs.cmd_args }}" \
            --quiet
          
          echo "✅ ${{ steps.config.outputs.scraper_name }} deployed successfully"

  update-scheduler:
    name: Update Cloud Scheduler
    runs-on: ubuntu-latest
    needs: [setup, deploy-jobs]
    if: ${{ github.event.inputs.deploy_scheduler != 'false' }}
    strategy:
      matrix:
        scraper: ${{ fromJson(needs.setup.outputs.scrapers) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Extract scheduler configuration
        id: config
        run: |
          SCRAPER_NAME=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].name" config/scrapers-config.json)
          CRON_SCHEDULE=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].schedule.cron" config/scrapers-config.json)
          SCHEDULE_ENABLED=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].schedule.enabled" config/scrapers-config.json)
          SCHEDULE_DESCRIPTION=$(jq -r ".scrapers[\"${{ matrix.scraper }}\"].schedule.description" config/scrapers-config.json)
          TIMEZONE=$(jq -r '.environment.cloud_scheduler.timezone' config/scrapers-config.json)

          echo "scraper_name=${SCRAPER_NAME}" >> $GITHUB_OUTPUT
          echo "cron_schedule=${CRON_SCHEDULE}" >> $GITHUB_OUTPUT
          echo "schedule_enabled=${SCHEDULE_ENABLED}" >> $GITHUB_OUTPUT
          echo "schedule_description=${SCHEDULE_DESCRIPTION}" >> $GITHUB_OUTPUT
          echo "timezone=${TIMEZONE}" >> $GITHUB_OUTPUT

      - name: Check Cloud Scheduler API
        run: |
          echo "Checking if Cloud Scheduler API is enabled..."
          if gcloud services list --enabled --filter="name:cloudscheduler.googleapis.com" --format="value(name)" | grep -q cloudscheduler.googleapis.com; then
            echo "✅ Cloud Scheduler API is enabled"
          else
            echo "❌ Cloud Scheduler API is not enabled"
            echo "Please enable the Cloud Scheduler API manually in the GCP Console:"
            echo "https://console.cloud.google.com/apis/library/cloudscheduler.googleapis.com"
            exit 1
          fi

      - name: Setup service account
        run: |
          # Read configuration directly to avoid masking
          SCHEDULER_SA_NAME=$(jq -r '.security.service_accounts.scheduler.name' config/scrapers-config.json)
          PROJECT_ID=$(jq -r '.global.project_id' config/scrapers-config.json)

          # Check if service account exists
          if gcloud iam service-accounts describe "${SCHEDULER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" --quiet 2>/dev/null; then
            echo "✅ Service account ${SCHEDULER_SA_NAME} already exists"
          else
            echo "❌ Service account ${SCHEDULER_SA_NAME} does not exist"
            echo "Please create the service account manually in the GCP Console:"
            echo "1. Go to: https://console.cloud.google.com/iam-admin/serviceaccounts"
            echo "2. Click 'Create Service Account'"
            echo "3. Service account name: ${SCHEDULER_SA_NAME}"
            echo "4. Display name: Cloud Scheduler Service Account"
            echo "5. Description: Service account for Cloud Scheduler to invoke Cloud Run jobs"
            echo "6. Grant the 'Cloud Run Invoker' role"
            echo ""
            echo "Or run this command with appropriate permissions:"
            echo "gcloud iam service-accounts create ${SCHEDULER_SA_NAME} --display-name='Cloud Scheduler Service Account' --description='Service account for Cloud Scheduler to invoke Cloud Run jobs'"
            echo "gcloud projects add-iam-policy-binding ${PROJECT_ID} --member='serviceAccount:${SCHEDULER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com' --role='roles/run.invoker'"
            exit 1
          fi

          # Verify and grant necessary permissions if needed
          echo "Ensuring Cloud Run Invoker role is granted..."
          gcloud projects add-iam-policy-binding "$PROJECT_ID" \
            --member="serviceAccount:${SCHEDULER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
            --role="roles/run.invoker" \
            --quiet || echo "⚠️  Could not grant role (may already exist or insufficient permissions)"

      - name: Update scheduler job
        if: ${{ steps.config.outputs.schedule_enabled == 'true' }}
        run: |
          SCHEDULER_JOB_NAME="${{ steps.config.outputs.scraper_name }}-scheduler"

          # Read configuration directly to avoid secret masking
          PROJECT_ID=$(jq -r '.global.project_id' config/scrapers-config.json)
          REGION=$(jq -r '.global.region' config/scrapers-config.json)
          SCHEDULER_SA_NAME=$(jq -r '.security.service_accounts.scheduler.name' config/scrapers-config.json)
          SCHEDULER_SA="${SCHEDULER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
          CLOUD_RUN_JOB_URL="https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${{ steps.config.outputs.scraper_name }}:run"

          echo "Updating scheduler job: ${SCHEDULER_JOB_NAME}"
          echo "Service account: ${SCHEDULER_SA}"
          echo "Cloud Run URL: ${CLOUD_RUN_JOB_URL}"

          # Try to update existing job first
          gcloud scheduler jobs update http "${SCHEDULER_JOB_NAME}" \
            --location="${REGION}" \
            --schedule="${{ steps.config.outputs.cron_schedule }}" \
            --time-zone="${{ steps.config.outputs.timezone }}" \
            --uri="${CLOUD_RUN_JOB_URL}" \
            --http-method=POST \
            --oidc-service-account-email="${SCHEDULER_SA}" \
            --oidc-token-audience="${CLOUD_RUN_JOB_URL}" \
            --max-retry-attempts=3 \
            --max-retry-duration=600s \
            --min-backoff=5s \
            --max-backoff=300s \
            --max-doublings=3 \
            --description="${{ steps.config.outputs.schedule_description }}" \
            --quiet || \

          # If update fails, create new job
          gcloud scheduler jobs create http "${SCHEDULER_JOB_NAME}" \
            --location="${REGION}" \
            --schedule="${{ steps.config.outputs.cron_schedule }}" \
            --time-zone="${{ steps.config.outputs.timezone }}" \
            --uri="${CLOUD_RUN_JOB_URL}" \
            --http-method=POST \
            --oidc-service-account-email="${SCHEDULER_SA}" \
            --oidc-token-audience="${CLOUD_RUN_JOB_URL}" \
            --max-retry-attempts=3 \
            --max-retry-duration=600s \
            --min-backoff=5s \
            --max-backoff=300s \
            --max-doublings=3 \
            --description="${{ steps.config.outputs.schedule_description }}" \
            --quiet

          echo "✅ Scheduler job ${SCHEDULER_JOB_NAME} updated successfully"

  notify:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [setup, build-and-push, deploy-jobs, update-scheduler]
    if: always()
    steps:
      - name: Deployment Summary
        run: |
          echo "🎉 Deployment Summary"
          echo "===================="
          echo "Project: ${{ needs.setup.outputs.project_id }}"
          echo "Region: ${{ needs.setup.outputs.region }}"
          echo "Commit: ${{ github.sha }}"
          echo "Triggered by: ${{ github.event_name }}"
          
          if [[ "${{ needs.build-and-push.result }}" == "success" ]]; then
            echo "✅ Images built and pushed successfully"
          else
            echo "❌ Image build failed"
          fi
          
          if [[ "${{ needs.deploy-jobs.result }}" == "success" ]]; then
            echo "✅ Cloud Run jobs deployed successfully"
          else
            echo "❌ Cloud Run job deployment failed"
          fi
          
          if [[ "${{ needs.update-scheduler.result }}" == "success" ]]; then
            echo "✅ Cloud Scheduler updated successfully"
          else
            echo "⚠️ Cloud Scheduler update skipped or failed"
          fi
          
          echo ""
          echo "🔗 Useful Links:"
          echo "Cloud Run Jobs: https://console.cloud.google.com/run/jobs?project=${{ needs.setup.outputs.project_id }}"
          echo "Cloud Scheduler: https://console.cloud.google.com/cloudscheduler?project=${{ needs.setup.outputs.project_id }}"
          echo "Cloud Logging: https://console.cloud.google.com/logs/query?project=${{ needs.setup.outputs.project_id }}"
