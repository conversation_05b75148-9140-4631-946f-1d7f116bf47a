name: Test Scrapers

on:
  pull_request:
    branches:
      - main
    paths:
      - 'nse_scraper/**'
      - 'bse_scraper/**'
      - 'shared/**'
      - 'requirements.txt'
      - 'tests/**'
  push:
    branches:
      - main
    paths:
      - 'nse_scraper/**'
      - 'bse_scraper/**'
      - 'shared/**'
      - 'requirements.txt'
      - 'tests/**'
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.12'

jobs:
  lint-and-format:
    name: Code Quality Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort mypy
          pip install -r requirements.txt

      - name: Run Black (code formatting)
        run: |
          black --check --diff nse_scraper/ bse_scraper/ shared/

      - name: Run isort (import sorting)
        run: |
          isort --check-only --diff nse_scraper/ bse_scraper/ shared/

      - name: Run flake8 (linting)
        run: |
          flake8 nse_scraper/ bse_scraper/ shared/ --max-line-length=88 --extend-ignore=E203,W503

      - name: Run mypy (type checking)
        run: |
          mypy nse_scraper/ bse_scraper/ shared/ --ignore-missing-imports

  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        scraper: [nse_scraper, bse_scraper]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-cov pytest-mock
          pip install -r requirements.txt

      - name: Run unit tests
        run: |
          if [ -d "tests/${{ matrix.scraper }}" ]; then
            pytest tests/${{ matrix.scraper }}/ -v --cov=${{ matrix.scraper }} --cov-report=xml
          else
            echo "No tests found for ${{ matrix.scraper }}, skipping..."
          fi

      - name: Upload coverage to Codecov
        if: matrix.scraper == 'nse_scraper'
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [lint-and-format, unit-tests]
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pytest pytest-mock
          pip install -r requirements.txt

      - name: Set up test environment
        run: |
          # Create test environment variables
          echo "SUPABASE_URL=https://test.supabase.co" >> $GITHUB_ENV
          echo "SUPABASE_ANON_KEY=test-anon-key" >> $GITHUB_ENV
          echo "SUPABASE_SERVICE_KEY=test-service-key" >> $GITHUB_ENV

      - name: Run integration tests
        run: |
          if [ -d "tests/integration" ]; then
            pytest tests/integration/ -v --tb=short
          else
            echo "No integration tests found, skipping..."
          fi

  docker-build-test:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [lint-and-format]
    strategy:
      matrix:
        scraper: [nse_scraper, bse_scraper]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build Docker image
        run: |
          docker build -f ${{ matrix.scraper }}/Dockerfile -t ${{ matrix.scraper }}:test .

      - name: Test Docker image
        run: |
          # Test that the image can start without errors
          docker run --rm ${{ matrix.scraper }}:test --help || true

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [lint-and-format, unit-tests, docker-build-test, security-scan]
    if: always()
    steps:
      - name: Test Results Summary
        run: |
          echo "🧪 Test Results Summary"
          echo "======================"
          
          if [[ "${{ needs.lint-and-format.result }}" == "success" ]]; then
            echo "✅ Code quality checks passed"
          else
            echo "❌ Code quality checks failed"
          fi
          
          if [[ "${{ needs.unit-tests.result }}" == "success" ]]; then
            echo "✅ Unit tests passed"
          else
            echo "❌ Unit tests failed"
          fi
          
          if [[ "${{ needs.docker-build-test.result }}" == "success" ]]; then
            echo "✅ Docker build tests passed"
          else
            echo "❌ Docker build tests failed"
          fi
          
          if [[ "${{ needs.security-scan.result }}" == "success" ]]; then
            echo "✅ Security scan completed"
          else
            echo "⚠️ Security scan had issues"
          fi
