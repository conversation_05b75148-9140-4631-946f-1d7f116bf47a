import "jsr:@supabase/functions-js/edge-runtime.d.ts";

interface AlertRecord {
  id: number;
  alert_date: string;
  alert_data: any[];
  alert_message: string;
  record_count: number;
  message_status: string;
  created_at: string;
}

interface WhatsAppResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Send WhatsApp message using Periskope API
 */
async function sendWhatsAppMessage(message: string, chatId: string): Promise<WhatsAppResponse> {
  try {
    const response = await fetch('https://api.periskope.app/v1/message/send', {
      method: 'POST',
      headers: {
        'x-phone': '+91 93210 00154',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TbNthsytB-kFR83b7SsJxCU7JiJRjAYnrO-SDjujHZE'
      },
      body: JSON.stringify({
        chat_id: chatId,
        message: message
      })
    });

    const result = await response.json();

    if (response.ok) {
      return { success: true, message: 'Message sent successfully' };
    } else {
      return { success: false, error: result.error || 'Failed to send message' };
    }
  } catch (error) {
    return { success: false, error: `Network error: ${error.message}` };
  }
}



Deno.serve(async (req: Request) => {
  console.log("Insider trading alert trigger activated...");

  try {
    // Parse request body to get trigger information
    const requestBody = await req.json().catch(() => ({}));
    const alertId = requestBody.alert_id;
    const triggerType = requestBody.trigger || 'manual';

    console.log(`Trigger type: ${triggerType}, Alert ID: ${alertId}`);

    // Load environment variables
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");

    // Fixed WhatsApp configuration
    const whatsappChatId = "<EMAIL>";

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing required Supabase environment variables");
    }

    let alertsToProcess: AlertRecord[] = [];

    if (alertId) {
      // Process specific alert triggered by database insert
      console.log(`Fetching specific alert ID: ${alertId}`);
      const specificAlertResponse = await fetch(
        `${supabaseUrl}/rest/v1/insider_trading_alerts?id=eq.${alertId}&message_status=eq.PENDING`,
        {
          method: "GET",
          headers: {
            apikey: supabaseServiceKey,
            Authorization: `Bearer ${supabaseServiceKey}`,
            "Content-Type": "application/json"
          }
        }
      );

      if (specificAlertResponse.ok) {
        alertsToProcess = await specificAlertResponse.json();
      }
    } else {
      // Fallback: Process all pending alerts (for manual calls)
      console.log("Fetching all pending alerts...");
      const allAlertsResponse = await fetch(
        `${supabaseUrl}/rest/v1/insider_trading_alerts?message_status=eq.PENDING&order=created_at.desc`,
        {
          method: "GET",
          headers: {
            apikey: supabaseServiceKey,
            Authorization: `Bearer ${supabaseServiceKey}`,
            "Content-Type": "application/json"
          }
        }
      );

      if (allAlertsResponse.ok) {
        alertsToProcess = await allAlertsResponse.json();
      }
    }

    console.log(`Found ${alertsToProcess.length} alerts to process`);

    if (alertsToProcess.length === 0) {
      return new Response(JSON.stringify({
        message: "No pending alerts to process",
        alert_id: alertId,
        trigger_type: triggerType,
        processed_count: 0
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    }

    let successCount = 0;
    let errorCount = 0;
    const processedAlertIds: number[] = [];

    // Process each alert
    for (const alert of alertsToProcess) {
      console.log(`Sending alert ID: ${alert.id} for date: ${alert.alert_date} (${alert.record_count} records)`);

      // Check message length (WhatsApp limit is 4096 characters)
      let messageToSend = alert.alert_message;
      if (messageToSend.length > 4000) {
        messageToSend = messageToSend.substring(0, 3900) + '\n\n... (Message truncated due to length)';
        console.log(`Message truncated for alert ID: ${alert.id}`);
      }

      const whatsappResult = await sendWhatsAppMessage(messageToSend, whatsappChatId);

      if (whatsappResult.success) {
        console.log(`Successfully sent alert ID: ${alert.id} for date: ${alert.alert_date}`);
        successCount++;
        processedAlertIds.push(alert.id);
      } else {
        console.error(`Failed to send alert ID: ${alert.id} for date ${alert.alert_date}:`, whatsappResult.error);
        errorCount++;
      }
    }

    // Update message status for successfully sent alerts
    if (processedAlertIds.length > 0) {
      console.log(`Updating status for ${processedAlertIds.length} alerts...`);

      const updateResponse = await fetch(
        `${supabaseUrl}/rest/v1/insider_trading_alerts?id=in.(${processedAlertIds.join(',')})`,
        {
          method: "PATCH",
          headers: {
            apikey: supabaseServiceKey,
            Authorization: `Bearer ${supabaseServiceKey}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            message_status: 'SENT',
            sent_at: new Date().toISOString()
          })
        }
      );

      if (updateResponse.ok) {
        console.log(`Successfully updated status for ${processedAlertIds.length} alerts`);
      } else {
        console.error("Failed to update alert status:", updateResponse.statusText);
      }
    }

    return new Response(JSON.stringify({
      message: "Alert processing completed",
      trigger_type: triggerType,
      alert_id: alertId,
      total_alerts: alertsToProcess.length,
      messages_sent: successCount,
      failed_messages: errorCount,
      processed_alert_ids: processedAlertIds,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });

  } catch (error: any) {
    console.error("Error in alert processing:", error);
    return new Response(JSON.stringify({
      error: "Alert processing failed",
      message: error.message || "Unknown error occurred",
      stack: error.stack,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});
