import "jsr:@supabase/functions-js/edge-runtime.d.ts";

/**
 * Utility function to normalize null values
 * Converts string representations of null to actual null values
 */
function normalizeNullValue(value: any): any {
  if (value === null || value === undefined) {
    return null;
  }

  const stringValue = String(value).trim().toLowerCase();
  const nullVariants = [
    'null', 'nil', 'none', 'n/a', 'na', '#n/a', '#na',
    'nan', 'undefined', 'empty', '-', '', 'null/nil'
  ];

  if (nullVariants.includes(stringValue)) {
    return null;
  }

  return value;
}

/**
 * Generate a unique hash for company record to detect duplicates
 * Uses key fields that uniquely identify a company
 */
function generateCompanyHash(company: any): string {
  const keyFields = [
    normalizeNullValue(company["bse-code"]) || '',
    normalizeNullValue(company["nse-code"]) || '',
    normalizeNullValue(company.name) || '',
    normalizeNullValue(company.id) || ''
  ];

  // Create a string from key fields and hash it
  const keyString = keyFields.join('|').toLowerCase();

  // Simple hash function (since crypto is not available in edge runtime)
  let hash = 0;
  for (let i = 0; i < keyString.length; i++) {
    const char = keyString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
}



/**
 * Validate company data and ensure required fields are present
 */
function validateCompanyData(company: any, index: number): boolean {
  // At least one of BSE code, NSE code, or name should be present
  const bseCode = normalizeNullValue(company["bse-code"]);
  const nseCode = normalizeNullValue(company["nse-code"]);
  const name = normalizeNullValue(company.name);

  if (!bseCode && !nseCode && !name) {
    console.warn(`Skipping company at index ${index}: No valid identifier found`);
    return false;
  }

  return true;
}

Deno.serve(async (_req: Request) => {
  console.log("Starting company sync process...");
  const apiUrl = "https://dev.indianapi.in/static/all_stocks.json";
  try {
    // Step 1: Fetch data from external API
    const res = await fetch(apiUrl);
    if (!res.ok) {
      throw new Error(`Failed to fetch data: ${res.statusText}`);
    }
    const text = await res.text();
    if (!text.trim()) throw new Error("Empty API response");

    // Step 2: Parse JSON
    const companies = JSON.parse(text);
    if (!Array.isArray(companies)) {
      throw new Error("Invalid API format: expected array");
    }
    console.log("Fetched", companies.length, "companies");

    // Step 3: Process and validate company data
    const processedCompanies = companies.map((company, index) => {
      try {
        // Validate company data
        if (!validateCompanyData(company, index)) {
          return null;
        }

        // Process and normalize data
        const processedCompany = {
          bse_code: normalizeNullValue(company["bse-code"]),
          nse_code: normalizeNullValue(company["nse-code"]),
          name: normalizeNullValue(company.name),
          record_hash: generateCompanyHash(company),
          updated_at: new Date().toISOString()
        };

        return processedCompany;
      } catch (err) {
        console.error(`Error processing company at index ${index}:`, err);
        return null;
      }
    }).filter(Boolean);

    if (processedCompanies.length === 0) {
      return new Response(JSON.stringify({
        message: "No valid companies to sync."
      }), {
        status: 200,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }

    console.log(`Processed ${processedCompanies.length} valid companies`);

    // Step 4: Load Supabase credentials
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseKey = Deno.env.get("SUPABASE_ANON_KEY");
    if (!supabaseUrl || !supabaseKey) {
      throw new Error("Supabase credentials missing");
    }

    // Step 5: Use batch processing to handle large datasets
    const BATCH_SIZE = 500; // Process 500 records at a time to avoid timeouts
    const totalBatches = Math.ceil(processedCompanies.length / BATCH_SIZE);
    console.log(`Processing ${processedCompanies.length} companies in ${totalBatches} batches of ${BATCH_SIZE}...`);

    let totalInserted = 0;
    let totalUpdated = 0;
    let totalSkipped = 0;
    let totalErrors = 0;
    const failedBatches: number[] = [];

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIndex = batchIndex * BATCH_SIZE;
      const endIndex = Math.min(startIndex + BATCH_SIZE, processedCompanies.length);
      const batch = processedCompanies.slice(startIndex, endIndex);

      console.log(`Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} records)...`);

      try {
        // Use the RPC function approach for proper upsert handling
        const dbResponse = await fetch(`${supabaseUrl}/rest/v1/rpc/upsert_company_details`, {
          method: "POST",
          headers: {
            apikey: supabaseKey,
            Authorization: `Bearer ${supabaseKey}`,
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            companies_data: batch
          })
        });

        const dbText = await dbResponse.text();
        let dbResult: any;
        try {
          dbResult = dbText ? JSON.parse(dbText) : null;
        } catch {
          dbResult = {
            raw: dbText
          };
        }

        if (!dbResponse.ok) {
          console.error(`Batch ${batchIndex + 1} failed:`, dbResult);
          failedBatches.push(batchIndex + 1);
          totalErrors += batch.length;
          continue;
        }

        // Parse the result to get statistics
        const batchStats = dbResult || { inserted: 0, updated: 0, skipped: 0, errors: 0 };
        totalInserted += batchStats.inserted || 0;
        totalUpdated += batchStats.updated || 0;
        totalSkipped += batchStats.skipped || 0;
        totalErrors += batchStats.errors || 0;

        console.log(`Batch ${batchIndex + 1} completed: ${batchStats.inserted || 0} inserted, ${batchStats.updated || 0} updated, ${batchStats.skipped || 0} skipped`);

        // Add a small delay between batches to avoid overwhelming the database
        if (batchIndex < totalBatches - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (batchError: any) {
        console.error(`Batch ${batchIndex + 1} error:`, batchError.message);
        failedBatches.push(batchIndex + 1);
        totalErrors += batch.length;
      }
    }

    console.log(`Batch processing completed. Total: ${totalInserted} inserted, ${totalUpdated} updated, ${totalSkipped} skipped, ${totalErrors} errors`);
    if (failedBatches.length > 0) {
      console.warn(`Failed batches: ${failedBatches.join(', ')}`);
    }

    // Determine overall success status
    const overallSuccess = failedBatches.length === 0;
    const statusCode = overallSuccess ? 200 : (failedBatches.length < totalBatches ? 207 : 500);

    return new Response(JSON.stringify({
      message: overallSuccess
        ? "Company details synced successfully"
        : `Partial sync completed. ${failedBatches.length}/${totalBatches} batches failed`,
      total_processed: processedCompanies.length,
      total_batches: totalBatches,
      successful_batches: totalBatches - failedBatches.length,
      failed_batches: failedBatches.length,
      failed_batch_numbers: failedBatches,
      records_inserted: totalInserted,
      records_updated: totalUpdated,
      records_skipped: totalSkipped,
      errors: totalErrors,
      success: overallSuccess,
      batch_size: BATCH_SIZE
    }), {
      status: statusCode,
      headers: {
        "Content-Type": "application/json"
      }
    });

  } catch (err: any) {
    console.error("Unexpected error:", err);
    return new Response(JSON.stringify({
      error: "Unexpected server error",
      message: err.message || "Unknown error occurred",
      stack: err.stack || "No stack trace available"
    }), {
      status: 500,
      headers: {
        "Content-Type": "application/json"
      }
    });
  }
});
