import "jsr:@supabase/functions-js/edge-runtime.d.ts";

/**
 * Test function for WhatsApp alert delivery
 * This function sends a test message to verify the WhatsApp integration
 */

interface WhatsAppResponse {
  success: boolean;
  message?: string;
  error?: string;
}

async function sendWhatsAppMessage(message: string, chatId: string): Promise<WhatsAppResponse> {
  try {
    const response = await fetch('https://api.periskope.app/v1/message/send', {
      method: 'POST',
      headers: {
        'x-phone': '+91 93210 00154',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TbNthsytB-kFR83b7SsJxCU7JiJRjAYnrO-SDjujHZE'
      },
      body: JSON.stringify({
        chat_id: chatId,
        message: message
      })
    });

    const result = await response.json();

    if (response.ok) {
      return { success: true, message: 'Message sent successfully' };
    } else {
      return { success: false, error: result.error || 'Failed to send message' };
    }
  } catch (error) {
    return { success: false, error: `Network error: ${error.message}` };
  }
}

Deno.serve(async (req: Request) => {
  console.log("Testing WhatsApp alert delivery...");

  try {
    // Fixed WhatsApp configuration
    const whatsappChatId = "<EMAIL>";

    // Create test message
    const testMessage = `🧪 Insider Trading Alert System Test

📊 Test Alert - ${new Date().toLocaleDateString('en-GB')}

🏢 TEST_SYMBOL
👤 Test Category
💰 Net Value: ₹1000000 (📈 POSITIVE)
📈 Buy: ₹1000000 | 📉 Sell: ₹N/A

Generated at: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })} IST

✅ This is a test message to verify the alert system is working correctly.`;

    console.log("Sending test message...");
    const result = await sendWhatsAppMessage(testMessage, whatsappChatId);

    if (result.success) {
      return new Response(JSON.stringify({
        success: true,
        message: "Test WhatsApp message sent successfully",
        timestamp: new Date().toISOString(),
        chat_id: whatsappChatId,
        api_endpoint: "https://api.periskope.app/v1/message/send"
      }), {
        status: 200,
        headers: { "Content-Type": "application/json" }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: "Failed to send test message",
        details: result.error,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

  } catch (error: any) {
    console.error("Error in test function:", error);
    return new Response(JSON.stringify({
      success: false,
      error: "Test function failed",
      message: error.message || "Unknown error occurred",
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});
