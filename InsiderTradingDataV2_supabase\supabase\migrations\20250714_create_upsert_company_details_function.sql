-- Create optimized upsert function for company details with batch processing
-- This function handles large datasets efficiently to avoid statement timeouts

CREATE OR REPLACE FUNCTION upsert_company_details(companies_data JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET statement_timeout = '300s'  -- 5 minute timeout for large batches
AS $$
DECLARE
    company_record JSONB;
    inserted_count INTEGER := 0;
    updated_count INTEGER := 0;
    skipped_count INTEGER := 0;
    error_count INTEGER := 0;
    total_count INTEGER := 0;
    result JSONB;
    error_message TEXT;
BEGIN
    -- Validate input
    IF companies_data IS NULL OR jsonb_array_length(companies_data) = 0 THEN
        RETURN jsonb_build_object(
            'inserted', 0,
            'updated', 0,
            'skipped', 0,
            'errors', 0,
            'success', true,
            'message', 'No data provided'
        );
    END IF;

    total_count := jsonb_array_length(companies_data);
    
    -- Log the start of processing
    RAISE NOTICE 'Starting upsert of % company records', total_count;

    -- Process each company record
    FOR company_record IN SELECT * FROM jsonb_array_elements(companies_data)
    LOOP
        BEGIN
            -- Validate required fields
            IF company_record->>'bse_code' IS NULL 
               AND company_record->>'nse_code' IS NULL 
               AND company_record->>'name' IS NULL THEN
                skipped_count := skipped_count + 1;
                CONTINUE;
            END IF;

            -- Perform upsert using ON CONFLICT
            INSERT INTO master_company_details (
                bse_code,
                nse_code,
                name,
                record_hash,
                updated_at
            ) VALUES (
                NULLIF(trim(company_record->>'bse_code'), ''),
                NULLIF(trim(company_record->>'nse_code'), ''),
                NULLIF(trim(company_record->>'name'), ''),
                company_record->>'record_hash',
                COALESCE((company_record->>'updated_at')::timestamp, NOW())
            )
            ON CONFLICT (record_hash) 
            DO UPDATE SET
                bse_code = COALESCE(EXCLUDED.bse_code, master_company_details.bse_code),
                nse_code = COALESCE(EXCLUDED.nse_code, master_company_details.nse_code),
                name = COALESCE(EXCLUDED.name, master_company_details.name),
                updated_at = EXCLUDED.updated_at
            WHERE 
                master_company_details.bse_code IS DISTINCT FROM EXCLUDED.bse_code
                OR master_company_details.nse_code IS DISTINCT FROM EXCLUDED.nse_code
                OR master_company_details.name IS DISTINCT FROM EXCLUDED.name;

            -- Check if it was an insert or update
            IF FOUND THEN
                -- Check if this was an insert (new record) or update (existing record)
                -- We can determine this by checking if the record existed before
                PERFORM 1 FROM master_company_details 
                WHERE record_hash = company_record->>'record_hash' 
                AND created_at = updated_at;
                
                IF FOUND THEN
                    inserted_count := inserted_count + 1;
                ELSE
                    updated_count := updated_count + 1;
                END IF;
            ELSE
                -- No change was made (all values were the same)
                skipped_count := skipped_count + 1;
            END IF;

        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
            error_message := SQLERRM;
            RAISE NOTICE 'Error processing company record: %', error_message;
            -- Continue processing other records
        END;
    END LOOP;

    -- Build result
    result := jsonb_build_object(
        'inserted', inserted_count,
        'updated', updated_count,
        'skipped', skipped_count,
        'errors', error_count,
        'total_processed', total_count,
        'success', error_count = 0,
        'message', CASE 
            WHEN error_count = 0 THEN 'All records processed successfully'
            ELSE format('Processed with %s errors', error_count)
        END
    );

    RAISE NOTICE 'Upsert completed: % inserted, % updated, % skipped, % errors', 
                 inserted_count, updated_count, skipped_count, error_count;

    RETURN result;

EXCEPTION WHEN OTHERS THEN
    -- Handle any unexpected errors
    RAISE NOTICE 'Unexpected error in upsert_company_details: %', SQLERRM;
    RETURN jsonb_build_object(
        'inserted', inserted_count,
        'updated', updated_count,
        'skipped', skipped_count,
        'errors', error_count + 1,
        'total_processed', total_count,
        'success', false,
        'message', 'Function failed: ' || SQLERRM
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION upsert_company_details(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION upsert_company_details(JSONB) TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION upsert_company_details(JSONB) IS 
'Efficiently upserts company details with batch processing support. 
Handles large datasets while avoiding statement timeouts.
Returns statistics about inserted, updated, skipped, and error records.';
