# Master Corporate Announcements System Documentation

## Overview
The Master Corporate Announcements System is a unified data consolidation solution that combines corporate announcements from both NSE and BSE exchanges into a single, comprehensive table. This system provides deduplication, data merging, and automatic synchronization capabilities.

## Table Structure

### Master Table: `master_corporate_announcements`

#### Primary Keys and Identifiers
- `id` (UUID) - Primary key, auto-generated
- `pdf_hash` (VARCHAR) - Unique deduplication key across exchanges
- `nse_source_id` (BIGINT) - Reference to original NSE record
- `bse_source_id` (UUID) - Reference to original BSE record

#### Source Tracking
- `source_exchange` (VARCHAR) - Primary source ('NSE' or 'BSE')
- `has_nse_data` (BOOLEAN) - Indicates NSE data presence
- `has_bse_data` (BOOLEAN) - Indicates BSE data presence

#### Company Information
- `company_name` (TEXT) - Unified company name
- `nse_symbol` (TEXT) - NSE trading symbol
- `bse_code` (TEXT) - BSE security code

#### Content Fields
- `subject` (TEXT) - Primary announcement subject
- `headline` (TEXT) - Announcement headline
- `details` (TEXT) - Detailed announcement content
- `category` (TEXT) - BSE category classification
- `subcategory` (TEXT) - BSE subcategory
- `classification` (TEXT) - BSE parsed classification
- `parsed_subject` (TEXT) - BSE parsed subject

#### Timing Information
- `announcement_date` (TIMESTAMPTZ) - Primary announcement date
- `submission_date` (TIMESTAMPTZ) - News submission date
- `dissemination_date` (TIMESTAMPTZ) - Dissemination timestamp
- `broadcast_date` (TIMESTAMPTZ) - NSE broadcast time
- `receipt_date` (TIMESTAMPTZ) - NSE receipt time
- `time_difference` (TEXT) - Time difference information
- `difference_seconds` (INTEGER) - NSE time difference in seconds

#### Attachment Information
- `attachment_url` (TEXT) - Primary attachment URL
- `attachment_file` (TEXT) - BSE attachment file path
- `file_size_bytes` (INTEGER) - File size in bytes
- `file_size_text` (TEXT) - NSE file size text
- `xml_data_url` (TEXT) - BSE XML data URL

#### Exchange-Specific Metadata
- `announcement_type` (TEXT) - BSE announcement type
- `file_status` (TEXT) - BSE file status
- `critical_news` (TEXT) - BSE critical news flag
- `nsurl` (TEXT) - BSE company URL

#### Data Quality
- `is_duplicate` (BOOLEAN) - Duplicate flag
- `duplicate_check_status` (TEXT) - Processing status
- `data_completeness_score` (DECIMAL) - Completeness score (0.00-1.00)

#### Audit Fields
- `created_at` (TIMESTAMPTZ) - Record creation time
- `updated_at` (TIMESTAMPTZ) - Last update time
- `scraped_at` (TIMESTAMPTZ) - Original scraping time
- `last_nse_update` (TIMESTAMPTZ) - Last NSE data update
- `last_bse_update` (TIMESTAMPTZ) - Last BSE data update

## Column Mapping

### BSE to Master Mapping
```
BSE Column                  → Master Column
id                         → bse_source_id
attachmentfile             → attachment_file
headline                   → headline
categoryname               → category
company_name               → company_name
news_submission_dt         → submission_date
dissemdt                   → dissemination_date
fld_attachsize             → file_size_bytes
xml_data                   → xml_data_url
classification             → classification
parsed_subject             → parsed_subject
pdf_hash                   → pdf_hash
subject                    → subject
subcatname                 → subcategory
bse_code                   → bse_code
announcement_type          → announcement_type
filestatus                 → file_status
criticalnews               → critical_news
nsurl                      → nsurl
more                       → details
timediff                   → time_difference
```

### NSE to Master Mapping
```
NSE Column                 → Master Column
id                         → nse_source_id
symbol                     → nse_symbol
company_name               → company_name
subject                    → subject
details                    → details
broadcast_date_time        → broadcast_date
dissemination              → dissemination_date
attachment_url             → attachment_url
file_size                  → file_size_text
pdf_hash                   → pdf_hash
receipt                    → receipt_date
difference_seconds         → difference_seconds
```

## Deduplication Logic

### Primary Key: `pdf_hash`
- Used as unique identifier across both exchanges
- Records with same `pdf_hash` are considered duplicates
- Only one record per `pdf_hash` exists in master table

### Merge Strategy
1. **New Record**: If `pdf_hash` doesn't exist, insert as new record
2. **Existing Record**: If `pdf_hash` exists, merge data using these rules:
   - Only update NULL or empty fields in existing record
   - Preserve existing non-empty data
   - Update source tracking flags (`has_nse_data`, `has_bse_data`)
   - Recalculate data completeness score
   - Update timestamp fields

### Processing Criteria
Records are only processed if:
- `is_duplicate = FALSE`
- `duplicate_check_status = 'completed'`
- `pdf_hash` is not NULL

## Data Completeness Scoring

### BSE Records
Important fields for completeness:
- `company_name`, `subject`, `headline`, `category`
- `announcement_date`, `attachment_file`, `bse_code`

### NSE Records
Important fields for completeness:
- `company_name`, `subject`, `nse_symbol`
- `announcement_date`, `attachment_url`, `details`

### Score Calculation
- Score = (Filled Important Fields) / (Total Important Fields)
- Range: 0.00 to 1.00
- Updated on every record insert/update

## Database Triggers

### BSE Trigger: `trigger_bse_to_master`
- Monitors: `bse_corporate_announcements` table
- Triggers on: INSERT or UPDATE of `is_duplicate`, `duplicate_check_status`
- Action: Sends notification when record is ready for processing

### NSE Trigger: `trigger_nse_to_master`
- Monitors: `nse_corporate_announcements` table
- Triggers on: INSERT or UPDATE of `is_duplicate`, `duplicate_check_status`
- Action: Sends notification when record is ready for processing

## Indexes

### Performance Indexes
- `idx_master_corp_ann_pdf_hash` - Primary deduplication key
- `idx_master_corp_ann_source_exchange` - Source filtering
- `idx_master_corp_ann_company_name` - Company searches
- `idx_master_corp_ann_nse_symbol` - NSE symbol lookups
- `idx_master_corp_ann_bse_code` - BSE code lookups
- `idx_master_corp_ann_category` - Category filtering

### Composite Indexes
- `idx_master_corp_ann_source_tracking` - Multi-source queries
- `idx_master_corp_ann_duplicate_status` - Processing status queries

## Usage Examples

### Query All Records from Both Exchanges
```sql
SELECT * FROM master_corporate_announcements 
WHERE has_nse_data = true AND has_bse_data = true;
```

### Find Records by Company
```sql
SELECT * FROM master_corporate_announcements 
WHERE company_name ILIKE '%Reliance%';
```

### Get High-Quality Records
```sql
SELECT * FROM master_corporate_announcements 
WHERE data_completeness_score >= 0.8;
```

### Source-Specific Queries
```sql
-- NSE-only records
SELECT * FROM master_corporate_announcements 
WHERE source_exchange = 'NSE' AND has_bse_data = false;

-- BSE-only records  
SELECT * FROM master_corporate_announcements 
WHERE source_exchange = 'BSE' AND has_nse_data = false;
```

## Implementation Steps

1. **Table Creation**: Create master table with all required columns
2. **Index Creation**: Add performance indexes
3. **Trigger Setup**: Create synchronization triggers
4. **Data Migration**: Migrate existing data from source tables
5. **Testing**: Validate deduplication and merge logic
6. **Monitoring**: Set up ongoing synchronization monitoring

## Benefits

1. **Unified View**: Single table for all corporate announcements
2. **Deduplication**: Automatic handling of cross-exchange duplicates
3. **Data Enrichment**: Merge complementary data from both sources
4. **Performance**: Optimized indexes for fast queries
5. **Data Quality**: Completeness scoring and tracking
6. **Audit Trail**: Complete lineage back to source tables
7. **Real-time Sync**: Automatic updates via triggers

## Maintenance

### Regular Tasks
- Monitor data completeness scores
- Check for processing errors in triggers
- Validate deduplication accuracy
- Update indexes as data grows

### Troubleshooting
- Check trigger function logs for errors
- Verify source table data quality
- Monitor master table growth patterns
- Validate merge logic accuracy
