# PDF Duplicate Detection System

A comprehensive system for detecting duplicate PDF attachments in corporate announcements using Supabase Edge Functions.

## 🎯 Overview

This system automatically detects duplicate PDF files across BSE and NSE corporate announcements tables using SHA-256 hashing. It integrates seamlessly with existing scrapers and provides real-time duplicate detection.

## 🏗️ Architecture

### Components

1. **Database Schema**: Added columns for duplicate tracking
2. **Edge Functions**: Serverless functions for PDF processing
3. **Database Triggers**: Automatic duplicate detection on insert
4. **Integration Layer**: Modified existing scrapers
5. **Management Tools**: Scripts for monitoring and maintenance

### Edge Functions

- **`pdf-duplicate-detector`**: Main function for processing individual records
- **`process-pending-duplicates`**: Batch processing for existing records
- **`get-duplicate-stats`**: Statistics and monitoring

## 📊 Database Schema Changes

### New Columns Added

Both `bse_corporate_announcements` and `nse_corporate_announcements` tables now include:

```sql
-- Duplicate detection columns
is_duplicate BOOLEAN DEFAULT FALSE
pdf_hash VARCHAR(64)
duplicate_check_status VARCHAR(20) DEFAULT 'pending'

-- Performance indexes
CREATE INDEX idx_bse_attachmentfile ON bse_corporate_announcements(attachmentfile);
CREATE INDEX idx_nse_attachment_url ON nse_corporate_announcements(attachment_url);
CREATE INDEX idx_bse_pdf_hash ON bse_corporate_announcements(pdf_hash);
CREATE INDEX idx_nse_pdf_hash ON nse_corporate_announcements(pdf_hash);
```

### Status Values

- `pending`: Record needs duplicate check
- `processing`: Currently being processed
- `completed`: Processing finished successfully
- `failed`: Processing failed (will retry)

## 🚀 How It Works

### 1. Automatic Detection (New Records)

When a new record is inserted:

1. **Database Trigger** sets `duplicate_check_status = 'pending'`
2. **Edge Function** is called automatically (via your trigger setup)
3. **PDF Download** and SHA-256 hash computation
4. **Same-table Check** for existing records with same hash
5. **Update Records** with duplicate flags and hash

### 2. Batch Processing (Existing Records)

For existing records without hash:

```bash
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY process --batch-size 50
```

### 3. Statistics and Monitoring

```bash
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY stats
```

## 🔧 Integration with Existing Scrapers

### BSE Corporate Announcements

The `bse_corporate_announcements/database_manager.py` now:

- Adds duplicate detection columns to new records
- Provides methods to trigger duplicate detection
- Includes duplicate statistics in output

### NSE Corporate Announcements

The `nse_corporate_announcements/database_manager.py` now:

- Adds duplicate detection columns to new records
- Provides methods to trigger duplicate detection
- Includes duplicate statistics in output

### Backward Compatibility

- ✅ Existing scrapers work without modification
- ✅ New columns have sensible defaults
- ✅ Optional duplicate detection features

## 📋 Usage Examples

### Check Statistics

```bash
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY stats
```

Output:
```
📊 PDF DUPLICATE DETECTION STATISTICS
================================================================================
📅 Generated: 2025-07-16T10:30:00.000Z

📋 BSE Corporate Announcements:
   Total records: 100
   Records with PDFs: 95
   Duplicate records: 12
   Unique PDFs: 83
   Duplicate percentage: 12.63%

📋 NSE Corporate Announcements:
   Total records: 20
   Records with PDFs: 18
   Duplicate records: 3
   Unique PDFs: 15
   Duplicate percentage: 16.67%

📊 Table Summary:
   Total unique PDFs (combined): 98
   BSE unique PDFs: 83
   NSE unique PDFs: 15

⚙️ Processing Status:
   Pending: 5
   Processing: 0
   Completed: 108
   Failed: 0
```

### Process Pending Records

```bash
# Process all pending records
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY process

# Process with custom settings
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY process \
  --batch-size 25 \
  --max-concurrent 3 \
  --table-filter bse_corporate_announcements
```

### Check Single Record

```bash
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY check \
  --record-id "70a9c0b2-a030-4149-927f-99e6a21ed43f" \
  --table "bse_corporate_announcements" \
  --pdf-url "https://www.bseindia.com/xml-data/corpfiling/AttachLive/30e825bf-ce6a-492a-ba90-a185b4335e0a.pdf"
```

## 🔍 Duplicate Detection Logic

### Hash-Based Detection

1. **Download PDF** from URL with retry logic
2. **Compute SHA-256** hash of entire file content
3. **Same-table Search** for existing records with same hash
4. **Mark Duplicates** based on chronological order (first occurrence = original)

### URL Validation

- Validates PDF URL format
- Filters out null/empty values
- Handles various PDF URL patterns

### Error Handling

- Retry logic for failed downloads
- Timeout handling (45 seconds per PDF)
- Graceful degradation on failures

## 📈 Performance Considerations

### Optimizations

- **Indexed Columns**: Fast lookups on PDF URLs and hashes
- **Batch Processing**: Configurable concurrency limits
- **Streaming Downloads**: Memory-efficient PDF processing
- **Async Processing**: Non-blocking duplicate detection

### Scalability

- **Edge Functions**: Auto-scaling serverless execution
- **Database Indexes**: Optimized query performance
- **Configurable Batching**: Adjustable processing rates

## 🛠️ Maintenance

### Regular Tasks

1. **Monitor Statistics**: Check duplicate rates and processing status
2. **Process Pending**: Handle any failed or pending records
3. **Review Failures**: Investigate failed duplicate checks

### Troubleshooting

```bash
# Check for failed records
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY stats

# Reprocess failed records (they'll be retried automatically)
python scripts/manage_pdf_duplicates.py --supabase-key YOUR_KEY process
```

## 🔐 Security

- Uses Supabase service role key for Edge Functions
- Validates input parameters
- Implements proper error handling
- No sensitive data in logs

## 📝 Configuration

### Environment Variables

Edge Functions use these environment variables:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for database access

### Customization

- Adjust batch sizes in processing scripts
- Modify timeout values in Edge Functions
- Configure retry logic parameters

## 🚨 Monitoring

### Key Metrics

- Duplicate detection rate
- Processing success rate
- Average processing time per PDF
- Failed detection count

### Alerts

Monitor for:
- High failure rates
- Long processing times
- Large numbers of pending records

## 🔄 Future Enhancements

- **Content-based similarity**: Beyond exact hash matching
- **Automated cleanup**: Remove duplicate records
- **Performance analytics**: Detailed processing metrics
- **API endpoints**: REST API for duplicate management

---

## 📞 Support

For issues or questions:
1. Check the statistics for system health
2. Review Edge Function logs in Supabase dashboard
3. Use the management script for troubleshooting
