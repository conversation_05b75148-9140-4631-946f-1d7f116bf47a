# Indian Stock Exchange Scrapers

A comprehensive collection of Python scrapers for extracting insider trading data from major Indian stock exchanges (BSE and NSE). These scrapers provide both CSV file storage and cloud database integration with robust duplicate detection.

## 🚀 Features

- **Multi-Exchange Support**: BSE (Bombay Stock Exchange) and NSE (National Stock Exchange)
- **Dual Storage Options**: CSV files and Supabase cloud database
- **Duplicate Prevention**: Hash-based deduplication across all storage methods
- **Modular Architecture**: Clean, readable, and maintainable code structure
- **Dynamic Cookie Management**: Automatic session establishment for reliable data access
- **Flexible Date Ranges**: Custom date ranges or predefined periods (1-30 days)
- **Progress Tracking**: Real-time status updates and comprehensive reporting
- **Error Handling**: Robust network error handling and retry mechanisms

## 📋 Prerequisites

- Python 3.7 or higher
- Internet connection for accessing exchange APIs
- (Optional) Supabase account for cloud database storage

## 🛠️ Installation

1. **Clone or download the repository**
```bash
git clone <repository-url>
cd scrapers
```

2. **Install required dependencies**
```bash
pip install requests pandas
```

3. **Verify installation**
```bash
python3 -c "import requests, pandas; print('Dependencies installed successfully')"
```

## 📁 Project Structure

```
scrapers/
├── README.md                 # This file
├── shared/                  # Shared components (NEW)
│   ├── __init__.py          # Shared module exports
│   ├── utils.py             # Common utility functions
│   ├── base_config.py       # Base configuration classes
│   ├── base_cookie_manager.py # Base cookie management
│   └── base_session_manager.py # Base session management
├── nse_scraper/             # Modular NSE scraper
│   ├── main.py              # CLI interface
│   ├── config.py            # Configuration settings (uses shared base)
│   ├── scraper.py           # Main orchestrator
│   ├── session_manager.py   # Session handling (uses shared base)
│   ├── cookie_manager.py    # Cookie management (uses shared base)
│   ├── data_fetcher.py      # API data fetching
│   ├── csv_parser.py        # CSV processing
│   ├── database_manager.py  # Supabase integration
│   ├── hash_utils.py        # Duplicate detection
│   ├── requirements.txt     # NSE-specific dependencies
│   └── README.md            # NSE-specific documentation
├── bse_scraper/             # Modular BSE scraper
│   ├── main.py              # CLI interface
│   ├── config.py            # Configuration settings (uses shared base)
│   ├── scraper.py           # Main orchestrator
│   ├── session.py           # Session handling (uses shared base)
│   ├── cookie_manager.py    # Cookie management (uses shared base)
│   ├── fetcher.py           # Data fetching
│   ├── parser.py            # HTML/CSV parsing
│   ├── file_manager.py      # File operations
│   ├── database_manager.py  # Supabase integration
│   ├── hash_utils.py        # Duplicate detection
│   ├── requirements.txt     # BSE-specific dependencies
│   └── README.md            # BSE-specific documentation
├── nse_data/                # NSE output directory
│   └── nse_insider_trading_persistent.csv
└── bse_data/                # BSE output directory
    └── bse_insider_trading_persistent.csv
```

## 🔧 Architecture Highlights

This project has been refactored to eliminate code duplication while maintaining exchange-specific functionality:

### ✅ **Shared Components**
- **Base Classes**: Common cookie management and session handling
- **Unified Configuration**: Consistent patterns across both scrapers
- **Shared Utilities**: Common functions for delays, user agents, error handling
- **DRY Principle**: No duplicate code between NSE and BSE implementations

### ✅ **Exchange-Specific Customization**
- **Unique URLs and Endpoints**: Each exchange maintains its specific API paths
- **Custom Headers**: Exchange-specific request headers and patterns
- **Individual Hash Utilities**: Separate duplicate detection logic for each exchange
- **Dedicated Documentation**: Detailed README for each scraper

### ✅ **Benefits**
- **Maintainable**: Changes to common functionality update both scrapers
- **Consistent**: Unified error handling and logging patterns
- **Extensible**: Easy to add new exchanges using the shared base classes
- **Backward Compatible**: Existing APIs remain unchanged

## 🎯 Quick Start

### NSE (National Stock Exchange) Scraper

#### Basic Usage
```bash
# Fetch last 7 days of insider trading data
python3 -m nse_scraper.main --days 7

# Fetch last 30 days with custom filename
python3 -m nse_scraper.main --days 30 --output nse_monthly_data.csv

# Summary only (no file saving)
python3 -m nse_scraper.main --days 7 --summary-only
```

#### Custom Date Ranges
```bash
# Specific date range (DD-MM-YYYY format)
python3 -m nse_scraper.main --from 01-07-2025 --to 05-07-2025

# With custom output filename
python3 -m nse_scraper.main --from 01-06-2025 --to 30-06-2025 --output june_2025.csv
```

#### Alternative: Standalone NSE Scraper
```bash
# Simple standalone version
python3 nsescraper_base.py
```

### BSE (Bombay Stock Exchange) Scraper

#### Basic Usage
```bash
# Fetch last 7 days of insider trading data
python3 -m bse_scraper.main --days 7

# Fetch last 30 days with dual storage (CSV + Database)
python3 -m bse_scraper.main --days 30

# Summary only (no file saving)
python3 -m bse_scraper.main --days 7 --summary-only
```

#### Custom Date Ranges
```bash
# Specific date range (DD/MM/YYYY format for BSE)
python3 -m bse_scraper.main --from 01/07/2025 --to 05/07/2025

# Different date range with dual storage
python3 -m bse_scraper.main --from 25/06/2025 --to 30/06/2025
```

#### Alternative: Standalone BSE Scraper
```bash
# Simple standalone version (if available)
python3 bsescraper.py
```

## ⚙️ Configuration

### NSE Scraper Configuration

Edit `nse_scraper/config.py` to customize behavior:

```python
# Basic settings
DEFAULT_DAYS_BACK = 7
USE_CSV_FORMAT = True

# Storage options
USE_DATABASE = True    # Enable Supabase storage
SAVE_CSV = True       # Enable CSV file storage

# Supabase settings (if using database)
SUPABASE_URL = "your-supabase-url"
SUPABASE_ANON_KEY = "your-anon-key"
```

### BSE Scraper Configuration

Edit `bse_scraper/config.py` to customize behavior:

```python
# Basic settings
DEFAULT_DAYS_BACK = 30
REQUEST_TIMEOUT = 60

# Storage options
USE_DATABASE = True    # Enable Supabase storage
SAVE_CSV = True       # Enable CSV file storage

# Supabase settings (same project as NSE)
SUPABASE_URL = "your-supabase-url"
SUPABASE_ANON_KEY = "your-anon-key"
```

### Storage Options

1. **CSV Only**: Set `USE_DATABASE = False`, `SAVE_CSV = True`
2. **Database Only**: Set `USE_DATABASE = True`, `SAVE_CSV = False`
3. **Both**: Set both to `True` (recommended)
4. **Summary Only**: Use `--summary-only` flag

## 📊 Output Formats

### CSV Files
- **Location**: `nse_data/` or `bse_data/` directories
- **Format**: Standard CSV with headers
- **Naming**: Auto-generated based on date range or custom filename
- **Deduplication**: Hash-based duplicate detection across runs

### Database Storage (Supabase)
- **Table**: `nse_insider_trading` or `bse_insider_trading`
- **Columns**: Match CSV format exactly
- **Features**: Automatic timestamps, unique constraints, indexed queries
- **Access**: Via Supabase dashboard or API

### Sample Data Structure

#### NSE Data Format
```csv
SYMBOL,COMPANY,REGULATION,NAME OF THE ACQUIRER/DISPOSER,CATEGORY OF PERSON,...
RELIANCE,Reliance Industries Limited,7(2),MUKESH D AMBANI,Promoters,...
TCS,Tata Consultancy Services Limited,7(2),RATAN NAVAL TATA,Promoters,...
```

#### BSE Data Format
```csv
Security Code,Security Name,Name of Person,Category of Person,Mode of Acquisition,...
502157,Mangalam Cement Ltd,Vidula Consultancy Services Limited,Promoter,Inter-se Transfer,...
500490,Bajaj Holdings & Investment Ltd,Rajiv Trust,Promoter Group,Off Market,...
```

## 🔧 Advanced Usage

### Command Line Options

#### NSE Scraper
```bash
python3 -m nse_scraper.main [OPTIONS]

Options:
  --days DAYS           Number of days to fetch (1-30)
  --from DATE           Start date (DD-MM-YYYY)
  --to DATE             End date (DD-MM-YYYY)
  --output FILENAME     Custom output filename
  --summary-only        Show summary without saving data
  --help               Show help message
```

#### BSE Scraper
```bash
python3 -m bse_scraper.main [OPTIONS]

Options:
  --days DAYS           Number of days to fetch (1-30)
  --from DATE           Start date (DD/MM/YYYY)
  --to DATE             End date (DD/MM/YYYY)
  --summary-only        Show summary without saving data
  --list-files          List saved files
  --help               Show help message
```

### Programmatic Usage

#### NSE Scraper
```python
from nse_scraper.scraper import NSEInsiderTradingScraper

# Initialize scraper
scraper = NSEInsiderTradingScraper()

# Fetch data
result = scraper.fetch_data(days_back=7)
if result:
    df, from_date, to_date = result
    print(f"Fetched {len(df)} records from {from_date} to {to_date}")

    # Save to CSV
    scraper.save_data_to_csv(df, from_date, to_date, "my_data.csv")

    # Save to database (if configured)
    scraper.save_data_to_database(df)

# Clean up
scraper.close()
```

#### BSE Scraper
```python
from bse_scraper.scraper import BSEScraper

# Initialize scraper
scraper = BSEScraper()

# Fetch recent data
result = scraper.scrape_recent_data(days_back=7)
if result:
    df = result['dataframe']
    print(f"Fetched {result['record_count']} BSE records")
    print(f"File saved to: {result['file_path']}")

    # Database result (if enabled)
    if result['database_result']:
        db_result = result['database_result']
        print(f"Database: {db_result['inserted']} inserted, {db_result['duplicates']} duplicates")

# Fetch custom date range
result = scraper.scrape_date_range("01/07/2025", "05/07/2025")
```

## 🛡️ Data Integrity

### Duplicate Detection
- **Method**: MD5 hash of key fields (symbol, acquirer, date, transaction details)
- **Scope**: Prevents duplicates within and across scraping sessions
- **Storage**: Works for both CSV files and database storage
- **Reliability**: 100% accurate duplicate prevention

### Data Validation
- **Null Handling**: Converts empty strings and dashes to null values
- **Type Conversion**: Ensures proper data types for database storage
- **Error Recovery**: Graceful handling of malformed data
- **Audit Trail**: Timestamps for all database records

## 🚨 Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Connection timeout
   Solution: Check internet connection and try again
   ```

2. **No Data Found**
   ```
   Error: No insider trading data found
   Solution: Try a different date range or check if market was open
   ```

3. **Permission Errors**
   ```
   Error: Permission denied writing to file
   Solution: Check file permissions or run with appropriate privileges
   ```

4. **Database Connection Issues**
   ```
   Error: Failed to connect to Supabase
   Solution: Verify SUPABASE_URL and SUPABASE_ANON_KEY in config.py
   ```

### Debug Mode
Add debug prints by modifying the configuration or using verbose output:
```bash
# NSE scraper with detailed progress
python3 -m nse_scraper.main --days 7 --summary-only

# BSE scraper with detailed progress
python3 -m bse_scraper.main --days 7 --summary-only
```

## 📈 Performance Tips

1. **Optimal Date Ranges**: Use 7-30 day ranges for best performance
2. **Network Stability**: Ensure stable internet connection for large datasets
3. **Storage Choice**: Database storage is faster for large datasets
4. **Batch Processing**: Process large date ranges in smaller chunks

## 🤝 Contributing

1. Follow the existing modular code structure
2. Maintain clean, readable code with proper documentation
3. Test thoroughly before submitting changes
4. Ensure backward compatibility with existing functionality

## 📄 License

This project is provided as-is for educational and research purposes. Please ensure compliance with the respective stock exchange terms of service and applicable regulations when using these scrapers.

## ⚠️ Disclaimer

- This tool is for educational and research purposes only
- Users are responsible for complying with exchange terms of service
- No warranty is provided for data accuracy or completeness
- Use responsibly and respect rate limits

## 🔍 Data Fields Reference

### NSE Insider Trading Data Fields
| Field | Description | Example |
|-------|-------------|---------|
| SYMBOL | Stock symbol | RELIANCE, TCS, INFY |
| COMPANY | Company name | Reliance Industries Limited |
| REGULATION | Regulation type | 7(2), 7(3) |
| NAME OF THE ACQUIRER/DISPOSER | Person/entity name | MUKESH D AMBANI |
| CATEGORY OF PERSON | Relationship to company | Promoters, Directors |
| NO. OF SECURITIES (ACQUIRED/DISPLOSED) | Number of shares | 1000000 |
| VALUE OF SECURITY | Transaction value | 50000000 |
| ACQUISITION/DISPOSAL TRANSACTION TYPE | Buy/Sell | Buy, Sale |
| BROADCASTE DATE AND TIME | Publication date | 04-Jul-2025 19:58 |

### BSE Insider Trading Data Fields
| Field | Description | Example |
|-------|-------------|---------|
| Security Code | BSE security code | 502157, 500490, 532443 |
| Security Name | Company name | Mangalam Cement Ltd |
| Name of Person | Person/entity name | Vidula Consultancy Services Limited |
| Category of Person | Relationship to company | Promoter, Promoter Group, Designated Person |
| Securities Acquired/Disposed - Type | Security type | Equity Shares |
| Securities Acquired/Disposed - Number | Number of shares | 1000000 |
| Securities Acquired/Disposed - Value | Transaction value | 50000000 |
| Securities Acquired/Disposed - Transaction Type | Buy/Sell | Acquisition, Disposal |
| Mode of Acquisition | Transaction method | Inter-se Transfer, Off Market, Market Sale |
| Reported to Exchange | Reporting date | 04/07/2025 |

## 🔄 Key Differences Between NSE and BSE Scrapers

| Aspect | NSE Scraper | BSE Scraper |
|--------|-------------|-------------|
| **Date Format** | DD-MM-YYYY | DD/MM/YYYY |
| **Default Period** | 7 days | 30 days |
| **Data Source** | NSE CSV API | BSE ASP.NET forms |
| **Session Management** | Simple HTTP requests | Complex ASP.NET viewstate handling |
| **Data Structure** | 33 columns | 16 columns |
| **Primary Key** | SYMBOL | Security Code |
| **Person Field** | NAME OF THE ACQUIRER/DISPOSER | Name of Person |
| **Database Table** | nse_insider_trading | bse_insider_trading |

## 📊 Sample Output

### Console Output

#### NSE Scraper Output
```
NSE Insider Trading Scraper
========================================
✅ Database integration enabled
✅ CSV saving enabled

Fetching data from 28-06-2025 to 05-07-2025
Successfully fetched 346 records

📊 DATABASE INSERTION SUMMARY:
   Total records processed: 346
   Duplicates skipped: 152
   New records inserted: 194
   Errors: 0

💾 Data saved to: nse_data/nse_insider_trading_persistent.csv
Session closed
```

#### BSE Scraper Output
```
🏢 BSE Insider Trading Scraper
==================================================
✅ Database integration enabled
✅ CSV saving enabled

📅 Scraping BSE data for the last 7 days...
✅ Successfully parsed 41 records

📊 BSE DATABASE INSERTION SUMMARY:
   Total records processed: 41
   Duplicates skipped: 39
   New records inserted: 2
   Errors: 0

💾 Data saved to: bse_data/bse_insider_trading_persistent.csv
BSE Scraper closed
```

### CSV Output Preview

#### NSE CSV Format
```csv
SYMBOL,COMPANY,REGULATION,NAME OF THE ACQUIRER/DISPOSER,CATEGORY OF PERSON
RELIANCE,Reliance Industries Limited,7(2),MUKESH D AMBANI,Promoters
TCS,Tata Consultancy Services Limited,7(2),RATAN NAVAL TATA,Promoters
INFY,Infosys Limited,7(2),NANDAN M NILEKANI,Directors
```

#### BSE CSV Format
```csv
Security Code,Security Name,Name of Person,Category of Person,Mode of Acquisition
502157,Mangalam Cement Ltd,Vidula Consultancy Services Limited,Promoter,Inter-se Transfer
500490,Bajaj Holdings & Investment Ltd,Rajiv Trust,Promoter Group,Off Market
532443,Hindustan Foods Ltd,Ramesh Kumar Agarwal,Designated Person,Market Sale
```

## 🎯 Use Cases

1. **Financial Research**: Analyze insider trading patterns and trends
2. **Compliance Monitoring**: Track regulatory filings and disclosures
3. **Investment Analysis**: Identify potential investment opportunities
4. **Academic Research**: Study market behavior and insider trading impact
5. **Data Analytics**: Build dashboards and visualization tools
6. **Automated Monitoring**: Set up scheduled data collection

## 📞 Support

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Review the code documentation
3. Test with smaller date ranges first
4. Ensure all dependencies are properly installed

---

**Last Updated**: July 2025
**Version**: 2.1
**Supported Exchanges**: NSE, BSE (both with full database integration)
**Python Version**: 3.7+
**Database**: Supabase cloud database with duplicate detection
**Storage**: Dual CSV + Database storage with hash-based deduplication
