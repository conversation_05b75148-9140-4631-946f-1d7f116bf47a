#!/usr/bin/env python3
"""
Script to add missing columns to master_corporate_announcements table
"""

import requests
import time

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def add_column_via_supabase_api(column_name, column_type):
    """Add a column using Supabase API"""
    try:
        query = f"ALTER TABLE master_corporate_announcements ADD COLUMN IF NOT EXISTS {column_name} {column_type};"
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
            headers=headers,
            json={"sql": query},
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            print(f"✅ Added column: {column_name}")
            return True
        else:
            print(f"❌ Failed to add {column_name}: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding {column_name}: {e}")
        return False

def main():
    """Add all missing columns"""
    
    print("🔧 Adding missing columns to master_corporate_announcements table...")
    
    # Define columns to add
    columns_to_add = [
        ("announcement_date", "TIMESTAMPTZ"),
        ("submission_date", "TIMESTAMPTZ"),
        ("dissemination_date", "TIMESTAMPTZ"),
        ("broadcast_date", "TIMESTAMPTZ"),
        ("receipt_date", "TIMESTAMPTZ"),
        ("time_difference", "TEXT"),
        ("difference_seconds", "INTEGER"),
        ("attachment_url", "TEXT"),
        ("attachment_file", "TEXT"),
        ("file_size_bytes", "INTEGER"),
        ("file_size_text", "TEXT"),
        ("xml_data_url", "TEXT"),
        ("announcement_type", "TEXT"),
        ("file_status", "TEXT"),
        ("critical_news", "TEXT"),
        ("nsurl", "TEXT"),
        ("is_duplicate", "BOOLEAN DEFAULT FALSE"),
        ("duplicate_check_status", "TEXT DEFAULT 'pending'"),
        ("data_completeness_score", "DECIMAL(3,2)"),
        ("scraped_at", "TIMESTAMPTZ"),
        ("last_nse_update", "TIMESTAMPTZ"),
        ("last_bse_update", "TIMESTAMPTZ")
    ]
    
    success_count = 0
    
    for column_name, column_type in columns_to_add:
        print(f"Adding {column_name}...")
        if add_column_via_supabase_api(column_name, column_type):
            success_count += 1
        time.sleep(1)  # Small delay between requests
    
    print(f"\n✅ Successfully added {success_count}/{len(columns_to_add)} columns")
    
    if success_count == len(columns_to_add):
        print("🎉 All columns added successfully!")
    else:
        print("⚠️ Some columns may already exist or failed to add")

if __name__ == "__main__":
    main()
