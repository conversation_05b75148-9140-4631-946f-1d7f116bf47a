# BSE Corporate Announcements Scraper

A clean, modular scraper for fetching corporate announcements data from BSE (Bombay Stock Exchange) API.

## Features

- **Simple API**: Easy-to-use Python interface
- **Command Line Interface**: Full CLI with comprehensive options
- **Modular Design**: Clean, maintainable code structure
- **Proxy Support**: Built-in ScraperAPI proxy integration
- **Data Export**: CSV file output with organized directory structure
- **Database Integration**: Optional Supabase database storage
- **Error Handling**: Robust error handling and retry mechanisms
- **Duplicate Detection**: Automatic duplicate record detection

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Configure proxy settings in `proxy_config.py` (optional but recommended)

## Usage

### Python API

```python
from bse_corporate_announcements import BSECorporateAnnouncementsScraper

# Basic usage - scrape last 7 days
with BSECorporateAnnouncementsScraper() as scraper:
    result = scraper.scrape_recent_data(days_back=7)
    if result:
        print(f"Scraped {result['record_count']} records")

# Custom date range
with BSECorporateAnnouncementsScraper() as scraper:
    result = scraper.scrape_date_range("01/01/2025", "07/01/2025")
    if result:
        print(f"Scraped {result['record_count']} records")
```

### Command Line Interface

```bash
# Scrape last 7 days
python -m bse_corporate_announcements.main --days 7

# Scrape specific date range
python -m bse_corporate_announcements.main --from 01/01/2025 --to 07/01/2025

# Test API connection
python -m bse_corporate_announcements.main --test-connection

# Show scraper information
python -m bse_corporate_announcements.main --info

# Clean up old files
python -m bse_corporate_announcements.main --cleanup 5
```

### Advanced Options

```bash
# Disable CSV output
python -m bse_corporate_announcements.main --days 7 --no-csv

# Disable database storage
python -m bse_corporate_announcements.main --days 7 --no-database

# Custom output directory
python -m bse_corporate_announcements.main --days 7 --output-dir /path/to/output

# Verbose output
python -m bse_corporate_announcements.main --days 7 --verbose

# Quiet mode
python -m bse_corporate_announcements.main --days 7 --quiet
```

## Configuration

The scraper uses configuration from:
- `config.py` - Main configuration settings
- `proxy_config.py` - Proxy configuration (shared with other scrapers)
- Environment variables for Supabase credentials

### Key Configuration Options

- `DEFAULT_DAYS_BACK`: Default number of days to scrape (7)
- `OUTPUT_FOLDER`: Directory for CSV files
- `SAVE_CSV`: Enable/disable CSV output
- `USE_DATABASE`: Enable/disable database storage
- `REQUEST_TIMEOUT`: API request timeout in seconds

## API Endpoint

The scraper uses the BSE Corporate Announcements API:
- Base URL: `https://api.bseindia.com`
- Endpoint: `/BseIndiaAPI/api/AnnSubCategoryGetData/w`

### API Parameters

- `pageno`: Page number for pagination
- `strCat`: Category filter (-1 for all)
- `strPrevDate`: Start date (YYYYMMDD format)
- `strToDate`: End date (YYYYMMDD format)
- `strType`: Type filter (C for corporate announcements)
- `strSearch`: Search parameter (P)
- `strScrip`: Company scrip filter (empty for all)
- `subcategory`: Subcategory filter (-1 for all)

## Output

### CSV Files
- Saved to `bse_corporate_announcements_data/` directory
- Filename format: `bse_corporate_announcements_YYYYMMDD_HHMMSS.csv`
- Includes all announcement data with standardized column names

### Database Storage
- Optional Supabase integration
- Table: `bse_corporate_announcements`
- Automatic duplicate detection using record hashes
- Metadata columns: `created_at`, `updated_at`, `record_hash`

## Error Handling

The scraper includes comprehensive error handling:
- Network connectivity issues
- API rate limiting
- Invalid date ranges
- File system errors
- Database connection problems

## Dependencies

- `requests`: HTTP requests and session management
- `pandas`: Data manipulation and CSV export
- `beautifulsoup4`: HTML parsing (if needed)
- `urllib3`: HTTP utilities
- `python-dateutil`: Date parsing utilities

## Architecture

The scraper follows a modular architecture:

- `scraper.py`: Main orchestrator class
- `session.py`: HTTP session management with proxy support
- `fetcher.py`: API data fetching logic
- `parser.py`: JSON data parsing and cleaning
- `file_manager.py`: File operations and CSV export
- `database_manager.py`: Database operations
- `config.py`: Configuration management
- `cli_parser.py`: Command line argument parsing
- `cli_commands.py`: CLI command execution
- `main.py`: Entry point for CLI

## License

This project is part of the BSE/NSE scrapers collection.
