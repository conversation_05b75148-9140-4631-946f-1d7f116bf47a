"""
Command Line Interface Parser for BSE Corporate Announcements Scraper
Handles parsing of command line arguments.
"""

import argparse
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .config import DEFAULT_DAYS_BACK
except ImportError:
    # Handle direct execution
    from config import DEFAULT_DAYS_BACK


def parse_arguments():
    """
    Parse command line arguments for BSE Corporate Announcements scraper.
    
    Returns:
        argparse.Namespace: Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="BSE Corporate Announcements Scraper - Fetch corporate announcements data from BSE",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Scrape last 7 days of data
  python -m bse_corporate_announcements.main --days 7
  
  # Scrape specific date range
  python -m bse_corporate_announcements.main --from 01/01/2025 --to 07/01/2025
  
  # Scrape with custom output settings
  python -m bse_corporate_announcements.main --days 30 --no-csv --no-database
  
  # Test connection only
  python -m bse_corporate_announcements.main --test-connection
  
  # Get scraper information
  python -m bse_corporate_announcements.main --info
        """
    )

    # Main operation modes
    operation_group = parser.add_mutually_exclusive_group()
    
    operation_group.add_argument(
        '--test-connection',
        action='store_true',
        help='Test connection to BSE Corporate Announcements API and exit'
    )
    
    operation_group.add_argument(
        '--info',
        action='store_true',
        help='Show scraper information and configuration'
    )
    
    operation_group.add_argument(
        '--cleanup',
        type=int,
        metavar='MAX_FILES',
        help='Clean up old files, keeping only MAX_FILES most recent files'
    )

    # Date range options
    date_group = parser.add_mutually_exclusive_group()
    
    date_group.add_argument(
        '--days',
        type=int,
        default=DEFAULT_DAYS_BACK,
        metavar='N',
        help=f'Number of days back to scrape (default: {DEFAULT_DAYS_BACK})'
    )
    
    date_range_group = date_group.add_argument_group('date_range')
    date_range_group.add_argument(
        '--from',
        dest='start_date',
        type=str,
        metavar='DD/MM/YYYY',
        help='Start date for scraping (format: DD/MM/YYYY)'
    )
    
    date_range_group.add_argument(
        '--to',
        dest='end_date',
        type=str,
        metavar='DD/MM/YYYY',
        help='End date for scraping (format: DD/MM/YYYY)'
    )

    # Output options
    output_group = parser.add_argument_group('output options')
    
    output_group.add_argument(
        '--no-csv',
        action='store_true',
        help='Disable CSV file output'
    )
    
    output_group.add_argument(
        '--no-database',
        action='store_true',
        help='Disable database storage'
    )
    
    output_group.add_argument(
        '--output-dir',
        type=str,
        metavar='DIR',
        help='Custom output directory for CSV files'
    )

    # API options
    api_group = parser.add_argument_group('API options')
    
    api_group.add_argument(
        '--max-pages',
        type=int,
        default=10,
        metavar='N',
        help='Maximum number of pages to fetch (default: 10)'
    )
    
    api_group.add_argument(
        '--timeout',
        type=int,
        default=30,
        metavar='SECONDS',
        help='Request timeout in seconds (default: 30)'
    )

    # Verbose output
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )
    
    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='Suppress non-essential output'
    )

    args = parser.parse_args()
    
    # Validate arguments
    validate_arguments(args)
    
    return args


def validate_arguments(args):
    """
    Validate parsed command line arguments.
    
    Args:
        args (argparse.Namespace): Parsed arguments
        
    Raises:
        argparse.ArgumentTypeError: If arguments are invalid
    """
    # Validate date range if provided
    if args.start_date or args.end_date:
        if not (args.start_date and args.end_date):
            raise argparse.ArgumentTypeError("Both --from and --to must be provided when using date range")
        
        try:
            start_date = datetime.strptime(args.start_date, '%d/%m/%Y')
            end_date = datetime.strptime(args.end_date, '%d/%m/%Y')
            
            if start_date > end_date:
                raise argparse.ArgumentTypeError("Start date must be before or equal to end date")
            
            # Check if date range is reasonable (not too far in the past or future)
            today = datetime.now()
            max_past_days = 365 * 2  # 2 years
            max_future_days = 30  # 30 days
            
            if start_date < today - timedelta(days=max_past_days):
                raise argparse.ArgumentTypeError(f"Start date is too far in the past (max {max_past_days} days)")
            
            if end_date > today + timedelta(days=max_future_days):
                raise argparse.ArgumentTypeError(f"End date is too far in the future (max {max_future_days} days)")
                
        except ValueError:
            raise argparse.ArgumentTypeError("Invalid date format. Use DD/MM/YYYY (e.g., 15/01/2025)")
    
    # Validate days back
    if args.days is not None:
        if args.days < 1:
            raise argparse.ArgumentTypeError("Days back must be at least 1")
        if args.days > 365:
            raise argparse.ArgumentTypeError("Days back cannot exceed 365")
    
    # Validate max pages
    if args.max_pages < 1:
        raise argparse.ArgumentTypeError("Max pages must be at least 1")
    if args.max_pages > 100:
        raise argparse.ArgumentTypeError("Max pages cannot exceed 100")
    
    # Validate timeout
    if args.timeout < 5:
        raise argparse.ArgumentTypeError("Timeout must be at least 5 seconds")
    if args.timeout > 300:
        raise argparse.ArgumentTypeError("Timeout cannot exceed 300 seconds")
    
    # Validate cleanup argument
    if args.cleanup is not None:
        if args.cleanup < 1:
            raise argparse.ArgumentTypeError("Max files for cleanup must be at least 1")
        if args.cleanup > 100:
            raise argparse.ArgumentTypeError("Max files for cleanup cannot exceed 100")
    
    # Check for conflicting quiet and verbose flags
    if args.quiet and args.verbose:
        raise argparse.ArgumentTypeError("Cannot use both --quiet and --verbose flags")


def get_operation_mode(args):
    """
    Determine the operation mode based on arguments.
    
    Args:
        args (argparse.Namespace): Parsed arguments
        
    Returns:
        str: Operation mode ('test', 'info', 'cleanup', 'scrape_days', 'scrape_range')
    """
    if args.test_connection:
        return 'test'
    elif args.info:
        return 'info'
    elif args.cleanup is not None:
        return 'cleanup'
    elif args.start_date and args.end_date:
        return 'scrape_range'
    else:
        return 'scrape_days'


def format_args_summary(args):
    """
    Format a summary of the parsed arguments for display.
    
    Args:
        args (argparse.Namespace): Parsed arguments
        
    Returns:
        str: Formatted summary
    """
    lines = []
    lines.append("Configuration Summary:")
    lines.append("-" * 30)
    
    operation = get_operation_mode(args)
    lines.append(f"Operation: {operation}")
    
    if operation == 'scrape_days':
        lines.append(f"Days back: {args.days}")
    elif operation == 'scrape_range':
        lines.append(f"Date range: {args.start_date} to {args.end_date}")
    elif operation == 'cleanup':
        lines.append(f"Keep files: {args.cleanup}")
    
    if operation in ['scrape_days', 'scrape_range']:
        lines.append(f"CSV output: {'disabled' if args.no_csv else 'enabled'}")
        lines.append(f"Database storage: {'disabled' if args.no_database else 'enabled'}")
        lines.append(f"Max pages: {args.max_pages}")
        lines.append(f"Timeout: {args.timeout}s")
        
        if args.output_dir:
            lines.append(f"Output directory: {args.output_dir}")
    
    if args.verbose:
        lines.append("Verbose output: enabled")
    elif args.quiet:
        lines.append("Quiet mode: enabled")
    
    return "\n".join(lines)
