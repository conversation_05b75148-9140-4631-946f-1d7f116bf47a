"""
Configuration settings for BSE Corporate Announcements Scraper
Contains all URLs, headers, and constants in one place.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import BaseConfig

class BSECorporateAnnouncementsConfig(BaseConfig):
    """Configuration class for BSE Corporate Announcements scraper"""

    # Abstract method implementations
    @property
    def BASE_URL(self):
        return "https://www.bseindia.com"

    @property
    def EXCHANGE_NAME(self):
        return "BSE Corporate Announcements"

    @property
    def OUTPUT_FOLDER(self):
        return "bse_corporate_announcements_data"

    @property
    def TABLE_NAME(self):
        return "bse_corporate_announcements"

    def __init__(self):
        super().__init__()

        # Additional settings
        self.API_BASE_URL = "https://api.bseindia.com"
        
        # Corporate announcements specific URLs
        self.CORPORATE_ANNOUNCEMENTS_API_URL = "/BseIndiaAPI/api/AnnSubCategoryGetData/w"
        
        # Date format settings
        self.BSE_DATE_FORMAT = "%Y%m%d"  # Format used in API: 20250714
        self.DISPLAY_DATE_FORMAT = "%d/%m/%Y"  # Format for display: 14/07/2025
        
        # Default parameters for API
        self.DEFAULT_CATEGORY = "-1"  # All categories
        self.DEFAULT_SUBCATEGORY = "-1"  # All subcategories
        self.DEFAULT_TYPE = "C"  # Corporate announcements
        self.DEFAULT_SEARCH = "P"  # Search parameter
        self.DEFAULT_SCRIP = ""  # Empty for all scrips
        self.DEFAULT_PAGE_SIZE = 100  # Records per page
        
        # File naming
        self.CSV_FILENAME_PREFIX = "bse_corporate_announcements"
        self.HTML_FILENAME_PREFIX = "bse_corporate_announcements_response"

        # Database settings
        self.DATABASE_TABLE = "bse_corporate_announcements"
        
        # Request settings
        self.REQUEST_TIMEOUT = 30
        self.MAX_RETRIES = 3
        self.RETRY_DELAY = 2
        
        # Default scraping settings
        self.DEFAULT_DAYS_BACK = 7
        
        # File settings
        self.SAVE_CSV = True
        self.USE_DATABASE = True
        self.CSV_EXTENSION = ".csv"
        self.HTML_EXTENSION = ".html"
        self.FILENAME_DATE_FORMAT = "%Y%m%d_%H%M%S"

# Create the configuration instance
config = BSECorporateAnnouncementsConfig()

# Export commonly used settings for backward compatibility
BASE_URL = config.BASE_URL
API_BASE_URL = config.API_BASE_URL
CORPORATE_ANNOUNCEMENTS_API_URL = config.CORPORATE_ANNOUNCEMENTS_API_URL
DEFAULT_DAYS_BACK = config.DEFAULT_DAYS_BACK
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
OUTPUT_FOLDER = config.OUTPUT_FOLDER
SUPABASE_URL = config.SUPABASE_URL
SUPABASE_ANON_KEY = config.SUPABASE_ANON_KEY
SAVE_CSV = config.SAVE_CSV
USE_DATABASE = config.USE_DATABASE
CSV_EXTENSION = config.CSV_EXTENSION
HTML_EXTENSION = config.HTML_EXTENSION
FILENAME_DATE_FORMAT = config.FILENAME_DATE_FORMAT

# BSE Corporate Announcements specific settings
BSE_DATE_FORMAT = config.BSE_DATE_FORMAT
DISPLAY_DATE_FORMAT = config.DISPLAY_DATE_FORMAT
DEFAULT_CATEGORY = config.DEFAULT_CATEGORY
DEFAULT_SUBCATEGORY = config.DEFAULT_SUBCATEGORY
DEFAULT_TYPE = config.DEFAULT_TYPE
DEFAULT_SEARCH = config.DEFAULT_SEARCH
DEFAULT_SCRIP = config.DEFAULT_SCRIP
DEFAULT_PAGE_SIZE = config.DEFAULT_PAGE_SIZE
DATABASE_TABLE = config.DATABASE_TABLE
CSV_FILENAME_PREFIX = config.CSV_FILENAME_PREFIX
HTML_FILENAME_PREFIX = config.HTML_FILENAME_PREFIX
