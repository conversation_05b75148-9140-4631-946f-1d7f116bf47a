"""
Example usage of BSE Corporate Announcements Scraper
Demonstrates various ways to use the scraper.
"""

import sys
import os

# Add the parent directory to the path to import the scraper
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bse_corporate_announcements import BSECorporateAnnouncementsScraper


def example_basic_usage():
    """Example: Basic usage - scrape last 7 days"""
    print("=" * 60)
    print("Example 1: Basic Usage - Last 7 Days")
    print("=" * 60)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            # Test connection first
            if not scraper.test_connection():
                print("❌ Connection test failed, skipping example")
                return
            
            # Scrape last 7 days
            result = scraper.scrape_recent_data(days_back=7)
            
            if result:
                print(f"✅ Successfully scraped {result['record_count']} records")
                print(f"📅 Date range: {result['start_date'].strftime('%d/%m/%Y')} to {result['end_date'].strftime('%d/%m/%Y')}")
                
                if result.get('files_saved'):
                    print(f"💾 Files saved: {len(result['files_saved'])}")
                    for file_path in result['files_saved']:
                        print(f"   - {file_path}")
                
                # Show sample data
                if 'dataframe' in result and not result['dataframe'].empty:
                    df = result['dataframe']
                    print(f"\n📊 Sample data (first 3 rows):")
                    print(df.head(3).to_string())
                    print(f"\n📋 Columns: {list(df.columns)}")
            else:
                print("❌ Scraping failed")
                
    except Exception as e:
        print(f"❌ Error in basic usage example: {e}")


def example_date_range():
    """Example: Scrape specific date range"""
    print("\n" + "=" * 60)
    print("Example 2: Date Range - Specific Dates")
    print("=" * 60)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            # Scrape specific date range
            result = scraper.scrape_date_range("10/07/2025", "14/07/2025")
            
            if result:
                print(f"✅ Successfully scraped {result['record_count']} records")
                print(f"📅 Date range: {result['start_date'].strftime('%d/%m/%Y')} to {result['end_date'].strftime('%d/%m/%Y')}")
                
                # Show data summary
                if 'summary' in result:
                    summary = result['summary']
                    print(f"📊 Data summary:")
                    print(f"   - Total records: {summary.get('total_records', 0)}")
                    print(f"   - Columns: {summary.get('column_count', 0)}")
                    print(f"   - Memory usage: {summary.get('memory_usage', 'N/A')}")
            else:
                print("❌ Date range scraping failed")
                
    except Exception as e:
        print(f"❌ Error in date range example: {e}")


def example_scraper_info():
    """Example: Get scraper information"""
    print("\n" + "=" * 60)
    print("Example 3: Scraper Information")
    print("=" * 60)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            info = scraper.get_scraper_info()
            
            print("🔧 Scraper Configuration:")
            print(f"   Name: {info['scraper_name']}")
            print(f"   Version: {info['version']}")
            print(f"   API Base URL: {info['api_base_url']}")
            print(f"   API Endpoint: {info['api_endpoint']}")
            print(f"   Output Folder: {info['output_folder']}")
            print(f"   CSV Enabled: {info['csv_enabled']}")
            print(f"   Database Enabled: {info['database_enabled']}")
            print(f"   Default Days Back: {info['default_days_back']}")
            
            if 'database_info' in info:
                db_info = info['database_info']
                print(f"\n🗄️ Database Configuration:")
                print(f"   Table: {db_info.get('table_name', 'N/A')}")
                print(f"   Status: {db_info.get('status', 'Unknown')}")
                
    except Exception as e:
        print(f"❌ Error getting scraper info: {e}")


def example_connection_test():
    """Example: Test API connection"""
    print("\n" + "=" * 60)
    print("Example 4: Connection Test")
    print("=" * 60)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            print("🔌 Testing BSE Corporate Announcements API connection...")
            
            success = scraper.test_connection()
            
            if success:
                print("✅ Connection test passed!")
                print("🎉 BSE Corporate Announcements API is accessible")
            else:
                print("❌ Connection test failed!")
                print("💡 Please check your internet connection and proxy settings")
                
    except Exception as e:
        print(f"❌ Error in connection test: {e}")


def example_file_management():
    """Example: File management operations"""
    print("\n" + "=" * 60)
    print("Example 5: File Management")
    print("=" * 60)
    
    try:
        with BSECorporateAnnouncementsScraper() as scraper:
            # Get latest data file
            latest_file = scraper.get_latest_data_file()
            if latest_file:
                print(f"📄 Latest data file: {latest_file}")
            else:
                print("📄 No data files found")
            
            # Clean up old files (keep 5 most recent)
            print("\n🧹 Cleaning up old files...")
            scraper.cleanup_old_files(max_files=5)
            print("✅ File cleanup completed")
            
    except Exception as e:
        print(f"❌ Error in file management example: {e}")


def main():
    """Run all examples"""
    print("🏢 BSE Corporate Announcements Scraper - Usage Examples")
    print("=" * 60)
    print("This script demonstrates various ways to use the scraper.")
    print("Make sure you have proper internet connection and proxy configuration.")
    print()
    
    # Run examples
    example_connection_test()
    example_scraper_info()
    example_basic_usage()
    example_date_range()
    example_file_management()
    
    print("\n" + "=" * 60)
    print("🎉 All examples completed!")
    print("💡 Check the output directory for saved CSV files.")
    print("💡 Use the CLI for more advanced options:")
    print("   python -m bse_corporate_announcements.main --help")


if __name__ == "__main__":
    main()
