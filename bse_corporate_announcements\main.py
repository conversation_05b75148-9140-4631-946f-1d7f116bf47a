"""
Command Line Interface for BSE Corporate Announcements Scraper
Provides easy-to-use command line access to BSE corporate announcements scraping functionality.
"""

import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .cli_parser import parse_arguments
    from .cli_commands import execute_command
except ImportError:
    # Handle direct execution
    from cli_parser import parse_arguments
    from cli_commands import execute_command


def main():
    """Main function for command line interface"""
    args = parse_arguments()
    execute_command(args)


if __name__ == "__main__":
    main()
