"""
HTTP Session Manager for BSE Corporate Announcements Scraper
Handles all HTTP requests with proper headers, retry strategy, and error handling.
"""

import sys
import os
import requests

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_session_manager import BaseSessionManager

try:
    from .config import config
except ImportError:
    # Handle direct execution
    from config import config


class SessionManager(BaseSessionManager):
    """
    BSE Corporate Announcements specific session manager.

    This class handles HTTP sessions for BSE corporate announcements API calls
    with proper headers and proxy configuration.
    """

    def __init__(self):
        """Initialize BSE Corporate Announcements session manager"""
        # Initialize session without calling parent __init__ to avoid cookie manager
        self.config = config
        self.session = requests.Session()
        self.setup_session()
        print(f"{config.EXCHANGE_NAME} Session manager initialized for API calls")

    def _create_cookie_manager(self):
        """Create cookie manager (not needed for API calls)"""
        return None

    def get_exchange_specific_headers(self):
        """
        Get BSE Corporate Announcements specific headers.
        Based on the curl command provided by the user.
        
        Returns:
            dict: BSE-specific HTTP headers
        """
        return {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Origin": "https://www.bseindia.com",
            "Priority": "u=1, i",
            "Referer": "https://www.bseindia.com/",
            "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
        }

    def _get_referer_url(self):
        """Get BSE corporate announcements referer URL"""
        return "https://www.bseindia.com/"

    def make_api_request(self, url, params=None, **kwargs):
        """
        Make an API request to BSE corporate announcements endpoint.
        Try direct request first, then fallback to proxy if needed.

        Args:
            url (str): The API URL to request
            params (dict): Query parameters for the request
            **kwargs: Additional arguments for the request

        Returns:
            requests.Response or None: Response object or None if failed
        """
        try:
            print(f"Making BSE Corporate Announcements API request to: {url}")

            # Try direct request first (BSE API works well without proxy)
            try:
                print("Trying direct API request...")
                response = self.session.get(url, params=params, **kwargs)

                if response and response.status_code == 200:
                    # Check if response is valid JSON
                    content_type = response.headers.get('Content-Type', '').lower()
                    if 'application/json' in content_type:
                        print("✅ Direct BSE Corporate Announcements API request successful")
                        return response
                    else:
                        print(f"⚠️ Direct request returned non-JSON content: {content_type}")
                        raise Exception("Non-JSON response")
                else:
                    print(f"Direct request failed with status: {response.status_code if response else 'None'}")
                    raise Exception("Direct request failed")

            except Exception as direct_error:
                print(f"Direct request failed: {direct_error}")
                print("Trying with proxy...")

                # Fallback to proxy request
                if hasattr(self, 'make_get_request'):
                    response = self.make_get_request(url, params=params, **kwargs)
                    if response and response.status_code == 200:
                        print("✅ Proxy BSE Corporate Announcements API request successful")
                        return response
                    else:
                        print(f"❌ Proxy request also failed with status: {response.status_code if response else 'None'}")
                        return None
                else:
                    print("❌ No proxy method available")
                    return None

        except Exception as e:
            print(f"Error making BSE Corporate Announcements API request: {e}")
            return None

    def test_connection(self):
        """
        Test connection to BSE corporate announcements API.
        
        Returns:
            bool: True if connection is successful, False otherwise
        """
        try:
            print("Testing BSE Corporate Announcements API connection...")
            
            # Test with a simple request to the base API URL
            test_url = f"{config.API_BASE_URL}{config.CORPORATE_ANNOUNCEMENTS_API_URL}"
            test_params = {
                "pageno": 1,
                "strCat": config.DEFAULT_CATEGORY,
                "strPrevDate": "20250714",  # Test date
                "strScrip": config.DEFAULT_SCRIP,
                "strSearch": config.DEFAULT_SEARCH,
                "strToDate": "20250714",  # Test date
                "strType": config.DEFAULT_TYPE,
                "subcategory": config.DEFAULT_SUBCATEGORY
            }
            
            response = self.make_api_request(test_url, params=test_params, timeout=10)
            
            if response:
                print("✅ BSE Corporate Announcements API connection test successful")
                return True
            else:
                print("❌ BSE Corporate Announcements API connection test failed")
                return False
                
        except Exception as e:
            print(f"Error testing BSE Corporate Announcements API connection: {e}")
            return False
