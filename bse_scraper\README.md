# BSE Scraper

A clean, modular BSE (Bombay Stock Exchange) insider trading scraper with simple, readable code.

## Features

- **Simple Code**: Easy to read and understand
- **Modular Design**: Each component has a single purpose
- **Clean Output**: All files saved in organized `bse_data` folder
- **Flexible Dates**: Recent days or custom date ranges
- **Error Handling**: Graceful handling of network and parsing errors
- **Multiple Interfaces**: Python API and command line

## Quick Start

### Python API
```python
from bse_scraper import BSEScraper

# Scrape last 7 days
with BSEScraper() as scraper:
    result = scraper.scrape_recent_data(days_back=7)
    if result:
        print(f"Found {result['record_count']} records")
        print(f"Saved to: {result['file_path']}")

# Custom date range (DD/MM/YYYY format)
with BSEScraper() as scraper:
    result = scraper.scrape_date_range("01/01/2025", "07/01/2025")
    if result:
        print(f"Found {result['record_count']} records")
```

### Command Line
```bash
# Last 7 days
python -m bse_scraper.main

# Last 30 days
python -m bse_scraper.main --days 30

# Custom date range
python -m bse_scraper.main --from 01/01/2025 --to 07/01/2025

# List saved files
python -m bse_scraper.main --list-files

# Custom output filename
python -m bse_scraper.main --output my_data.csv
```

## Package Structure

```
bse_scraper/
├── __init__.py          # Package exports
├── config.py            # Configuration settings
├── session.py           # HTTP session management
├── fetcher.py           # Data fetching from BSE
├── parser.py            # HTML parsing logic
├── file_manager.py      # File operations
├── scraper.py           # Main orchestration class
├── main.py              # CLI interface
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## Components

### BSEScraper
Main class that coordinates all operations. Simple methods for scraping data.

### SessionManager
Handles HTTP requests with proper headers and error handling.

### DataFetcher
Fetches data from BSE website with date range support.

### BSEParser
Parses HTML content and extracts table data into pandas DataFrames.

### FileManager
Saves data to organized CSV files in the `bse_data` folder.

## Output Organization

All files are automatically saved in the `bse_data` folder:
- **Auto-generated names**: `bse_insider_trading_DD_MM_YYYY_to_DD_MM_YYYY.csv`
- **Custom names**: Use `--output` flag or `save_custom_filename()` method
- **Organized structure**: Clean folder organization

## Date Formats

- **Input dates**: DD/MM/YYYY (e.g., "01/01/2025")
- **Filenames**: DD_MM_YYYY (filesystem-safe)

## Error Handling

The scraper gracefully handles:
- Network timeouts and connection errors
- Invalid HTML responses
- Date format validation
- File system errors
- Missing or malformed data

All errors are reported with clear, helpful messages.

## Requirements

- Python 3.6+
- pandas (data handling)
- requests (HTTP requests)
- beautifulsoup4 (HTML parsing)

## Installation

```bash
pip install -r requirements.txt
```

## Code Quality

- **Short functions**: Most functions under 20 lines
- **Clear names**: Descriptive variable and function names
- **Simple logic**: No complex nested conditions
- **Good comments**: Explains purpose and approach
- **Modular structure**: Easy to test and maintain

## Examples

### Get Data Summary
```python
with BSEScraper() as scraper:
    result = scraper.scrape_recent_data(7)
    if result:
        summary = scraper.get_data_summary(result['dataframe'])
        print(f"Columns: {summary['column_count']}")
        print(f"Companies: {summary.get('unique_companies', 'N/A')}")
```

### List Saved Files
```python
with BSEScraper() as scraper:
    files = scraper.list_saved_files()
    for file_info in files:
        print(f"{file_info['name']} ({file_info['size_kb']} KB)")
```

### Custom Filename
```python
with BSEScraper() as scraper:
    result = scraper.scrape_recent_data(7)
    if result:
        scraper.save_custom_filename(
            result['dataframe'],
            result['start_date'],
            result['end_date'],
            "my_custom_data.csv"
        )
```

## Notes

- The scraper respects BSE's website structure
- All dates use DD/MM/YYYY format (BSE standard)
- Files are automatically organized by date
- Clean, readable code for easy maintenance
- Comprehensive error handling throughout
