"""
BSE Scraper Package

A clean, modular BSE insider trading scraper with simple, readable code.
Designed for easy maintenance and understanding.

Main Features:
- Simple, readable code structure
- Modular design with focused components
- Clean error handling
- Organized file output
- Both Python API and CLI interface

Usage Examples:

Python API:
    from bse_scraper import BSEScraper
    
    # Basic usage
    with BSEScraper() as scraper:
        result = scraper.scrape_recent_data(days_back=7)
        if result:
            print(f"Scraped {result['record_count']} records")
    
    # Custom date range
    with BSEScraper() as scraper:
        result = scraper.scrape_date_range("01/01/2025", "07/01/2025")
        if result:
            print(f"Scraped {result['record_count']} records")

Command Line:
    python -m bse_scraper.main --days 7
    python -m bse_scraper.main --from 01/01/2025 --to 07/01/2025
"""

# Import main classes for easy access
from .scraper import BSEScraper
from .session import SessionManager
from .fetcher import DataFetcher
from .parser import BSEParser
from .file_manager import FileManager

# Import configuration for advanced users
from . import config

# Package metadata
__version__ = "1.0.0"
__author__ = "BSE Scraper Development Team"
__description__ = "Simple, modular BSE insider trading scraper"

# Define what gets imported with "from bse_scraper import *"
__all__ = [
    "BSEScraper",
    "SessionManager",
    "DataFetcher",
    "BSEParser", 
    "FileManager",
    "config"
]
