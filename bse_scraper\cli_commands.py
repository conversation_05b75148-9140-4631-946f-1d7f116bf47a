"""
Command execution logic for BSE Scraper CLI
Handles the actual scraping and processing operations using shared CLI framework.
"""

import sys
import os
from datetime import datetime
from typing import Optional, Dict, Any

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseCommandExecutor, DataDisplayHelper
from shared.data_processor import FileManager
from .scraper import BSEScraper


class BSECommandExecutor(BaseCommandExecutor):
    """BSE-specific command executor using shared framework"""

    def __init__(self):
        super().__init__("BSE")
        self.file_manager = FileManager("bse_data", "BSE")

    def _create_scraper(self):
        """Create and return BSE scraper instance"""
        return BSEScraper()

    def _handle_list_files_command(self, args):
        """Handle the --list-files command"""
        files = self.file_manager.list_saved_files()
        DataDisplayHelper.display_file_list(files, "BSE")

    def _perform_scraping(self, scraper, args) -> Optional[Dict[str, Any]]:
        """Perform the actual scraping based on arguments"""
        if args.from_date and args.to_date:
            print(f"\n🔄 Scraping custom date range: {args.from_date} to {args.to_date}")
            result = scraper.scrape_date_range(args.from_date, args.to_date)
        else:
            print(f"\n🔄 Scraping last {args.days} days")
            result = scraper.scrape_recent_data(args.days)

        return result

    def _display_results(self, scraper, result: Dict[str, Any], args) -> bool:
        """Display scraping results and handle data saving"""
        if not result:
            print("❌ No data retrieved")
            return False

        dataframe = result['dataframe']
        start_date = result['start_date']
        end_date = result['end_date']

        print(f"\n✅ Data fetched successfully!")
        print(f"Date range: {start_date} to {end_date}")
        print(f"Total records: {len(dataframe)}")

        if len(dataframe) > 0:
            # Display summary using shared helper
            DataDisplayHelper.display_summary(dataframe, "BSE", "insider trading")

            # Save data unless summary-only is specified
            if not args.summary_only:
                self._save_data(scraper, dataframe, start_date, end_date, args.output)
            else:
                print("\n📋 Summary-only mode: Data not saved")
        else:
            print("📊 No data found for the specified criteria.")

        return True

    def _save_data(self, scraper, dataframe, start_date: str, end_date: str, custom_filename: str = None):
        """Save data to CSV and database"""
        # Save with custom filename if provided
        if custom_filename:
            file_path = scraper.save_custom_filename(dataframe, start_date, end_date, custom_filename)
        else:
            # Use the file path from the result if available
            file_path = self.file_manager.save_dataframe_to_csv(dataframe, start_date, end_date)

        if file_path:
            print(f"💾 Data saved to: {file_path}")
        else:
            print("⚠️ Warning: Failed to save data to file")


def handle_list_files_command(scraper):
    """Handle the --list-files command"""
    files = scraper.list_saved_files()
    
    if not files:
        print("📁 No saved files found")
        return
    
    print("📁 Saved files:")
    print("-" * 40)
    
    for file_info in files:
        print(f"📄 {file_info['name']}")
        print(f"   Size: {file_info['size_kb']} KB")
        print(f"   Path: {file_info['path']}")
        print()


def perform_scraping(scraper, args):
    """Perform the actual scraping based on arguments"""
    if args.from_date and args.to_date:
        print(f"📅 Scraping custom date range: {args.from_date} to {args.to_date}")
        result = scraper.scrape_date_range(args.from_date, args.to_date)
    else:
        print(f"📅 Scraping last {args.days} days")
        result = scraper.scrape_recent_data(args.days)
    
    return result


def display_results(scraper, result, args):
    """Display scraping results to the user"""
    if not result:
        print("❌ Failed to scrape data")
        print("This could be due to:")
        print("- Network connectivity issues")
        print("- BSE website being unavailable")
        print("- No data available for the specified date range")
        return False
    
    print("✅ Scraping completed successfully!")
    print(f"📊 Records found: {result['record_count']}")
    print(f"📋 Columns: {result['column_count']}")
    
    # Show data summary
    summary = scraper.get_data_summary(result['dataframe'])
    
    if 'unique_companies' in summary:
        print(f"🏢 Unique companies: {summary['unique_companies']}")
    
    # Save data unless summary-only is specified
    if not args.summary_only:
        if args.output:
            # Save with custom filename
            file_path = scraper.save_custom_filename(
                result['dataframe'],
                result['start_date'],
                result['end_date'],
                args.output
            )
        else:
            file_path = result['file_path']
        
        if file_path:
            print(f"💾 Data saved to: {file_path}")
        else:
            print("⚠️ Warning: Failed to save data to file")
    else:
        print("📋 Summary-only mode: Data not saved")
    
    # Show sample data
    dataframe = result['dataframe']
    if len(dataframe) > 0:
        print(f"\n📄 Sample data (first 3 records):")
        print(dataframe.head(3).to_string(max_cols=5, max_colwidth=20))
    
    return True


# Legacy functions for backward compatibility
def show_startup_message():
    """Display startup message (legacy compatibility)"""
    print("🏢 BSE Insider Trading Scraper")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()


def handle_list_files_command(scraper):
    """Handle the --list-files command (legacy compatibility)"""
    files = scraper.list_saved_files()
    DataDisplayHelper.display_file_list(files, "BSE")


def perform_scraping(scraper, args):
    """Perform the actual scraping based on arguments (legacy compatibility)"""
    executor = BSECommandExecutor()
    return executor._perform_scraping(scraper, args)


def display_results(scraper, result, args):
    """Display scraping results and handle data saving (legacy compatibility)"""
    if not result:
        return False

    executor = BSECommandExecutor()
    return executor._display_results(scraper, result, args)


def execute_command(args):
    """Execute the main command based on parsed arguments"""
    # Use the new command executor
    executor = BSECommandExecutor()
    executor.execute_command(args)
