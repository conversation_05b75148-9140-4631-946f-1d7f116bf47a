"""
Command-line argument parser for BSE Scraper
Handles all argument parsing and validation logic using shared CLI framework.
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseArgumentParser, ValidationHelper


class BSEArgumentParser(BaseArgumentParser):
    """BSE-specific argument parser using shared framework"""

    def __init__(self):
        super().__init__(
            exchange_name="BSE",
            description="BSE Insider Trading Data Scraper"
        )

    def _add_exchange_specific_arguments(self):
        """Add BSE-specific arguments (none currently needed)"""
        pass

    def _get_date_format(self) -> str:
        """Get the date format string for BSE"""
        return "DD/MM/YYYY"

    def _get_examples(self) -> str:
        """Get example usage strings for BSE"""
        return """
Examples:
  python -m bse_scraper.main                           # Scrape last 7 days
  python -m bse_scraper.main --days 30                 # Scrape last 30 days
  python -m bse_scraper.main --from 01/01/2025 --to 07/01/2025  # Custom range
  python -m bse_scraper.main --list-files              # List saved files
        """


def create_argument_parser():
    """Create and configure the command line argument parser (legacy compatibility)"""
    bse_parser = BSEArgumentParser()
    return bse_parser.create_parser()

def validate_arguments(args):
    """Validate command line arguments (enhanced with shared validation)"""
    # Use shared validation helper
    if not ValidationHelper.validate_date_arguments(args):
        return False

    # BSE-specific date format validation
    if args.from_date:
        try:
            datetime.strptime(args.from_date, '%d/%m/%Y')
            datetime.strptime(args.to_date, '%d/%m/%Y')
        except ValueError:
            print("❌ Error: Dates must be in DD/MM/YYYY format")
            return False

    # Validate output filename
    if args.output:
        validated_filename = ValidationHelper.validate_output_filename(args.output)
        if not validated_filename:
            return False
        args.output = validated_filename

    return True


def parse_arguments():
    """Parse and validate command-line arguments"""
    parser = create_argument_parser()
    args = parser.parse_args()

    if not validate_arguments(args):
        sys.exit(1)

    return args
