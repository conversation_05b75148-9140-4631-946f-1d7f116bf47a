"""
Configuration settings for BSE Scraper
Contains all URLs, headers, and constants in one place.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import BSEConfig

# Create the configuration instance
config = BSEConfig()

# Export commonly used settings for backward compatibility
BASE_URL = config.BASE_URL
DEFAULT_DAYS_BACK = config.DEFAULT_DAYS_BACK
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
OUTPUT_FOLDER = config.OUTPUT_FOLDER
SUPABASE_URL = config.SUPABASE_URL
SUPABASE_ANON_KEY = config.SUPABASE_ANON_KEY
SAVE_CSV = config.SAVE_CSV
USE_DATABASE = config.USE_DATABASE
CSV_EXTENSION = config.CSV_EXTENSION
HTML_EXTENSION = config.HTML_EXTENSION
FILENAME_DATE_FORMAT = config.FILENAME_DATE_FORMAT

# BSE-specific settings
INSIDER_TRADING_URL = config.INSIDER_TRADING_URL
BSE_DATE_FORMAT = config.BSE_DATE_FORMAT
BSE_INTERNAL_DATE_FORMAT = config.BSE_INTERNAL_DATE_FORMAT

# BSE specific form parameters (corrected based on working scraper)
BSE_FORM_PARAMS = {
    'submit_button': 'ctl00$ContentPlaceHolder1$btnSubmit',
    'search_type': 'insider_trading'
}
