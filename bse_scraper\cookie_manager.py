"""
Cookie Manager for BSE Insider Trading Scraper
Handles dynamic cookie acquisition by visiting BSE pages.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_cookie_manager import BSECookieManager
from .config import config


class <PERSON><PERSON>anager(BSECookieManager):
    """
    BSE-specific cookie manager that inherits from the shared base class.

    This class maintains backward compatibility while using the shared
    cookie management functionality.
    """

    def __init__(self, session):
        """
        Initialize BSE cookie manager.

        Args:
            session: HTTP session object
        """
        super().__init__(session, config)
