"""
Data Fetcher for BSE Insider Trading Information
Handles fetching data from BSE website with date range support.
"""

from datetime import datetime, timedelta
from .config import BASE_URL, INSIDER_TRADING_URL, BSE_DATE_FORMAT, BSE_FORM_PARAMS


class DataFetcher:
    """
    Fetches insider trading data from BSE website.
    Handles date calculations and form submissions.
    """
    
    def __init__(self, session_manager):
        """Initialize with a session manager"""
        self.session = session_manager
        self.base_url = BASE_URL
        self.insider_url = INSIDER_TRADING_URL

    def fetch_data(self, start_date, end_date):
        """
        Main method to fetch data for a date range.
        Returns HTML content or None if failed.
        """
        try:
            # Format dates for BSE
            start_date_str = self.format_date_for_bse(start_date)
            end_date_str = self.format_date_for_bse(end_date)

            print(f"Fetching BSE data from {start_date_str} to {end_date_str}")

            # Use the existing fetch_data_for_date_range method
            result = self.fetch_data_for_date_range(start_date, end_date)

            if result and 'html_content' in result:
                return result['html_content']
            else:
                return None

        except Exception as e:
            print(f"Error in fetch_data: {e}")
            return None
    
    def fetch_recent_data(self, days_back=7):
        """
        Fetch insider trading data for the last N days.
        Returns data dictionary or None if failed.
        """
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        return self.fetch_data_for_date_range(start_date, end_date)
    
    def fetch_data_for_date_range(self, start_date, end_date):
        """
        Fetch data for a specific date range.
        Dates should be datetime objects.
        """
        try:
            # Format dates for BSE API
            start_date_str = self.format_date_for_bse(start_date)
            end_date_str = self.format_date_for_bse(end_date)

            print(f"Fetching BSE data from {start_date_str} to {end_date_str}")

            # Try simple GET request first (many BSE pages work with GET parameters)
            url = self.base_url + self.insider_url

            # Try with query parameters
            params = {
                'from_date': start_date_str,
                'to_date': end_date_str
            }

            print("Trying GET request with parameters...")
            response = self.session.make_get_request(url, params=params)

            if response and response.status_code == 200:
                print("Successfully fetched data with GET request")
                return {
                    'html_content': response.text,
                    'start_date': start_date,
                    'end_date': end_date,
                    'url': url,
                    'content_length': len(response.text)
                }

            print(f"GET request failed with status: {response.status_code if response else 'None'}")

            # If GET fails, try the original POST approach
            print("Trying POST request with form data...")
            post_result = self.try_post_request(url, start_date_str, end_date_str, start_date, end_date)

            if post_result:
                return post_result

            # If both fail, return None (no demo data)
            print("Both GET and POST requests failed. No data available.")
            print("This could be due to:")
            print("- BSE website being temporarily unavailable")
            print("- Network connectivity issues")
            print("- Changes in BSE website structure")
            return None

        except Exception as e:
            print(f"Error fetching data: {e}")
            return None

    def try_post_request(self, url, start_date_str, end_date_str, start_date, end_date):
        """Try POST request approach with proper ASP.NET form handling"""
        try:
            # First get the form page to extract ASP.NET viewstate
            print("Getting BSE form page for viewstate extraction...")
            response = self.session.make_get_request(url)

            if not response or response.status_code != 200:
                print("Failed to get BSE form page")
                return None

            # Extract form fields using the existing method
            form_data = self.extract_form_fields(response.text, start_date_str, end_date_str)

            if not form_data:
                print("Failed to extract form fields")
                return None

            print("Submitting BSE form with extracted viewstate...")
            response = self.session.make_post_request(url, data=form_data)

            if response and response.status_code == 200:
                print("✅ Successfully fetched data with POST request")
                return {
                    'html_content': response.text,
                    'start_date': start_date,
                    'end_date': end_date,
                    'url': url,
                    'content_length': len(response.text)
                }
            else:
                print(f"POST request failed with status: {response.status_code if response else 'None'}")
                return None

        except Exception as e:
            print(f"Error in POST request: {e}")
            return None
    
    def fetch_data_for_date_strings(self, start_date_str, end_date_str):
        """
        Fetch data using date strings in DD/MM/YYYY format.
        Converts strings to datetime objects first.
        """
        try:
            start_date = self.parse_date_string(start_date_str)
            end_date = self.parse_date_string(end_date_str)
            
            return self.fetch_data_for_date_range(start_date, end_date)
            
        except ValueError as e:
            print(f"Invalid date format: {e}")
            return None
    
    def format_date_for_bse(self, date_obj):
        """Convert datetime object to BSE format (DD/MM/YYYY)"""
        return date_obj.strftime(BSE_DATE_FORMAT)
    
    def parse_date_string(self, date_str):
        """Parse date string in DD/MM/YYYY format to datetime object"""
        try:
            return datetime.strptime(date_str, BSE_DATE_FORMAT)
        except ValueError:
            raise ValueError(f"Date must be in DD/MM/YYYY format, got: {date_str}")
    
    def prepare_form_data(self, start_date_str, end_date_str):
        """
        Prepare form data for BSE POST request.
        Returns dictionary with all required form fields.
        """
        # BSE uses specific form field names
        form_data = {
            'ctl00$ContentPlaceHolder1$txtFromDt': start_date_str,
            'ctl00$ContentPlaceHolder1$txtToDt': end_date_str,
            'ctl00$ContentPlaceHolder1$btnSubmit': 'Submit',
            '__EVENTTARGET': '',
            '__EVENTARGUMENT': '',
            '__VIEWSTATE': '',  # This will need to be extracted from the page
            '__VIEWSTATEGENERATOR': '',
            '__EVENTVALIDATION': ''
        }

        return form_data
    
    def validate_date_range(self, start_date, end_date):
        """
        Validate that the date range makes sense.
        Returns True if valid, raises ValueError if not.
        """
        if start_date > end_date:
            raise ValueError("Start date cannot be after end date")
        
        # Check if dates are too far in the future
        today = datetime.now()
        if start_date > today:
            raise ValueError("Start date cannot be in the future")
        
        return True

    def extract_form_fields(self, html_content, start_date_str, end_date_str):
        """
        Extract required form fields from BSE ASP.NET page.
        Uses the exact form structure from the working bsescraper.py
        """
        try:
            from bs4 import BeautifulSoup

            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract ASP.NET form fields using ID selectors (more reliable)
            viewstate = soup.find("input", {"id": "__VIEWSTATE"})
            viewstate_value = viewstate["value"] if viewstate else ''

            viewstate_gen = soup.find("input", {"id": "__VIEWSTATEGENERATOR"})
            viewstate_gen_value = viewstate_gen["value"] if viewstate_gen else ''

            event_validation = soup.find("input", {"id": "__EVENTVALIDATION"})
            event_validation_value = event_validation["value"] if event_validation else ''

            # Calculate dates in BSE internal format (YYYYMMDD)
            from datetime import datetime
            start_date_obj = datetime.strptime(start_date_str, '%d/%m/%Y')
            end_date_obj = datetime.strptime(end_date_str, '%d/%m/%Y')

            # Prepare form data with exact field names from working scraper
            form_data = {
                "__EVENTTARGET": "",
                "__EVENTARGUMENT": "",
                "__VIEWSTATE": viewstate_value,
                "__VIEWSTATEGENERATOR": viewstate_gen_value,
                "__EVENTVALIDATION": event_validation_value,
                "__VIEWSTATEENCRYPTED": "",
                "ctl00$ContentPlaceHolder1$fmdate": start_date_obj.strftime("%Y%m%d"),
                "ctl00$ContentPlaceHolder1$eddate": end_date_obj.strftime("%Y%m%d"),
                "ctl00$ContentPlaceHolder1$hidCurrentDate": end_date_obj.strftime("%Y/%m/%d"),
                "ctl00$ContentPlaceHolder1$txtDate": start_date_str,
                "ctl00$ContentPlaceHolder1$txtTodate": end_date_str,
                "ctl00$ContentPlaceHolder1$hf_scripcode": "",
                "ctl00$ContentPlaceHolder1$btnSubmit": "Submit",
                "ctl00$ContentPlaceHolder1$SmartSearch$hdnCode": "",
                "ctl00$ContentPlaceHolder1$SmartSearch$smartSearch": "",
                "ctl00$ContentPlaceHolder1$ctl00_ContentPlaceHolder1_hdnCode": "",
                "ctl00$ContentPlaceHolder1$rdoMode": "0",
                "ddlPeriod": "Day",
                "ddlRange": "1M",
                "txtScripCode": "",
                "txtCompany": "",
                "txtName": "",
            }

            print(f"✅ Extracted BSE form fields successfully ({len(form_data)} fields)")
            return form_data

        except Exception as e:
            print(f"Error extracting BSE form fields: {e}")
            return None