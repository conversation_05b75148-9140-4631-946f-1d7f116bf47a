"""
File Manager for BSE Scraper
Handles saving data to organized folders and files.
"""

import os
import pandas as pd
from .config import OUTPUT_FOLDER, CSV_EXTENSION, FILENAME_DATE_FORMAT
from .hash_utils import add_record_hashes, find_duplicates_in_csv


class FileManager:
    """
    Manages file operations for BSE scraper.
    Creates organized folder structure and saves data cleanly.
    """
    
    def __init__(self):
        """Initialize file manager and create output folder"""
        self.output_folder = OUTPUT_FOLDER
        self.create_output_folder()
    
    def create_output_folder(self):
        """Create the output folder if it doesn't exist"""
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"Created output folder: {self.output_folder}")
        else:
            print(f"Using existing output folder: {self.output_folder}")
    
    def save_dataframe_to_csv(self, dataframe, start_date, end_date, custom_filename=None):
        """
        Save pandas DataFrame to CSV file with duplicate detection.
        Uses persistent CSV file approach like NSE scraper.
        """
        try:
            # Use persistent filename for BSE data
            persistent_filename = "bse_insider_trading_persistent.csv"
            file_path = os.path.join(self.output_folder, persistent_filename)

            # Add record hashes for duplicate detection
            dataframe_with_hashes = add_record_hashes(dataframe.copy())

            if os.path.exists(file_path):
                print(f"📁 Found existing BSE CSV file: {file_path}")

                # Find duplicates
                new_records, duplicate_count = find_duplicates_in_csv(dataframe_with_hashes, file_path)

                if len(new_records) > 0:
                    # Append new records to existing file
                    existing_df = pd.read_csv(file_path)
                    combined_df = pd.concat([existing_df, new_records], ignore_index=True)
                    combined_df.to_csv(file_path, index=False)

                    print(f"💾 BSE data saved to: {file_path}")
                    print(f"📊 Added {len(new_records)} new records")
                    print(f"📊 Total records in file: {len(combined_df)}")
                    print(f"📊 Duplicates skipped: {duplicate_count}")
                else:
                    print(f"📊 No new records to add (all {duplicate_count} were duplicates)")
            else:
                print(f"📁 No existing BSE CSV file found at {file_path}")
                # Save new file
                dataframe_with_hashes.to_csv(file_path, index=False)
                print(f"💾 BSE data saved to: {file_path}")
                print(f"📊 Added {len(dataframe_with_hashes)} new records")
                print(f"📊 Total records in file: {len(dataframe_with_hashes)}")
                print(f"📊 Duplicates skipped: 0")

            return file_path

        except Exception as e:
            print(f"Error saving BSE DataFrame to CSV: {e}")
            return None

    def save_dataframe_to_csv_legacy(self, dataframe, start_date, end_date, custom_filename=None):
        """
        Legacy method: Save DataFrame with date-based filename.
        Creates filename from dates if no custom filename provided.
        """
        try:
            # Generate filename
            if custom_filename:
                filename = self.ensure_csv_extension(custom_filename)
            else:
                filename = self.generate_filename_from_dates(start_date, end_date)

            # Create full file path
            file_path = os.path.join(self.output_folder, filename)

            # Save the DataFrame
            dataframe.to_csv(file_path, index=False)

            print(f"Data saved to: {file_path}")
            return file_path

        except Exception as e:
            print(f"Error saving DataFrame to CSV: {e}")
            return None
    
    def generate_filename_from_dates(self, start_date, end_date):
        """
        Generate a filename using start and end dates.
        Format: bse_insider_trading_DD_MM_YYYY_to_DD_MM_YYYY.csv
        """
        start_str = start_date.strftime(FILENAME_DATE_FORMAT)
        end_str = end_date.strftime(FILENAME_DATE_FORMAT)
        
        filename = f"bse_insider_trading_{start_str}_to_{end_str}{CSV_EXTENSION}"
        return filename
    
    def ensure_csv_extension(self, filename):
        """Make sure filename has .csv extension"""
        if not filename.endswith(CSV_EXTENSION):
            filename += CSV_EXTENSION
        return filename
    
    def save_html_for_debug(self, html_content, start_date, end_date):
        """
        Save HTML content for debugging purposes.
        Only used when debugging is needed.
        """
        try:
            start_str = start_date.strftime(FILENAME_DATE_FORMAT)
            end_str = end_date.strftime(FILENAME_DATE_FORMAT)
            
            filename = f"bse_debug_{start_str}_to_{end_str}.html"
            file_path = os.path.join(self.output_folder, filename)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(html_content)
            
            print(f"Debug HTML saved to: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"Error saving debug HTML: {e}")
            return None
    
    def list_saved_files(self):
        """
        List all files in the output folder.
        Returns list of file information dictionaries.
        """
        try:
            if not os.path.exists(self.output_folder):
                return []
            
            files_info = []
            
            for filename in os.listdir(self.output_folder):
                file_path = os.path.join(self.output_folder, filename)
                
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    
                    file_info = {
                        'name': filename,
                        'path': file_path,
                        'size_bytes': file_size,
                        'size_kb': round(file_size / 1024, 1),
                        'size_mb': round(file_size / (1024 * 1024), 2)
                    }
                    
                    files_info.append(file_info)
            
            return files_info
            
        except Exception as e:
            print(f"Error listing files: {e}")
            return []
    
    def get_file_info(self, file_path):
        """
        Get information about a specific file.
        Returns dictionary with file details.
        """
        try:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                
                return {
                    'exists': True,
                    'size_bytes': file_size,
                    'size_kb': round(file_size / 1024, 1),
                    'size_mb': round(file_size / (1024 * 1024), 2),
                    'path': file_path
                }
            else:
                return {
                    'exists': False,
                    'path': file_path
                }
                
        except Exception as e:
            print(f"Error getting file info: {e}")
            return {
                'exists': False,
                'error': str(e),
                'path': file_path
            }
    
    def clean_old_files(self, days_to_keep=30):
        """
        Clean up old files (optional utility method).
        Removes files older than specified days.
        """
        try:
            import time
            from datetime import datetime, timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_timestamp = cutoff_date.timestamp()
            
            removed_count = 0
            
            for filename in os.listdir(self.output_folder):
                file_path = os.path.join(self.output_folder, filename)
                
                if os.path.isfile(file_path):
                    file_modified_time = os.path.getmtime(file_path)
                    
                    if file_modified_time < cutoff_timestamp:
                        os.remove(file_path)
                        removed_count += 1
                        print(f"Removed old file: {filename}")
            
            print(f"Cleaned up {removed_count} old files")
            return removed_count
            
        except Exception as e:
            print(f"Error cleaning old files: {e}")
            return 0
