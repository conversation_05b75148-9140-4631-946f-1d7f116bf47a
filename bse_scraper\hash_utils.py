"""
Hash Utilities for BSE Insider Trading Scraper
Provides unified hash generation for duplicate detection.

Note: This module now uses the standardized hash function from shared.utils.calculate_unified_hash
for consistency across both NSE and BSE scrapers.
"""

import pandas as pd
import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Legacy hash function removed - now using shared/utils.calculate_unified_hash()
# This ensures consistency across both NSE and BSE scrapers


def add_record_hashes(dataframe):
    """
    Add record hash column to DataFrame for duplicate detection.
    Uses the standardized hash function from shared.utils.calculate_unified_hash.

    Args:
        dataframe: pandas DataFrame with BSE insider trading data

    Returns:
        pandas DataFrame with added 'record_hash' column
    """
    try:
        if dataframe is None or dataframe.empty:
            return dataframe

        # Use the shared hash function for consistency
        from shared.utils import calculate_unified_hash

        print(f"Generating hashes for {len(dataframe)} BSE records using shared hash function...")

        # Generate hash for each row using the standardized hash function
        def calculate_bse_hash(row):
            try:
                return calculate_unified_hash('bse', **row.to_dict())
            except Exception as e:
                print(f"⚠️ Hash calculation error for row: {e}")
                # Simple fallback hash if shared function fails
                import hashlib
                return hashlib.sha256(str(row.to_dict()).encode('utf-8')).hexdigest()

        dataframe['record_hash'] = dataframe.apply(calculate_bse_hash, axis=1)

        # Check for any duplicate hashes within this dataset
        duplicate_hashes = dataframe['record_hash'].duplicated().sum()
        if duplicate_hashes > 0:
            print(f"⚠️ Found {duplicate_hashes} duplicate hashes within dataset")

        print(f"✅ Generated {len(dataframe)} hashes for BSE records")
        return dataframe

    except Exception as e:
        print(f"❌ Error generating hashes: {e}")
        # Fallback to legacy method if shared hash function fails
        print("🔄 Falling back to legacy hash calculation...")

        print(f"Generating hashes for {len(dataframe)} BSE records...")

        # Generate simple fallback hash for each row
        import hashlib
        dataframe['record_hash'] = dataframe.apply(
            lambda row: hashlib.sha256(str(row.to_dict()).encode('utf-8')).hexdigest(), axis=1
        )

        # Check for any duplicate hashes within this dataset
        duplicate_hashes = dataframe['record_hash'].duplicated().sum()
        if duplicate_hashes > 0:
            print(f"⚠️ Found {duplicate_hashes} duplicate hashes within dataset")

        print(f"✅ Generated {len(dataframe)} hashes for BSE records")
        return dataframe


def find_duplicates_in_csv(dataframe, csv_file_path):
    """
    Find duplicate records by comparing with existing CSV file.
    
    Args:
        dataframe: New data to check
        csv_file_path: Path to existing CSV file
        
    Returns:
        tuple: (new_records_df, duplicate_count)
    """
    try:
        import os
        
        if not os.path.exists(csv_file_path):
            print(f"No existing CSV file found at {csv_file_path}")
            return dataframe, 0
        
        # Load existing data
        existing_df = pd.read_csv(csv_file_path)
        
        if 'record_hash' not in existing_df.columns:
            print("Existing CSV doesn't have record_hash column, treating all as new")
            return dataframe, 0
        
        # Find duplicates
        existing_hashes = set(existing_df['record_hash'].values)
        new_hashes = set(dataframe['record_hash'].values)
        
        duplicate_hashes = new_hashes.intersection(existing_hashes)
        duplicate_count = len(duplicate_hashes)
        
        if duplicate_count > 0:
            # Filter out duplicates
            new_records = dataframe[~dataframe['record_hash'].isin(duplicate_hashes)]
            print(f"🔍 Found {duplicate_count} duplicates, {len(new_records)} new records")
            return new_records, duplicate_count
        else:
            print(f"✅ No duplicates found, all {len(dataframe)} records are new")
            return dataframe, 0
            
    except Exception as e:
        print(f"Error checking for duplicates: {e}")
        return dataframe, 0


def get_hash_summary(dataframe):
    """
    Get summary statistics about hashes in the DataFrame.

    Args:
        dataframe: DataFrame with record_hash column

    Returns:
        dict: Summary statistics
    """
    try:
        if 'record_hash' not in dataframe.columns:
            return {'error': 'No record_hash column found'}

        total_records = len(dataframe)
        unique_hashes = dataframe['record_hash'].nunique()
        duplicate_hashes = total_records - unique_hashes

        return {
            'total_records': total_records,
            'unique_hashes': unique_hashes,
            'duplicate_hashes': duplicate_hashes,
            'duplicate_percentage': (duplicate_hashes / total_records * 100) if total_records > 0 else 0
        }

    except Exception as e:
        return {'error': f'Error calculating hash summary: {e}'}
