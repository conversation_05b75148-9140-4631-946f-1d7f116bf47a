"""
Main BSE Scraper Class
Orchestrates all components to provide a clean, simple interface.
Enhanced with proper BSE form handling and duplicate detection.
"""

from .session import <PERSON><PERSON>anager
from .fetcher import <PERSON>Fetcher
from .parser import BSEParser
from .file_manager import <PERSON>Manager
from .database_manager import BSEDatabaseManager
from .config import BASE_URL, DEFAULT_DAYS_BACK, SAVE_CSV, USE_DATABASE
from datetime import datetime, timedelta


class BSEScraper:
    """
    Main BSE scraper class that coordinates all operations.
    Provides simple methods for scraping insider trading data with duplicate detection.
    """

    def __init__(self):
        """Initialize all components of the scraper"""
        print("🏢 BSE Insider Trading Scraper")
        print("=" * 50)
        print("Initializing enhanced BSE scraper...")

        # Initialize all components
        self.session_manager = SessionManager()
        self.data_fetcher = DataFetcher(self.session_manager)
        self.html_parser = BSEParser(self.session_manager)  # Pass session manager to parser
        self.file_manager = FileManager()

        # Initialize database manager if enabled
        self.database_manager = None
        if USE_DATABASE:
            try:
                self.database_manager = BSEDatabaseManager()
                print("✅ Database integration enabled")
            except Exception as e:
                print(f"⚠️ Database integration failed: {e}")
                print("⚠️ Continuing with CSV-only mode")

        print("✅ BSE Scraper ready to use")
        print(f"✅ CSV saving {'enabled' if SAVE_CSV else 'disabled'}")
        print(f"✅ Database saving {'enabled' if USE_DATABASE and self.database_manager else 'disabled'}")
        print()
    
    def scrape_recent_data(self, days_back=DEFAULT_DAYS_BACK):
        """
        Scrape insider trading data for the last N days.
        Returns dictionary with results or None if failed.
        """
        try:
            print(f"📅 Scraping BSE data for the last {days_back} days...")

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            print(f"📅 Date range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")

            # Test connection first
            if not self.test_connection():
                return None

            # Fetch the data using proper BSE form submission
            print("🔄 Fetching data from BSE...")
            html_content = self.data_fetcher.fetch_data(start_date, end_date)

            if html_content is None:
                print("❌ Failed to fetch data from BSE")
                return None

            print("✅ Successfully fetched data from BSE")

            # Parse the HTML content
            print("🔄 Parsing BSE response...")
            dataframe = self.html_parser.parse_html_content(html_content, start_date, end_date)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No data found or parsing failed")
                return None

            print(f"✅ Successfully parsed {len(dataframe)} records")

            # Validate and clean the data before processing
            print("🔍 Validating parsed data...")
            dataframe = self._validate_and_clean_dataframe(dataframe)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No valid data found after validation")
                return None

            print(f"✅ Validation complete: {len(dataframe)} valid records")

            # Add record hashes for duplicate detection (needed for both CSV and database)
            from .hash_utils import add_record_hashes
            dataframe_with_hashes = add_record_hashes(dataframe.copy())

            # Save the data to CSV if enabled
            file_path = None
            if SAVE_CSV:
                print("💾 Saving BSE data to CSV...")
                file_path = self.file_manager.save_dataframe_to_csv(
                    dataframe_with_hashes,
                    start_date,
                    end_date
                )
            else:
                print("📋 CSV saving disabled")

            # Save to database if enabled
            db_result = None
            if USE_DATABASE and self.database_manager:
                print("🗄️ Saving BSE data to database...")
                # Convert DataFrame to records for database insertion
                records = dataframe_with_hashes.to_dict('records')
                db_result = self.database_manager.save_records(records)

                if db_result['success']:
                    print(f"🗄️ Database: {db_result['inserted']} new BSE records inserted")
                    if db_result['duplicates'] > 0:
                        print(f"🗄️ Database: {db_result['duplicates']} duplicates skipped")
                else:
                    print(f"❌ Database insertion failed: {db_result['errors']} errors")
            else:
                print("📋 Database saving disabled")

            # Return comprehensive result
            return {
                'dataframe': dataframe,
                'file_path': file_path,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(dataframe),
                'column_count': len(dataframe.columns),
                'database_result': db_result
            }

        except Exception as e:
            print(f"❌ Error in scrape_recent_data: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def scrape_date_range(self, start_date_str, end_date_str):
        """
        Scrape data for a specific date range.
        Dates should be in DD/MM/YYYY format.
        """
        try:
            print(f"📅 Scraping BSE data from {start_date_str} to {end_date_str}")

            # Parse date strings
            from datetime import datetime
            start_date = datetime.strptime(start_date_str, '%d/%m/%Y')
            end_date = datetime.strptime(end_date_str, '%d/%m/%Y')

            # Test connection first
            if not self.test_connection():
                return None

            # Fetch data for the date range
            print("🔄 Fetching data from BSE...")
            html_content = self.data_fetcher.fetch_data(start_date, end_date)

            if html_content is None:
                print("❌ Failed to fetch data from BSE")
                return None

            print("✅ Successfully fetched data from BSE")

            # Parse the HTML content
            print("🔄 Parsing BSE response...")
            dataframe = self.html_parser.parse_html_content(html_content, start_date, end_date)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No data found or parsing failed")
                return None

            print(f"✅ Successfully parsed {len(dataframe)} records")

            # Add record hashes for duplicate detection (needed for both CSV and database)
            from .hash_utils import add_record_hashes
            dataframe_with_hashes = add_record_hashes(dataframe.copy())

            # Save the data to CSV if enabled
            file_path = None
            if SAVE_CSV:
                print("💾 Saving BSE data to CSV...")
                file_path = self.file_manager.save_dataframe_to_csv(
                    dataframe_with_hashes,
                    start_date,
                    end_date
                )
            else:
                print("📋 CSV saving disabled")

            # Save to database if enabled
            db_result = None
            if USE_DATABASE and self.database_manager:
                print("🗄️ Saving BSE data to database...")
                # Convert DataFrame to records for database insertion
                records = dataframe_with_hashes.to_dict('records')
                db_result = self.database_manager.save_records(records)

                if db_result['success']:
                    print(f"🗄️ Database: {db_result['inserted']} new BSE records inserted")
                    if db_result['duplicates'] > 0:
                        print(f"🗄️ Database: {db_result['duplicates']} duplicates skipped")
                else:
                    print(f"❌ Database insertion failed: {db_result['errors']} errors")
            else:
                print("📋 Database saving disabled")

            # Return comprehensive result
            return {
                'dataframe': dataframe,
                'file_path': file_path,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(dataframe),
                'column_count': len(dataframe.columns),
                'database_result': db_result
            }

        except Exception as e:
            print(f"❌ Error in scrape_date_range: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def test_connection(self):
        """Test connection to BSE website"""
        print("🔍 Testing connection to BSE website...")
        success = self.session_manager.test_connection(BASE_URL)
        if success:
            print("✅ BSE connection test successful")
        else:
            print("❌ BSE connection test failed")
        return success
    
    def get_data_summary(self, dataframe):
        """
        Get a summary of the scraped data.
        Returns dictionary with useful statistics.
        """
        if dataframe is None or len(dataframe) == 0:
            return {
                'total_records': 0,
                'columns': [],
                'column_count': 0
            }
        
        summary = {
            'total_records': len(dataframe),
            'columns': dataframe.columns.tolist(),
            'column_count': len(dataframe.columns)
        }
        
        # Try to find company information
        company_columns = [col for col in dataframe.columns 
                          if 'company' in col.lower() or 'name' in col.lower()]
        
        if company_columns:
            company_col = company_columns[0]
            unique_companies = dataframe[company_col].unique()
            summary['unique_companies'] = len(unique_companies)
            summary['sample_companies'] = unique_companies[:5].tolist()
        
        return summary
    
    def list_saved_files(self):
        """List all files saved by the scraper"""
        return self.file_manager.list_saved_files()
    
    def save_custom_filename(self, dataframe, start_date, end_date, filename):
        """
        Save dataframe with a custom filename.
        Useful for specific naming requirements.
        """
        return self.file_manager.save_dataframe_to_csv(
            dataframe, start_date, end_date, filename
        )
    
    def close(self):
        """Close the scraper and clean up resources"""
        self.session_manager.close_session()

    def _validate_and_clean_dataframe(self, dataframe):
        """
        Validate and clean the parsed DataFrame to remove truly malformed records.
        Only filters out records that are genuinely problematic (like completely empty rows).

        Args:
            dataframe: pandas DataFrame with parsed BSE data

        Returns:
            pandas DataFrame with validated and cleaned data
        """
        try:
            if dataframe is None or dataframe.empty:
                return dataframe

            import pandas as pd

            original_count = len(dataframe)
            print(f"🔍 Validating {original_count} parsed records...")

            # Remove completely empty rows (all values are NaN or empty strings)
            cleaned_dataframe = dataframe.dropna(how='all').copy()

            # Remove rows where all text columns are empty or just whitespace
            text_columns = dataframe.select_dtypes(include=['object']).columns
            if len(text_columns) > 0:
                # Create mask for rows that have at least one non-empty text field
                non_empty_mask = cleaned_dataframe[text_columns].apply(
                    lambda row: row.astype(str).str.strip().ne('').any(), axis=1
                )
                cleaned_dataframe = cleaned_dataframe[non_empty_mask].copy()

            # Only filter out records that have critical missing data that would cause database issues
            # Check for records where BOTH security code AND security name are missing
            security_code_col = None
            security_name_col = None
            person_name_col = None
            person_category_col = None

            # Find the actual column names (they vary between CSV and HTML parsing)
            for col in cleaned_dataframe.columns:
                if 'Security Code' in col:
                    security_code_col = col
                elif 'Security Name' in col:
                    security_name_col = col
                elif 'Name of Person' in col or 'Name of  Person' in col:
                    person_name_col = col
                elif 'Category of Person' in col or 'Category of person' in col:
                    person_category_col = col

            # Filter out records that are truly malformed (missing ALL critical fields)
            if security_code_col and security_name_col and person_name_col and person_category_col:
                # Only remove records where ALL critical fields are null/empty
                critical_fields_mask = (
                    (cleaned_dataframe[security_code_col].notna() &
                     (cleaned_dataframe[security_code_col].astype(str).str.strip() != '')) |
                    (cleaned_dataframe[security_name_col].notna() &
                     (cleaned_dataframe[security_name_col].astype(str).str.strip() != '')) |
                    (cleaned_dataframe[person_name_col].notna() &
                     (cleaned_dataframe[person_name_col].astype(str).str.strip() != '')) |
                    (cleaned_dataframe[person_category_col].notna() &
                     (cleaned_dataframe[person_category_col].astype(str).str.strip() != ''))
                )

                cleaned_dataframe = cleaned_dataframe[critical_fields_mask].copy()

            removed_count = original_count - len(cleaned_dataframe)
            if removed_count > 0:
                print(f"🧹 Removed {removed_count} truly malformed records")
                print(f"✅ Kept {len(cleaned_dataframe)} valid records")
            else:
                print(f"✅ All {len(cleaned_dataframe)} records are valid")

            return cleaned_dataframe

        except Exception as e:
            print(f"❌ Error during data validation: {e}")
            import traceback
            traceback.print_exc()
            return dataframe  # Return original dataframe if validation fails
        print("BSE Scraper closed")
    
    def __enter__(self):
        """Support for context manager (with statement)"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Cleanup when exiting context manager"""
        self.close()
