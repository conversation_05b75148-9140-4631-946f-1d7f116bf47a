"""
HTTP Session Manager for BSE Scraper
Handles all HTTP requests with proper headers, retry strategy, and error handling.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_session_manager import BSESessionManager
from .config import config


class SessionManager(BSESessionManager):
    """
    BSE-specific session manager that inherits from the shared base class.

    This class maintains backward compatibility while using the shared
    session management functionality.
    """

    def __init__(self):
        """Initialize BSE session manager"""
        super().__init__(config)

