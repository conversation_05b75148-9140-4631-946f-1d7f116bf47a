steps:
  # Build the BSE scraper Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-f'
      - 'bse_scraper/Dockerfile'
      - '-t'
      - 'asia-south1-docker.pkg.dev/webscrapers-463817/scrapers/bse-scraper:latest'
      - '.'
    
  # Push the image to Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'asia-south1-docker.pkg.dev/webscrapers-463817/scrapers/bse-scraper:latest'

images:
  - 'asia-south1-docker.pkg.dev/webscrapers-463817/scrapers/bse-scraper:latest'

options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
