import { crypto } from "https://deno.land/std@0.200.0/crypto/mod.ts";

async function getPdfHashFromUrl(url: string): Promise<string> {
  try {
    console.log(`Fetching: ${url}`);
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/pdf,*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText} for ${url}`);
    }

    console.log(`Successfully fetched ${url} (${response.headers.get('content-length')} bytes)`);
    const pdfData = new Uint8Array(await response.arrayBuffer());
    const hashBuffer = await crypto.subtle.digest("SHA-256", pdfData);
    // Convert ArrayBuffer to hex string
    const hashArray = new Uint8Array(hashBuffer);
    return Array.from(hashArray)
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
  } catch (error) {
    console.error(`Error fetching ${url}:`, error.message);
    throw error;
  }
}

async function comparePdfsByUrl(url1: string, url2: string): Promise<boolean | null> {
  try {
    const results = await Promise.allSettled([
      getPdfHashFromUrl(url1),
      getPdfHashFromUrl(url2),
    ]);

    const hash1Result = results[0];
    const hash2Result = results[1];

    if (hash1Result.status === 'rejected') {
      console.error(`Failed to get hash for PDF 1: ${hash1Result.reason}`);
      return null;
    }

    if (hash2Result.status === 'rejected') {
      console.error(`Failed to get hash for PDF 2: ${hash2Result.reason}`);
      return null;
    }

    const hash1 = hash1Result.value;
    const hash2 = hash2Result.value;

    console.log(`Hash of PDF 1: ${hash1}`);
    console.log(`Hash of PDF 2: ${hash2}`);
    return hash1 === hash2;
  } catch (error) {
    console.error('Error comparing PDFs:', error);
    return null;
  }
}

async function comparePdfsBinaryFromUrls(url1: string, url2: string): Promise<boolean> {
  try {
    console.log(`Binary comparison - Fetching: ${url1}`);
    console.log(`Binary comparison - Fetching: ${url2}`);

    const fetchOptions = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/pdf,*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      }
    };

    const [response1, response2] = await Promise.all([
      fetch(url1, fetchOptions),
      fetch(url2, fetchOptions)
    ]);

    if (!response1.ok || !response2.ok) {
      throw new Error(`Failed to fetch PDFs: ${response1.status} ${response1.statusText}, ${response2.status} ${response2.statusText}`);
    }

    const [data1, data2] = await Promise.all([
      new Uint8Array(await response1.arrayBuffer()),
      new Uint8Array(await response2.arrayBuffer()),
    ]);

    console.log(`Binary comparison - PDF 1 size: ${data1.length} bytes`);
    console.log(`Binary comparison - PDF 2 size: ${data2.length} bytes`);

    if (data1.length !== data2.length) {
      console.log('Binary comparison - Different file sizes, PDFs are not identical');
      return false;
    }

    console.log('Binary comparison - Comparing bytes...');

    // Sample positions to check and log (beginning, middle, and end)
    const samplePositions = [
      0, 1, 2, // First few bytes
      Math.floor(data1.length / 2) - 1, Math.floor(data1.length / 2), Math.floor(data1.length / 2) + 1, // Middle bytes
      data1.length - 3, data1.length - 2, data1.length - 1 // Last few bytes
    ];

    // Log sample bytes for demonstration
    console.log('Sample bytes comparison:');
    for (const pos of samplePositions) {
      console.log(`  Byte ${pos}: PDF1=0x${data1[pos].toString(16).padStart(2, '0')}, PDF2=0x${data2[pos].toString(16).padStart(2, '0')}`);
    }

    // Actual comparison
    for (let i = 0; i < data1.length; i++) {
      if (data1[i] !== data2[i]) {
        console.log(`Binary comparison - Difference found at byte ${i}:`);
        console.log(`  PDF 1 byte ${i}: ${data1[i]} (0x${data1[i].toString(16).padStart(2, '0')})`);
        console.log(`  PDF 2 byte ${i}: ${data2[i]} (0x${data2[i].toString(16).padStart(2, '0')})`);

        // Show some context around the difference
        const start = Math.max(0, i - 5);
        const end = Math.min(data1.length, i + 6);
        console.log(`  Context (bytes ${start}-${end-1}):`);
        console.log(`    PDF 1: [${Array.from(data1.slice(start, end)).map(b => b.toString(16).padStart(2, '0')).join(' ')}]`);
        console.log(`    PDF 2: [${Array.from(data2.slice(start, end)).map(b => b.toString(16).padStart(2, '0')).join(' ')}]`);

        return false;
      }
    }

    console.log('Binary comparison - All bytes match');
    return true;
  } catch (error) {
    console.error('Binary comparison error:', error.message);
    throw error;
  }
}

// Usage (Replace with your PDF URLs)
const pdfUrl1 = "https://nsearchives.nseindia.com/corporate/ICICIPRULI_15072025134258_Outcome_BM_July_15_2025_Financials_.pdf";
const pdfUrl2 = "https://nsearchives.nseindia.com/corporate/MANKIND2_14072025222935_BMIntimation14072025.pdf";

// Hash-based comparison
const areSame = await comparePdfsByUrl(pdfUrl1, pdfUrl2);
if (areSame === null) {
  console.log('Could not compare PDFs due to errors');
} else {
  console.log(`Are the PDFs identical (hash)? ${areSame}`);
}

// Binary comparison
const areSameBinary = await comparePdfsBinaryFromUrls(pdfUrl1, pdfUrl2);
console.log(`Are the PDFs identical (binary)? ${areSameBinary}`);