{"global": {"project_id": "webscrapers-463817", "region": "asia-south1", "container_registry": "gcr.io", "artifact_registry": "asia-south1-docker.pkg.dev", "supabase": {"url": "https://ratzkumhtswilixzfcgl.supabase.co"}, "common": {"timeout": 30, "save_csv": true, "use_database": true, "max_retries": 3, "retry_delay": 5}}, "environment": {"name": "prod", "project_id": "webscrapers-463817", "cloud_run": {"cpu": "1", "memory": "1Gi", "max_instances": 3, "timeout": 2400, "min_instances": 0, "concurrency": 1, "execution_environment": "gen2"}, "cloud_scheduler": {"timezone": "Asia/Kolkata", "retry_config": {"retry_count": 3, "max_retry_duration": "600s", "min_backoff_duration": "5s", "max_backoff_duration": "300s", "max_doublings": 3}}}, "scrapers": {"bse-insider": {"name": "bse-insider-trading-scraper", "description": "BSE Insider Trading Data Scraper", "scraper_type": "bse", "image_name": "bse-scraper", "dockerfile_path": "bse_scraper/Dockerfile", "build_context": ".", "default_args": {"days": 1}, "schedule": {"cron": "0 1 * * *", "timezone": "Asia/Kolkata", "enabled": true, "description": "Run BSE insider trading scraper daily at 1 AM IST"}, "env_vars": {"PYTHONUNBUFFERED": "1", "PYTHONDONTWRITEBYTECODE": "1", "SCRAPER_TYPE": "bse-insider", "SCRAPERAPI_KEY": "********************************", "USE_PROXY": "true"}, "args": {"days": 1}}, "nse-insider": {"name": "nse-insider-trading-scraper", "description": "NSE Insider Trading Data Scraper", "scraper_type": "nse-insider", "image_name": "nse-scraper", "dockerfile_path": "nse_scraper/Dockerfile", "build_context": ".", "default_args": {"days": 7}, "schedule": {"cron": "15 1 * * *", "timezone": "Asia/Kolkata", "enabled": true, "description": "Run NSE insider trading scraper daily at 1:15 AM IST"}, "env_vars": {"PYTHONUNBUFFERED": "1", "PYTHONDONTWRITEBYTECODE": "1", "SCRAPER_TYPE": "nse-insider", "SCRAPERAPI_KEY": "********************************", "USE_PROXY": "true"}, "args": {"days": 1}}, "nse-corporate": {"name": "nse-corporate-announcements-scraper", "description": "NSE Corporate Announcements Data Scraper", "scraper_type": "nse-corporate", "image_name": "nse-scraper", "dockerfile_path": "nse_scraper/Dockerfile", "build_context": ".", "default_args": {"corporate_announcements": true}, "schedule": {"cron": "30 1 * * *", "timezone": "Asia/Kolkata", "enabled": true, "description": "Run NSE corporate announcements scraper daily at 1:30 AM IST"}, "env_vars": {"PYTHONUNBUFFERED": "1", "PYTHONDONTWRITEBYTECODE": "1", "SCRAPER_TYPE": "nse-corporate"}, "args": {"corporate_announcements": true}}}, "security": {"service_accounts": {"scraper_runner": {"name": "scraper-runner", "display_name": "<PERSON><PERSON>er Runner Service Account", "description": "Service account for running scraper Cloud Run jobs", "roles": ["roles/run.invoker", "roles/secretmanager.secretAccessor", "roles/logging.logWriter", "roles/monitoring.metricWriter", "roles/cloudsql.client"]}, "scheduler": {"name": "scraper-scheduler", "display_name": "Scraper Scheduler Service Account", "description": "Service account for Cloud Scheduler to invoke jobs", "roles": ["roles/run.invoker", "roles/cloudscheduler.jobRunner"]}, "github_actions": {"name": "github-actions-scraper", "display_name": "GitHub Actions Scraper Deployment", "description": "Service account for GitHub Actions CI/CD pipeline", "roles": ["roles/run.admin", "roles/storage.admin", "roles/cloudscheduler.admin", "roles/iam.serviceAccountUser", "roles/secretmanager.admin"]}}, "secrets": [{"name": "supabase-anon-key", "description": "Supabase anonymous key for database access"}, {"name": "supabase-service-key", "description": "Supabase service key for admin operations"}]}, "monitoring": {"log_retention_days": 30, "alerts": {"job_failure": {"enabled": true, "notification_channels": [], "threshold": 1}, "job_duration": {"enabled": true, "threshold_minutes": 45, "notification_channels": []}, "error_rate": {"enabled": true, "threshold_percent": 10, "notification_channels": []}}, "logging": {"level": "INFO", "structured": true, "include_request_id": true}}, "cost_optimization": {"cleanup_old_revisions": true, "keep_revisions": 3, "use_preemptible": false, "resource_limits": {"max_concurrent_jobs": 3, "job_timeout_minutes": 30}}, "deployment": {"phases": {"phase1": {"name": "Manual Deployment", "description": "Deploy from local machine using gcloud CLI", "enabled": true}, "phase2": {"name": "Automated CI/CD", "description": "Deploy using GitHub Actions", "enabled": true, "triggers": ["push", "manual_dispatch"]}}, "rollback": {"enabled": true, "keep_previous_versions": 2}}}