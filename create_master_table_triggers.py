#!/usr/bin/env python3
"""
Create Database Triggers for Master Corporate Announcements System
This script creates triggers on NSE and BSE tables to automatically sync data to master table
"""

import requests
import time
from typing import List, Tuple

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class MasterTableTriggerManager:
    """Manager for creating and managing master table triggers"""
    
    def __init__(self):
        self.headers = headers
        
        # BSE column mapping for trigger function
        self.bse_mapping = {
            'id': 'bse_source_id',
            'attachmentfile': 'attachment_file',
            'headline': 'headline',
            'categoryname': 'category',
            'company_name': 'company_name',
            'news_submission_dt': 'submission_date',
            'dissemdt': 'dissemination_date',
            'fld_attachsize': 'file_size_bytes',
            'xml_data': 'xml_data_url',
            'classification': 'classification',
            'parsed_subject': 'parsed_subject',
            'pdf_hash': 'pdf_hash',
            'subject': 'subject',
            'subcatname': 'subcategory',
            'bse_code': 'bse_code',
            'more': 'details'
        }
        
        # NSE column mapping for trigger function
        self.nse_mapping = {
            'id': 'nse_source_id',
            'symbol': 'nse_symbol',
            'company_name': 'company_name',
            'subject': 'subject',
            'details': 'details',
            'broadcast_date_time': 'broadcast_date',
            'dissemination': 'dissemination_date',
            'attachment_url': 'attachment_url',
            'file_size': 'file_size_text',
            'pdf_hash': 'pdf_hash'
        }
    
    def execute_sql(self, query: str, description: str = "") -> bool:
        """Execute SQL query via Supabase"""
        
        try:
            print(f"🔧 {description}...")
            
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/rpc/exec_sql",
                headers=self.headers,
                json={"sql": query},
                timeout=60
            )
            
            if response.status_code in [200, 201]:
                print(f"✅ {description} completed successfully")
                return True
            else:
                print(f"❌ {description} failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error in {description}: {e}")
            return False
    
    def create_upsert_function(self) -> bool:
        """Create the upsert function for master table"""
        
        upsert_function = """
        CREATE OR REPLACE FUNCTION upsert_master_record(
            p_pdf_hash VARCHAR,
            p_source_exchange VARCHAR,
            p_source_id TEXT,
            p_record_data JSONB
        ) RETURNS VOID AS $$
        DECLARE
            existing_record RECORD;
            update_data JSONB := '{}';
            key TEXT;
            value TEXT;
        BEGIN
            -- Check if record exists
            SELECT * INTO existing_record 
            FROM master_corporate_announcements 
            WHERE pdf_hash = p_pdf_hash;
            
            IF FOUND THEN
                -- Update existing record with merge logic
                FOR key, value IN SELECT * FROM jsonb_each_text(p_record_data)
                LOOP
                    -- Only update if existing value is null or empty
                    IF (existing_record.company_name IS NULL OR existing_record.company_name = '') AND key = 'company_name' THEN
                        update_data := update_data || jsonb_build_object(key, value);
                    ELSIF (existing_record.subject IS NULL OR existing_record.subject = '') AND key = 'subject' THEN
                        update_data := update_data || jsonb_build_object(key, value);
                    ELSIF (existing_record.headline IS NULL OR existing_record.headline = '') AND key = 'headline' THEN
                        update_data := update_data || jsonb_build_object(key, value);
                    ELSIF (existing_record.details IS NULL OR existing_record.details = '') AND key = 'details' THEN
                        update_data := update_data || jsonb_build_object(key, value);
                    END IF;
                END LOOP;
                
                -- Update source tracking
                IF p_source_exchange = 'NSE' THEN
                    update_data := update_data || jsonb_build_object('has_nse_data', true);
                    IF existing_record.nse_source_id IS NULL THEN
                        update_data := update_data || jsonb_build_object('nse_source_id', p_source_id::BIGINT);
                    END IF;
                ELSIF p_source_exchange = 'BSE' THEN
                    update_data := update_data || jsonb_build_object('has_bse_data', true);
                    IF existing_record.bse_source_id IS NULL THEN
                        update_data := update_data || jsonb_build_object('bse_source_id', p_source_id::UUID);
                    END IF;
                END IF;
                
                -- Perform update if there's data to update
                IF jsonb_object_keys(update_data) IS NOT NULL THEN
                    UPDATE master_corporate_announcements 
                    SET 
                        company_name = COALESCE((update_data->>'company_name'), company_name),
                        subject = COALESCE((update_data->>'subject'), subject),
                        headline = COALESCE((update_data->>'headline'), headline),
                        details = COALESCE((update_data->>'details'), details),
                        has_nse_data = COALESCE((update_data->>'has_nse_data')::BOOLEAN, has_nse_data),
                        has_bse_data = COALESCE((update_data->>'has_bse_data')::BOOLEAN, has_bse_data),
                        nse_source_id = COALESCE((update_data->>'nse_source_id')::BIGINT, nse_source_id),
                        bse_source_id = COALESCE((update_data->>'bse_source_id')::UUID, bse_source_id),
                        updated_at = NOW()
                    WHERE pdf_hash = p_pdf_hash;
                END IF;
            ELSE
                -- Insert new record
                INSERT INTO master_corporate_announcements (
                    pdf_hash, source_exchange, has_nse_data, has_bse_data,
                    nse_source_id, bse_source_id, company_name, subject, headline, details
                ) VALUES (
                    p_pdf_hash,
                    p_source_exchange,
                    CASE WHEN p_source_exchange = 'NSE' THEN true ELSE false END,
                    CASE WHEN p_source_exchange = 'BSE' THEN true ELSE false END,
                    CASE WHEN p_source_exchange = 'NSE' THEN p_source_id::BIGINT ELSE NULL END,
                    CASE WHEN p_source_exchange = 'BSE' THEN p_source_id::UUID ELSE NULL END,
                    p_record_data->>'company_name',
                    p_record_data->>'subject',
                    p_record_data->>'headline',
                    p_record_data->>'details'
                );
            END IF;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        return self.execute_sql(upsert_function, "Creating upsert function")
    
    def create_bse_trigger_function(self) -> bool:
        """Create BSE trigger function"""
        
        bse_trigger_function = """
        CREATE OR REPLACE FUNCTION sync_bse_to_master()
        RETURNS TRIGGER AS $$
        BEGIN
            -- Only process non-duplicate, completed records with pdf_hash
            IF NEW.is_duplicate = FALSE AND 
               NEW.duplicate_check_status = 'completed' AND 
               NEW.pdf_hash IS NOT NULL THEN
                
                -- Call upsert function with BSE data
                PERFORM upsert_master_record(
                    NEW.pdf_hash,
                    'BSE',
                    NEW.id::TEXT,
                    jsonb_build_object(
                        'company_name', NEW.company_name,
                        'subject', NEW.subject,
                        'headline', NEW.headline,
                        'details', NEW.more
                    )
                );
            END IF;
            
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        return self.execute_sql(bse_trigger_function, "Creating BSE trigger function")
    
    def create_nse_trigger_function(self) -> bool:
        """Create NSE trigger function"""
        
        nse_trigger_function = """
        CREATE OR REPLACE FUNCTION sync_nse_to_master()
        RETURNS TRIGGER AS $$
        BEGIN
            -- Only process non-duplicate, completed records with pdf_hash
            IF NEW.is_duplicate = FALSE AND 
               NEW.duplicate_check_status = 'completed' AND 
               NEW.pdf_hash IS NOT NULL THEN
                
                -- Call upsert function with NSE data
                PERFORM upsert_master_record(
                    NEW.pdf_hash,
                    'NSE',
                    NEW.id::TEXT,
                    jsonb_build_object(
                        'company_name', NEW.company_name,
                        'subject', NEW.subject,
                        'headline', NEW.subject,
                        'details', NEW.details
                    )
                );
            END IF;
            
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        return self.execute_sql(nse_trigger_function, "Creating NSE trigger function")
    
    def create_bse_trigger(self) -> bool:
        """Create BSE table trigger"""
        
        bse_trigger = """
        DROP TRIGGER IF EXISTS trigger_bse_to_master ON bse_corporate_announcements;
        CREATE TRIGGER trigger_bse_to_master
            AFTER INSERT OR UPDATE OF is_duplicate, duplicate_check_status
            ON bse_corporate_announcements
            FOR EACH ROW
            EXECUTE FUNCTION sync_bse_to_master();
        """
        
        return self.execute_sql(bse_trigger, "Creating BSE table trigger")
    
    def create_nse_trigger(self) -> bool:
        """Create NSE table trigger"""
        
        nse_trigger = """
        DROP TRIGGER IF EXISTS trigger_nse_to_master ON nse_corporate_announcements;
        CREATE TRIGGER trigger_nse_to_master
            AFTER INSERT OR UPDATE OF is_duplicate, duplicate_check_status
            ON nse_corporate_announcements
            FOR EACH ROW
            EXECUTE FUNCTION sync_nse_to_master();
        """
        
        return self.execute_sql(nse_trigger, "Creating NSE table trigger")
    
    def test_trigger_functionality(self) -> bool:
        """Test the trigger functionality"""
        
        print("🧪 Testing trigger functionality...")
        
        # Test with a sample update to BSE table
        test_update = """
        UPDATE bse_corporate_announcements 
        SET duplicate_check_status = 'completed', is_duplicate = false
        WHERE pdf_hash IS NOT NULL 
        AND duplicate_check_status != 'completed'
        LIMIT 1;
        """
        
        success = self.execute_sql(test_update, "Testing BSE trigger with sample update")
        
        if success:
            print("✅ Trigger test completed - check master table for new records")
        
        return success
    
    def create_all_triggers(self) -> bool:
        """Create all triggers and functions"""
        
        print("🔧 Creating Master Table Triggers and Functions")
        print("=" * 60)
        
        steps = [
            ("Creating upsert function", self.create_upsert_function),
            ("Creating BSE trigger function", self.create_bse_trigger_function),
            ("Creating NSE trigger function", self.create_nse_trigger_function),
            ("Creating BSE table trigger", self.create_bse_trigger),
            ("Creating NSE table trigger", self.create_nse_trigger)
        ]
        
        success_count = 0
        
        for step_name, step_function in steps:
            print(f"\n{len([s for s in steps if steps.index(s) <= steps.index((step_name, step_function))])}️⃣ {step_name}...")
            if step_function():
                success_count += 1
            else:
                print(f"❌ Failed: {step_name}")
            
            # Small delay between operations
            time.sleep(2)
        
        print(f"\n📊 Results: {success_count}/{len(steps)} operations completed successfully")
        
        if success_count == len(steps):
            print("🎉 All triggers created successfully!")
            
            # Test functionality
            print("\n🧪 Testing trigger functionality...")
            self.test_trigger_functionality()
            
            return True
        else:
            print("⚠️ Some operations failed - triggers may not work correctly")
            return False


def main():
    """Main function to create triggers"""
    
    print("🏢 Master Corporate Announcements Trigger Setup")
    print("=" * 60)
    print("This will create database triggers to automatically sync NSE and BSE")
    print("corporate announcements to the master table when duplicate check is completed.")
    print()
    
    # Confirm with user
    response = input("Do you want to proceed with trigger creation? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled by user")
        return
    
    # Create triggers
    manager = MasterTableTriggerManager()
    success = manager.create_all_triggers()
    
    if success:
        print("\n✅ Trigger setup completed successfully!")
        print("\n💡 What happens now:")
        print("   - When BSE records are marked as non-duplicate and completed,")
        print("     they will automatically be synced to master table")
        print("   - When NSE records are marked as non-duplicate and completed,")
        print("     they will automatically be synced to master table")
        print("   - Duplicate pdf_hash records will be merged intelligently")
        print("   - Source tracking flags will be updated automatically")
    else:
        print("\n❌ Trigger setup had some failures")
        print("   - Check the error messages above")
        print("   - Some triggers may need to be created manually")


if __name__ == "__main__":
    main()
