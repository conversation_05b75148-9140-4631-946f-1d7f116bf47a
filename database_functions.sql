-- Database functions for PDF duplicate detection system

-- Function to trigger duplicate check (sets status to pending)
CREATE OR REPLACE FUNCTION trigger_duplicate_check()
RETURNS TRIGGER AS $$
DECLARE
    pdf_url_value TEXT;
BEGIN
    -- Determine PDF URL based on table
    IF TG_TABLE_NAME = 'bse_corporate_announcements' THEN
        pdf_url_value := NEW.attachmentfile;
    ELSIF TG_TABLE_NAME = 'nse_corporate_announcements' THEN
        pdf_url_value := NEW.attachment_url;
    ELSE
        RETURN NEW;
    END IF;

    -- Only process if PDF URL exists and is not empty
    IF pdf_url_value IS NOT NULL 
       AND TRIM(pdf_url_value) != '' 
       AND pdf_url_value NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined') THEN
        
        -- Set status to pending for new records
        NEW.duplicate_check_status := 'pending';
        
        -- Note: We'll handle the HTTP call to <PERSON> Function from application level
        -- to avoid blocking the insert operation
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to get duplicate statistics
CREATE OR REPLACE FUNCTION get_duplicate_statistics()
RETURNS TABLE(
    table_name TEXT,
    total_records BIGINT,
    records_with_pdfs BIGINT,
    duplicate_records BIGINT,
    unique_pdfs BIGINT,
    pending_checks BIGINT,
    completed_checks BIGINT,
    failed_checks BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'bse_corporate_announcements'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(CASE WHEN attachmentfile IS NOT NULL AND TRIM(attachmentfile) != '' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN is_duplicate = TRUE THEN 1 END)::BIGINT,
        COUNT(DISTINCT pdf_hash)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'pending' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'completed' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'failed' THEN 1 END)::BIGINT
    FROM bse_corporate_announcements
    
    UNION ALL
    
    SELECT 
        'nse_corporate_announcements'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(CASE WHEN attachment_url IS NOT NULL AND TRIM(attachment_url) != '' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN is_duplicate = TRUE THEN 1 END)::BIGINT,
        COUNT(DISTINCT pdf_hash)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'pending' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'completed' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN duplicate_check_status = 'failed' THEN 1 END)::BIGINT
    FROM nse_corporate_announcements;
END;
$$ LANGUAGE plpgsql;

-- Function to reset failed duplicate checks (for retry)
CREATE OR REPLACE FUNCTION reset_failed_duplicate_checks(max_age_hours INTEGER DEFAULT 24)
RETURNS INTEGER AS $$
DECLARE
    cutoff_time TIMESTAMP WITH TIME ZONE;
    reset_count INTEGER := 0;
BEGIN
    cutoff_time := NOW() - (max_age_hours || ' hours')::INTERVAL;
    
    -- Reset BSE failed checks
    UPDATE bse_corporate_announcements 
    SET duplicate_check_status = 'pending',
        updated_at = NOW()
    WHERE duplicate_check_status = 'failed' 
    AND updated_at < cutoff_time;
    
    GET DIAGNOSTICS reset_count = ROW_COUNT;
    
    -- Reset NSE failed checks
    UPDATE nse_corporate_announcements 
    SET duplicate_check_status = 'pending',
        updated_at = NOW()
    WHERE duplicate_check_status = 'failed' 
    AND updated_at < cutoff_time;
    
    GET DIAGNOSTICS reset_count = reset_count + ROW_COUNT;
    
    RETURN reset_count;
END;
$$ LANGUAGE plpgsql;

-- Function to find potential duplicates by URL (fast check)
CREATE OR REPLACE FUNCTION find_url_duplicates()
RETURNS TABLE(
    pdf_url TEXT,
    total_occurrences BIGINT,
    bse_count BIGINT,
    nse_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH all_pdfs AS (
        SELECT attachmentfile as pdf_url, 'bse' as source
        FROM bse_corporate_announcements 
        WHERE attachmentfile IS NOT NULL AND TRIM(attachmentfile) != ''
        
        UNION ALL
        
        SELECT attachment_url as pdf_url, 'nse' as source
        FROM nse_corporate_announcements 
        WHERE attachment_url IS NOT NULL AND TRIM(attachment_url) != ''
    ),
    url_counts AS (
        SELECT 
            pdf_url,
            COUNT(*) as total_occurrences,
            COUNT(CASE WHEN source = 'bse' THEN 1 END) as bse_count,
            COUNT(CASE WHEN source = 'nse' THEN 1 END) as nse_count
        FROM all_pdfs
        GROUP BY pdf_url
        HAVING COUNT(*) > 1
    )
    SELECT 
        url_counts.pdf_url,
        url_counts.total_occurrences,
        url_counts.bse_count,
        url_counts.nse_count
    FROM url_counts
    ORDER BY total_occurrences DESC;
END;
$$ LANGUAGE plpgsql;
