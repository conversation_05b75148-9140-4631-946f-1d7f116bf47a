-- Database trigger setup for PDF duplicate detection
-- This creates triggers that will call the Edge Function when records are inserted

-- First, enable the http extension if not already enabled
CREATE EXTENSION IF NOT EXISTS http;

-- <PERSON>reate function to call Edge Function for duplicate detection
CREATE OR REPLACE FUNCTION call_duplicate_detector()
RETURNS TRIGGER AS $$
DECLARE
    pdf_url_value TEXT;
    payload JSONB;
    response_status INTEGER;
BEGIN
    -- Determine PDF URL based on table
    IF TG_TABLE_NAME = 'bse_corporate_announcements' THEN
        pdf_url_value := NEW.attachmentfile;
    ELSIF TG_TABLE_NAME = 'nse_corporate_announcements' THEN
        pdf_url_value := NEW.attachment_url;
    ELSE
        RETURN NEW;
    END IF;

    -- Only process if PDF URL exists and is not empty/null
    IF pdf_url_value IS NOT NULL 
       AND TRIM(pdf_url_value) != '' 
       AND pdf_url_value NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined') THEN
        
        -- Set status to pending
        NEW.duplicate_check_status := 'pending';
        
        -- Prepare payload for Edge Function
        payload := jsonb_build_object(
            'record_id', NEW.id::TEXT,
            'table_name', TG_TABLE_NAME,
            'pdf_url', pdf_url_value
        );
        
        -- Log the trigger execution
        RAISE NOTICE 'Calling duplicate detector for % record % with URL %', TG_TABLE_NAME, NEW.id, pdf_url_value;
        
        -- Call Edge Function asynchronously (fire and forget)
        -- Note: This requires the http extension and proper configuration
        BEGIN
            SELECT status INTO response_status
            FROM http((
                'POST',
                'https://yvuwseolouiqhoxsieop.supabase.co/functions/v1/pdf-duplicate-detector',
                ARRAY[
                    http_header('Content-Type', 'application/json'),
                    http_header('Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key', true))
                ],
                'application/json',
                payload::TEXT
            )::http_request);
            
            RAISE NOTICE 'Edge Function called with status: %', response_status;
            
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the insert
            RAISE WARNING 'Failed to call duplicate detector Edge Function: %', SQLERRM;
        END;
        
    ELSE
        -- No PDF URL or invalid URL, mark as completed
        NEW.duplicate_check_status := 'completed';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for both tables
DROP TRIGGER IF EXISTS bse_duplicate_detector_trigger ON bse_corporate_announcements;
CREATE TRIGGER bse_duplicate_detector_trigger
    BEFORE INSERT ON bse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION call_duplicate_detector();

DROP TRIGGER IF EXISTS nse_duplicate_detector_trigger ON nse_corporate_announcements;
CREATE TRIGGER nse_duplicate_detector_trigger
    BEFORE INSERT ON nse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION call_duplicate_detector();

-- Alternative approach: Use pg_net extension (if available)
-- This is more reliable for HTTP calls from PostgreSQL

CREATE OR REPLACE FUNCTION call_duplicate_detector_pgnet()
RETURNS TRIGGER AS $$
DECLARE
    pdf_url_value TEXT;
    payload JSONB;
BEGIN
    -- Determine PDF URL based on table
    IF TG_TABLE_NAME = 'bse_corporate_announcements' THEN
        pdf_url_value := NEW.attachmentfile;
    ELSIF TG_TABLE_NAME = 'nse_corporate_announcements' THEN
        pdf_url_value := NEW.attachment_url;
    ELSE
        RETURN NEW;
    END IF;

    -- Only process if PDF URL exists and is not empty/null
    IF pdf_url_value IS NOT NULL 
       AND TRIM(pdf_url_value) != '' 
       AND pdf_url_value NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined') THEN
        
        -- Set status to pending
        NEW.duplicate_check_status := 'pending';
        
        -- Prepare payload for Edge Function
        payload := jsonb_build_object(
            'record_id', NEW.id::TEXT,
            'table_name', TG_TABLE_NAME,
            'pdf_url', pdf_url_value
        );
        
        -- Call Edge Function using pg_net (if available)
        BEGIN
            PERFORM net.http_post(
                url := 'https://yvuwseolouiqhoxsieop.supabase.co/functions/v1/pdf-duplicate-detector',
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key', true)
                ),
                body := payload
            );
            
            RAISE NOTICE 'Edge Function called via pg_net for % record %', TG_TABLE_NAME, NEW.id;
            
        EXCEPTION WHEN OTHERS THEN
            -- Log error but don't fail the insert
            RAISE WARNING 'Failed to call duplicate detector via pg_net: %', SQLERRM;
        END;
        
    ELSE
        -- No PDF URL or invalid URL, mark as completed
        NEW.duplicate_check_status := 'completed';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Simple trigger that just sets status (recommended approach)
-- The actual Edge Function call should be handled by the application layer
CREATE OR REPLACE FUNCTION set_duplicate_status()
RETURNS TRIGGER AS $$
DECLARE
    pdf_url_value TEXT;
BEGIN
    -- Determine PDF URL based on table
    IF TG_TABLE_NAME = 'bse_corporate_announcements' THEN
        pdf_url_value := NEW.attachmentfile;
    ELSIF TG_TABLE_NAME = 'nse_corporate_announcements' THEN
        pdf_url_value := NEW.attachment_url;
    ELSE
        RETURN NEW;
    END IF;

    -- Set appropriate status based on PDF URL presence
    IF pdf_url_value IS NOT NULL 
       AND TRIM(pdf_url_value) != '' 
       AND pdf_url_value NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined') THEN
        NEW.duplicate_check_status := 'pending';
    ELSE
        NEW.duplicate_check_status := 'completed';
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Use the simple trigger approach (recommended)
DROP TRIGGER IF EXISTS bse_duplicate_status_trigger ON bse_corporate_announcements;
CREATE TRIGGER bse_duplicate_status_trigger
    BEFORE INSERT ON bse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION set_duplicate_status();

DROP TRIGGER IF EXISTS nse_duplicate_status_trigger ON nse_corporate_announcements;
CREATE TRIGGER nse_duplicate_status_trigger
    BEFORE INSERT ON nse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION set_duplicate_status();

-- Verify triggers are created
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_name LIKE '%duplicate%'
ORDER BY event_object_table, trigger_name;
