# Scrapers CI/CD Deployment Guide

This guide covers the complete deployment process for the three scrapers (NSE Insider Trading, NSE Corporate Announcements, and BSE Insider Trading) to Google Cloud Platform using a two-phase approach.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Phase 1: Manual Deployment](#phase-1-manual-deployment)
- [Phase 2: Automated CI/CD](#phase-2-automated-cicd)
- [Configuration Management](#configuration-management)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

The deployment architecture consists of:

- **3 Cloud Run Jobs**: One for each scraper (NSE insider, NSE corporate, BSE insider)
- **3 Cloud Scheduler Jobs**: Automated triggers for each scraper
- **Docker Images**: Stored in Google Artifact Registry
- **Service Accounts**: Secure access with minimal permissions
- **Secrets Management**: Supabase credentials stored in Secret Manager

### Architecture Diagram

```
GitHub Repository
       ↓
   GitHub Actions (Phase 2)
       ↓
Google Artifact Registry
       ↓
   Cloud Run Jobs ←── Cloud Scheduler
       ↓
   Supabase Database
```

## 🔧 Prerequisites

### Required Tools

1. **Google Cloud SDK** (gcloud CLI)
   ```bash
   # Install gcloud CLI
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   gcloud init
   ```

2. **Docker**
   ```bash
   # Install Docker
   sudo apt-get update
   sudo apt-get install docker.io
   sudo usermod -aG docker $USER
   ```

3. **jq** (JSON processor)
   ```bash
   # Ubuntu/Debian
   sudo apt-get install jq
   
   # macOS
   brew install jq
   ```

### Google Cloud Setup

1. **Create or Select Project**
   ```bash
   # Create new project
   gcloud projects create webscrapers-463817 --name="Web Scrapers"
   
   # Set as default project
   gcloud config set project webscrapers-463817
   ```

2. **Enable Billing**
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Navigate to Billing and link a billing account

3. **Authentication**
   ```bash
   # Authenticate with your Google account
   gcloud auth login
   
   # Set application default credentials
   gcloud auth application-default login
   ```

### Supabase Setup

1. **Get Supabase Credentials**
   - Go to your Supabase project dashboard
   - Navigate to Settings > API
   - Copy the Project URL and anon/public key

2. **Prepare Secret Files**
   ```bash
   # Create secrets directory
   mkdir -p secrets/
   
   # Create secret files
   echo "your-supabase-anon-key" > secrets/supabase-anon-key.txt
   echo "your-supabase-service-key" > secrets/supabase-service-key.txt
   ```

## 🚀 Phase 1: Manual Deployment

Phase 1 involves deploying from your local machine using gcloud CLI scripts.

### Step 1: Infrastructure Setup

```bash
# Make scripts executable
chmod +x scripts/*.sh

# Run infrastructure setup
./scripts/deploy-phase1.sh
```

This script will:
- Enable required Google Cloud APIs
- Create Artifact Registry repository
- Create service accounts with proper IAM roles
- Create Secret Manager secrets

### Step 2: Set Secret Values

```bash
# Set Supabase secrets
gcloud secrets versions add supabase-anon-key --data-file=secrets/supabase-anon-key.txt
gcloud secrets versions add supabase-service-key --data-file=secrets/supabase-service-key.txt
```

### Step 3: Build and Push Images

```bash
# Build and push Docker images to Artifact Registry
./scripts/build-and-push.sh
```

This script will:
- Configure Docker authentication
- Build Docker images for all three scrapers
- Push images to Artifact Registry with timestamps and latest tags
- Verify images in registry

### Step 4: Deploy Cloud Run Jobs

```bash
# Deploy Cloud Run jobs
./scripts/deploy-jobs.sh
```

This script will:
- Create Cloud Run jobs for each scraper
- Configure resource limits and environment variables
- Set up proper service account permissions
- Verify deployments

### Step 5: Setup Cloud Scheduler

```bash
# Setup automated scheduling
./scripts/setup-scheduler.sh
```

This script will:
- Create Cloud Scheduler jobs for each scraper
- Configure cron schedules based on config file
- Set up retry policies and error handling
- Optionally test scheduler jobs

### Step 6: Verify Deployment

```bash
# Test manual job execution
gcloud run jobs execute bse-insider-trading-scraper --region=asia-south1
gcloud run jobs execute nse-insider-trading-scraper --region=asia-south1
gcloud run jobs execute nse-corporate-announcements-scraper --region=asia-south1

# Check job status
gcloud run jobs describe bse-insider-trading-scraper --region=asia-south1
```

## 🤖 Phase 2: Automated CI/CD

Phase 2 sets up automated deployment using GitHub Actions.

### Step 1: GitHub Secrets Setup

Add the following secrets to your GitHub repository:

1. **GCP_PROJECT_ID**: Your Google Cloud project ID
   ```
   webscrapers-463817
   ```

2. **GCP_SA_KEY**: Service account key JSON
   ```bash
   # Create service account key
   gcloud iam service-accounts keys create github-actions-key.json \
     --iam-account=<EMAIL>
   
   # Copy the JSON content to GitHub secret
   cat github-actions-key.json
   ```

### Step 2: Repository Setup

1. **Add GitHub Secrets**
   - Go to your repository Settings > Secrets and variables > Actions
   - Add `GCP_PROJECT_ID` and `GCP_SA_KEY`

2. **Verify Workflow File**
   - Ensure `.github/workflows/deploy-scrapers.yml` is in your repository
   - Check that the workflow has proper permissions

### Step 3: Trigger Deployment

The GitHub Actions workflow will trigger automatically on:
- Push to `main` branch (when scraper files change)
- Manual dispatch from GitHub Actions tab

```bash
# Push changes to trigger deployment
git add .
git commit -m "Deploy scrapers to GCP"
git push origin main
```

### Step 4: Monitor Deployment

1. **GitHub Actions**
   - Go to your repository's Actions tab
   - Monitor the deployment workflow progress

2. **Google Cloud Console**
   - Check Cloud Run jobs: https://console.cloud.google.com/run/jobs
   - Monitor Cloud Scheduler: https://console.cloud.google.com/cloudscheduler
   - View logs: https://console.cloud.google.com/logs

## ⚙️ Configuration Management

### Scraper Configuration

The main configuration file is `config/scrapers-config.json`:

```json
{
  "global": {
    "project_id": "webscrapers-463817",
    "region": "asia-south1"
  },
  "scrapers": {
    "bse-insider": {
      "schedule": {
        "cron": "0 1 * * *",
        "enabled": true
      }
    }
  }
}
```

### Schedule Configuration

Each scraper has its own schedule configuration:

- **BSE Insider**: Daily at 1:00 AM IST (`0 1 * * *`)
- **NSE Insider**: Daily at 1:15 AM IST (`15 1 * * *`)
- **NSE Corporate**: Daily at 1:30 AM IST (`30 1 * * *`)

### Resource Configuration

Cloud Run job resources are configured per environment:

```json
{
  "environment": {
    "cloud_run": {
      "cpu": "1",
      "memory": "1Gi",
      "max_instances": 3,
      "timeout": 1800
    }
  }
}
```

## 📊 Monitoring and Maintenance

### Monitoring Dashboard

1. **Cloud Run Jobs**
   - URL: https://console.cloud.google.com/run/jobs?project=webscrapers-463817
   - Monitor execution history, success/failure rates
   - Check resource utilization

2. **Cloud Scheduler**
   - URL: https://console.cloud.google.com/cloudscheduler?project=webscrapers-463817
   - View scheduled jobs and their status
   - Monitor trigger history

3. **Cloud Logging**
   - URL: https://console.cloud.google.com/logs/query?project=webscrapers-463817
   - Filter logs by scraper type:
     ```
     resource.type="cloud_run_job"
     labels."run.googleapis.com/job_name"="bse-insider-trading-scraper"
     ```

### Key Metrics to Monitor

1. **Job Success Rate**: Should be > 95%
2. **Execution Duration**: Should be < 30 minutes per job
3. **Error Rate**: Should be < 5%
4. **Resource Utilization**: CPU and memory usage

### Alerting Setup

Configure alerts for:
- Job failures
- Long-running jobs (> 45 minutes)
- High error rates (> 10%)

```bash
# Example: Create alert policy for job failures
gcloud alpha monitoring policies create --policy-from-file=monitoring/job-failure-alert.yaml
```

### Regular Maintenance Tasks

1. **Weekly**:
   - Review job execution logs
   - Check for any failed runs
   - Monitor resource usage trends

2. **Monthly**:
   - Update Docker images with security patches
   - Review and optimize resource allocations
   - Clean up old Cloud Run revisions

3. **Quarterly**:
   - Review and update IAM permissions
   - Audit secret access and rotation
   - Performance optimization review

## 🔧 Troubleshooting

### Common Issues

#### 1. Job Execution Failures

**Symptoms**: Cloud Run job fails with exit code 1

**Diagnosis**:
```bash
# Check job logs
gcloud logging read "resource.type=cloud_run_job AND labels.job_name=bse-insider-trading-scraper" --limit=50

# Check job configuration
gcloud run jobs describe bse-insider-trading-scraper --region=asia-south1
```

**Solutions**:
- Check Supabase connectivity
- Verify secret access permissions
- Review scraper-specific error logs

#### 2. Scheduler Not Triggering

**Symptoms**: Scheduled jobs not executing

**Diagnosis**:
```bash
# Check scheduler job status
gcloud scheduler jobs describe bse-insider-trading-scraper-scheduler --location=asia-south1

# Check scheduler logs
gcloud logging read "resource.type=cloud_scheduler_job" --limit=20
```

**Solutions**:
- Verify scheduler service account permissions
- Check Cloud Run job URL configuration
- Ensure scheduler job is enabled

#### 3. Image Build Failures

**Symptoms**: GitHub Actions fails during image build

**Diagnosis**:
- Check GitHub Actions workflow logs
- Verify Dockerfile syntax
- Check base image availability

**Solutions**:
- Update base image versions
- Fix Dockerfile syntax errors
- Check Docker registry permissions

#### 4. Permission Denied Errors

**Symptoms**: Service account permission errors

**Diagnosis**:
```bash
# Check service account permissions
gcloud projects get-iam-policy webscrapers-463817 --flatten="bindings[].members" --filter="bindings.members:serviceAccount:<EMAIL>"
```

**Solutions**:
- Re-run infrastructure setup script
- Manually assign missing IAM roles
- Verify service account key validity

### Emergency Procedures

#### 1. Disable All Schedulers

```bash
# Disable all scheduler jobs
for job in $(gcloud scheduler jobs list --location=asia-south1 --format="value(name)"); do
  gcloud scheduler jobs pause "$job" --location=asia-south1
done
```

#### 2. Rollback Deployment

```bash
# List previous revisions
gcloud run revisions list --service=bse-insider-trading-scraper --region=asia-south1

# Rollback to previous revision
gcloud run services update-traffic bse-insider-trading-scraper --to-revisions=REVISION-NAME=100 --region=asia-south1
```

#### 3. Manual Job Execution

```bash
# Execute job manually for testing
gcloud run jobs execute bse-insider-trading-scraper --region=asia-south1 --wait
```

### Getting Help

1. **Check Documentation**: Review this guide and Google Cloud documentation
2. **Check Logs**: Always start with Cloud Logging for detailed error information
3. **GitHub Issues**: Create issues in the repository for deployment-specific problems
4. **Google Cloud Support**: For infrastructure-related issues

---

## 📚 Additional Resources

- [Google Cloud Run Jobs Documentation](https://cloud.google.com/run/docs/create-jobs)
- [Cloud Scheduler Documentation](https://cloud.google.com/scheduler/docs)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)

---

*Last updated: 2025-01-07*
