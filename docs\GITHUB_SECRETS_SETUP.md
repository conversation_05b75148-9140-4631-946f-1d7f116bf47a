# GitHub Secrets Setup for Phase 2 CI/CD

## Required GitHub Secrets

To enable automated deployment via GitHub Actions, you need to configure the following secrets in your GitHub repository:

### 1. GCP_PROJECT_ID
- **Value:** `webscrapers-463817`
- **Description:** Your Google Cloud Project ID

### 2. GCP_SA_KEY
- **Value:** Service Account JSON key (see instructions below)
- **Description:** Google Cloud Service Account credentials for GitHub Actions

## Creating the Service Account Key

### Step 1: Create a Service Account for GitHub Actions
```bash
# Create service account
gcloud iam service-accounts create github-actions-deployer \
    --description="Service account for GitHub Actions CI/CD" \
    --display-name="GitHub Actions Deployer"

# Assign necessary roles
gcloud projects add-iam-policy-binding webscrapers-463817 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/run.admin"

gcloud projects add-iam-policy-binding webscrapers-463817 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/artifactregistry.admin"

gcloud projects add-iam-policy-binding webscrapers-463817 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/cloudscheduler.admin"

gcloud projects add-iam-policy-binding webscrapers-463817 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountUser"
```

### Step 2: Generate and Download the Key
```bash
# Create and download the service account key
gcloud iam service-accounts keys create github-actions-key.json \
    --iam-account=<EMAIL>
```

### Step 3: Add the Key to GitHub Secrets
1. Open the downloaded `github-actions-key.json` file
2. Copy the entire JSON content
3. Go to your GitHub repository → Settings → Secrets and variables → Actions
4. Click "New repository secret"
5. Name: `GCP_SA_KEY`
6. Value: Paste the entire JSON content
7. Click "Add secret"

## Adding Secrets to GitHub Repository

### Via GitHub Web Interface:
1. Go to `https://github.com/Pkeday-Advisors/Scrapers-scripts`
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret**
5. Add each secret:

| Secret Name | Value |
|-------------|-------|
| `GCP_PROJECT_ID` | `webscrapers-463817` |
| `GCP_SA_KEY` | (JSON content from Step 2) |

### Via GitHub CLI (if you have it installed):
```bash
# Set project ID
gh secret set GCP_PROJECT_ID --body "webscrapers-463817"

# Set service account key (from file)
gh secret set GCP_SA_KEY --body-file github-actions-key.json
```

## Testing the Setup

Once secrets are configured, you can test the CI/CD pipeline:

### Manual Trigger:
1. Go to your repository on GitHub
2. Click **Actions** tab
3. Select **Deploy Scrapers to Google Cloud Platform**
4. Click **Run workflow**
5. Choose branch: `main`
6. Click **Run workflow**

### Automatic Trigger:
The workflow will automatically run when you:
- Push changes to `main` branch
- Modify files in: `nse_scraper/`, `bse_scraper/`, `shared/`, `config/`, or `requirements.txt`

## Security Notes

- ✅ The service account has minimal required permissions
- ✅ The JSON key is stored securely in GitHub Secrets
- ✅ Never commit the `github-actions-key.json` file to your repository
- ✅ Delete the local key file after uploading to GitHub: `rm github-actions-key.json`

## Troubleshooting

### Common Issues:
1. **Permission Denied**: Ensure all IAM roles are assigned correctly
2. **Invalid JSON**: Make sure the entire JSON key is copied without truncation
3. **Project Not Found**: Verify the project ID is correct

### Checking Deployment Status:
- **GitHub Actions**: Check the Actions tab for workflow runs
- **Google Cloud Console**: Monitor Cloud Run jobs and Cloud Scheduler
- **Logs**: View execution logs in Google Cloud Logging

---

**Once configured, your scrapers will automatically deploy whenever you push changes to the main branch!** 🚀
