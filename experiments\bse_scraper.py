#!/usr/bin/env python3
"""
BSE Insider Trading Scraper with Supabase Integration
Scrapes BSE insider trading data and stores it directly in Supabase database.
"""

# Suppress urllib3 OpenSSL warnings before importing
# import warnings
# warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL 1.1.1+')

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import random
import re
from datetime import datetime, timedelta
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import urllib3
import json
from typing import Dict, List, Optional, Tuple
import os

# Additional warning suppression
# warnings.filterwarnings('ignore', message='NotOpenSSLWarning')
# urllib3.disable_warnings()

# Supabase configuration
SUPABASE_URL = "https://ikcpoevsbbqfyxnmlmgl.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlrY3BvZXZzYmJxZnl4bm1sbWdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMDc2MzMsImV4cCI6MjA2NjU4MzYzM30.1SGjVa7I_BgOO__d2WnGo22qgIs9BM3FUOVWF2__laE"# Set as environment variable

class BSEInsiderTradingScraper:
    def __init__(self):
        self.url = "https://www.bseindia.com/corporates/Insider_Trading_new.aspx"
        self.session = self._setup_session()
        self.supabase = self._setup_supabase()
        
    def _setup_session(self):
        """Setup requests session with retry strategy and headers."""
        session = requests.Session()
        
        # User agent rotation for better anti-bot evasion
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        ]

        # Headers to mimic a real browser
        headers = {
            "User-Agent": random.choice(user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "max-age=0",
            "Connection": "keep-alive",
            "Origin": "https://www.bseindia.com",
            "Referer": self.url,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1",
        }
        
        # Cookies for session persistence
        cookies = {
            "_ga": "GA1.1.1178654004.1750527647",
            "_ga_TM52BJH9HF": "GS2.1.s1750527646$o1$g1$t1750529155$j59$l0$h0"
        }
        
        # Retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("https://", adapter)
        session.mount("http://", adapter)
        session.cookies.update(cookies)
        session.headers.update(headers)
        
        return session

    def _make_request(self, method, url, max_retries=3, **kwargs):
        """Make direct HTTP request with enhanced error handling and retry logic."""
        for attempt in range(max_retries):
            try:
                # Add random delay to avoid being blocked
                delay = random.uniform(2, 5) + (attempt * 2)  # Increase delay with retries
                print(f"⏳ Waiting {delay:.1f} seconds before request (attempt {attempt + 1}/{max_retries})...")
                time.sleep(delay)

                # Rotate user agent for each retry
                if attempt > 0:
                    user_agents = [
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
                    ]
                    self.session.headers['User-Agent'] = random.choice(user_agents)

                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Check for successful response
                if response.status_code == 200:
                    return response
                elif response.status_code in [429, 503, 504]:
                    print(f"⚠️ Server busy (Status: {response.status_code}), retrying...")
                    continue
                else:
                    response.raise_for_status()

            except requests.exceptions.RequestException as e:
                print(f"❌ Request failed (attempt {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                print("🔄 Retrying with different parameters...")

        raise requests.exceptions.RequestException(f"Failed to complete request after {max_retries} attempts")

    def _setup_supabase(self):
        """Setup Supabase REST API headers."""
        if not SUPABASE_KEY or len(SUPABASE_KEY) < 50:
            print("Warning: Invalid SUPABASE_ANON_KEY")
            return None

        # Return headers for REST API calls
        return {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'resolution=merge-duplicates'  # For upsert functionality
        }
    
    def get_form_parameters(self):
        """Fetch initial page and extract ASP.NET form parameters."""
        print("Fetching initial page to retrieve form parameters...")
        print("Making direct request to BSE...")
        try:
            # Make direct request to BSE
            response = self._make_request('GET', self.url, timeout=30)
            response.raise_for_status()
            print(f"Initial request successful (Status: {response.status_code})")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract ASP.NET form fields
            viewstate = soup.find("input", {"id": "__VIEWSTATE"})["value"]
            event_validation = soup.find("input", {"id": "__EVENTVALIDATION"})["value"]
            viewstate_generator = soup.find("input", {"id": "__VIEWSTATEGENERATOR"})["value"]
            
            print("Successfully retrieved dynamic form parameters")
            return viewstate, event_validation, viewstate_generator
            
            
        except (TypeError, KeyError) as e:
            print(f"Failed to extract form parameters: {e}")
            # Save response for debugging
            with open("bse_initial_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("Saved bse_initial_response.html for debugging")
            raise
        except requests.exceptions.RequestException as e:
            print(f"Initial request failed: {e}")
            raise
    
    def scrape_data(self, days_back=30):
        """Scrape BSE insider trading data for the specified number of days."""
        print(f"🔄 Starting data scraping for last {days_back} days...")

        # Try multiple approaches to get the most complete dataset
        approaches = [
            ("Standard Web Scraping", self.scrape_data_standard),
            ("Alternative Date Ranges", self.scrape_data_alternative_ranges),
        ]

        best_result = pd.DataFrame()

        for approach_name, approach_method in approaches:
            try:
                print(f"\n🔄 Trying approach: {approach_name}")
                result = approach_method(days_back)

                if result is not None and not result.empty:
                    print(f"✅ {approach_name} succeeded with {len(result)} records")
                    if len(result) > len(best_result):
                        print(f"📈 This is the best result so far (previous best: {len(best_result)} records)")
                        best_result = result
                    break
                else:
                    print(f"❌ {approach_name} returned no data")

            except Exception as e:
                print(f"❌ {approach_name} failed: {e}")
                continue

        if best_result.empty:
            print("❌ All scraping approaches failed")
        else:
            print(f"🎉 Best result: {len(best_result)} records using the successful approach")

        return best_result

    def scrape_data_standard(self, days_back=30):
        """Standard BSE scraping approach."""
        # Get form parameters
        viewstate, event_validation, viewstate_generator = self.get_form_parameters()

        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        # Prepare form data
        form_data = {
            "__EVENTTARGET": "",
            "__EVENTARGUMENT": "",
            "__VIEWSTATE": viewstate,
            "__VIEWSTATEGENERATOR": viewstate_generator,
            "__EVENTVALIDATION": event_validation,
            "__VIEWSTATEENCRYPTED": "",
            "ctl00$ContentPlaceHolder1$fmdate": start_date.strftime("%Y%m%d"),
            "ctl00$ContentPlaceHolder1$eddate": end_date.strftime("%Y%m%d"),
            "ctl00$ContentPlaceHolder1$hidCurrentDate": end_date.strftime("%Y/%m/%d"),
            "ctl00$ContentPlaceHolder1$txtDate": start_date.strftime("%d/%m/%Y"),
            "ctl00$ContentPlaceHolder1$txtTodate": end_date.strftime("%d/%m/%Y"),
            "ctl00$ContentPlaceHolder1$hf_scripcode": "",
            "ctl00$ContentPlaceHolder1$btnSubmit": "Submit",
            "ctl00$ContentPlaceHolder1$SmartSearch$hdnCode": "",
            "ctl00$ContentPlaceHolder1$SmartSearch$smartSearch": "",
            "ctl00$ContentPlaceHolder1$ctl00_ContentPlaceHolder1_hdnCode": "",
            "ctl00$ContentPlaceHolder1$rdoMode": "0",
            "ddlPeriod": "Day",
            "ddlRange": "1M",
            "txtScripCode": "",
            "txtCompany": "",
            "txtName": "",
        }

        print(f"📅 Searching for insider trading data from {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")

        # Add delay to avoid rate limiting
        print("⏳ Waiting 3 seconds before submitting...")
        time.sleep(3)

        # Submit form
        print("📤 Submitting form to retrieve insider trading data...")
        try:
            # Make direct POST request to BSE
            response = self._make_request('POST', self.url, data=form_data, timeout=60)
            response.raise_for_status()
            print(f"✅ Form submission successful (Status: {response.status_code})")

            # Save response for debugging
            with open("bse_response.html", "w", encoding="utf-8") as f:
                f.write(response.text)
            print("💾 Response saved to bse_response.html for debugging")

            return self.parse_response(response.text)

        except requests.exceptions.Timeout as e:
            print(f"⏰ Request timed out: {e}")
            print("🔄 The BSE server might be slow. Try running the script again later.")
            raise
        except requests.exceptions.RequestException as e:
            print(f"❌ Form submission failed: {e}")
            raise

    def scrape_data_alternative_ranges(self, days_back=30):
        """Try scraping with smaller date ranges to get more complete data."""
        print(f"🔄 Trying alternative approach: smaller date ranges")

        all_data = []
        end_date = datetime.now()

        # Split the date range into smaller chunks (e.g., 7-day periods)
        chunk_size = 7
        current_end = end_date

        for i in range(0, days_back, chunk_size):
            current_start = current_end - timedelta(days=min(chunk_size, days_back - i))

            print(f"📅 Scraping chunk: {current_start.strftime('%d/%m/%Y')} to {current_end.strftime('%d/%m/%Y')}")

            try:
                chunk_data = self.scrape_data_chunk(current_start, current_end)
                if chunk_data is not None and not chunk_data.empty:
                    all_data.append(chunk_data)
                    print(f"✅ Chunk successful: {len(chunk_data)} records")
                else:
                    print(f"⚠️ Chunk returned no data")

                # Small delay between chunks
                time.sleep(2)

            except Exception as e:
                print(f"❌ Chunk failed: {e}")
                continue

            current_end = current_start - timedelta(days=1)

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            # Remove duplicates
            combined_df = combined_df.drop_duplicates()
            print(f"🔗 Combined {len(all_data)} chunks into {len(combined_df)} unique records")
            return combined_df
        else:
            print("❌ No data retrieved from any chunk")
            return pd.DataFrame()

    def scrape_data_chunk(self, start_date, end_date):
        """Scrape data for a specific date range chunk."""
        try:
            # Get fresh form parameters for each chunk
            viewstate, event_validation, viewstate_generator = self.get_form_parameters()

            # Prepare form data for this chunk
            form_data = {
                "__EVENTTARGET": "",
                "__EVENTARGUMENT": "",
                "__VIEWSTATE": viewstate,
                "__VIEWSTATEGENERATOR": viewstate_generator,
                "__EVENTVALIDATION": event_validation,
                "__VIEWSTATEENCRYPTED": "",
                "ctl00$ContentPlaceHolder1$fmdate": start_date.strftime("%Y%m%d"),
                "ctl00$ContentPlaceHolder1$eddate": end_date.strftime("%Y%m%d"),
                "ctl00$ContentPlaceHolder1$hidCurrentDate": end_date.strftime("%Y/%m/%d"),
                "ctl00$ContentPlaceHolder1$txtDate": start_date.strftime("%d/%m/%Y"),
                "ctl00$ContentPlaceHolder1$txtTodate": end_date.strftime("%d/%m/%Y"),
                "ctl00$ContentPlaceHolder1$hf_scripcode": "",
                "ctl00$ContentPlaceHolder1$btnSubmit": "Submit",
                "ctl00$ContentPlaceHolder1$SmartSearch$hdnCode": "",
                "ctl00$ContentPlaceHolder1$SmartSearch$smartSearch": "",
                "ctl00$ContentPlaceHolder1$ctl00_ContentPlaceHolder1_hdnCode": "",
                "ctl00$ContentPlaceHolder1$rdoMode": "0",
                "ddlPeriod": "Day",
                "ddlRange": "1M",
                "txtScripCode": "",
                "txtCompany": "",
                "txtName": "",
            }

            # Submit form for this chunk
            response = self.session.post(self.url, data=form_data, timeout=60)
            response.raise_for_status()

            return self.parse_response(response.text)

        except Exception as e:
            print(f"❌ Chunk scraping failed: {e}")
            return pd.DataFrame()
    
    def parse_response(self, html_content):
        """Parse HTML response and extract insider trading data."""
        print("Parsing response HTML...")
        soup = BeautifulSoup(html_content, 'html.parser')
        table = soup.find("table", {"id": "ContentPlaceHolder1_gvData"})

        if not table:
            print("Data table not found. Check bse_response.html for details.")
            # Check for error messages
            error_div = soup.find("div", {"id": "ContentPlaceHolder1_lblError"})
            if error_div and error_div.text.strip():
                print(f"Server returned error: {error_div.text.strip()}")
            return []

        print("Found data table!")

        # Check for download link to get all records
        download_link = soup.find("a", {"id": "ContentPlaceHolder1_lnkDownload"})
        if download_link:
            print("📥 Found 'Download All Records' link - attempting to download complete dataset...")
            try:
                complete_df = self.download_all_records(soup)
                if complete_df is not None and not complete_df.empty:
                    print(f"✅ Successfully downloaded {len(complete_df)} records from complete dataset")
                    return complete_df
                else:
                    print("⚠️ Download failed, falling back to web page parsing...")
            except Exception as e:
                print(f"⚠️ Download failed with error: {e}, falling back to web page parsing...")

        # Extract headers
        headers = []
        header_row = table.find_all("tr", class_="innertable_header1")[0]
        for th in header_row.find_all(["td", "th"]):
            if th.get("colspan") == "4":
                headers.extend([
                    "Securities Acquired/Disposed - Type of Securities",
                    "Securities Acquired/Disposed - Number",
                    "Securities Acquired/Disposed - Value",
                    "Securities Acquired/Disposed - Transaction Type"
                ])
            elif th.get("colspan") == "3":
                headers.extend([
                    "Trading in Derivatives - Type of Contract",
                    "Trading in Derivatives - Buy Value (Units)",
                    "Trading in Derivatives - Sale Value (Units)"
                ])
            else:
                headers.append(th.text.strip())

        # Extract data rows
        data = []
        data_rows = table.find_all("tr", class_="TTRow")
        print(f"📊 Found {len(data_rows)} data rows in web page table")

        for row in data_rows:
            cols = row.find_all("td")
            row_data = []
            for col in cols:
                # Handle columns with nested spans
                spans = col.find_all("span")
                if len(spans) == 2:
                    # Combine number and percentage
                    row_data.append(f"{spans[0].text.strip()} ({spans[1].text.strip()})")
                else:
                    # Clean text and handle multiline content
                    text = col.text.strip().replace('\n', ' ').replace('\r', ' ')
                    # Remove extra whitespace
                    text = ' '.join(text.split())
                    row_data.append(text)
            data.append(row_data)

        # Create DataFrame
        df = pd.DataFrame(data, columns=headers)
        df = df.dropna(how='all').dropna(axis=1, how='all')

        print(f"📋 Parsed {len(df)} records from web page display")

        # Check if this might be a limited view
        if len(df) > 0:
            print(f"⚠️ Note: Web page shows {len(df)} records. This might be a limited view.")
            print("💡 BSE website may limit web display. Consider using 'Download All Records' for complete data.")

        return df

    def download_all_records(self, soup):
        """Download all records using the BSE download functionality."""
        try:
            # Extract form parameters from the current page
            viewstate = soup.find("input", {"id": "__VIEWSTATE"})["value"]
            event_validation = soup.find("input", {"id": "__EVENTVALIDATION"})["value"]
            viewstate_generator = soup.find("input", {"id": "__VIEWSTATEGENERATOR"})["value"]

            # Get current form values
            from_date = soup.find("input", {"id": "ContentPlaceHolder1_txtDate"})["value"]
            to_date = soup.find("input", {"id": "ContentPlaceHolder1_txtTodate"})["value"]

            print(f"🔄 Triggering download for date range: {from_date} to {to_date}")

            # Prepare form data for download request
            download_form_data = {
                "__EVENTTARGET": "ctl00$ContentPlaceHolder1$lnkDownload",
                "__EVENTARGUMENT": "",
                "__VIEWSTATE": viewstate,
                "__VIEWSTATEGENERATOR": viewstate_generator,
                "__EVENTVALIDATION": event_validation,
                "__VIEWSTATEENCRYPTED": "",
                "ctl00$ContentPlaceHolder1$fmdate": soup.find("input", {"id": "ContentPlaceHolder1_fmdate"})["value"],
                "ctl00$ContentPlaceHolder1$eddate": soup.find("input", {"id": "ContentPlaceHolder1_eddate"})["value"],
                "ctl00$ContentPlaceHolder1$hidCurrentDate": soup.find("input", {"id": "ContentPlaceHolder1_hidCurrentDate"})["value"],
                "ctl00$ContentPlaceHolder1$txtDate": from_date,
                "ctl00$ContentPlaceHolder1$txtTodate": to_date,
                "ctl00$ContentPlaceHolder1$hf_scripcode": "",
                "ctl00$ContentPlaceHolder1$SmartSearch$hdnCode": "",
                "ctl00$ContentPlaceHolder1$SmartSearch$smartSearch": "",
                "ctl00$ContentPlaceHolder1$ctl00_ContentPlaceHolder1_hdnCode": "",
                "ctl00$ContentPlaceHolder1$rdoMode": "0",
                "ddlPeriod": "Day",
                "ddlRange": "1M",
                "txtScripCode": "",
                "txtCompany": "",
                "txtName": "",
            }

            print("📥 Requesting download of all records...")

            # Make download request
            response = self.session.post(self.url, data=download_form_data, timeout=120)
            response.raise_for_status()

            print(f"📊 Download response status: {response.status_code}")
            print(f"📊 Content type: {response.headers.get('content-type', 'unknown')}")
            print(f"📊 Content length: {len(response.content)} bytes")

            # Check if response is Excel file or CSV
            content_type = response.headers.get('content-type', '').lower()

            # Check content to determine actual format
            content_start = response.content[:100].decode('utf-8', errors='ignore')
            is_csv_content = ',' in content_start and ('Security Code' in content_start or 'security_code' in content_start.lower())

            if is_csv_content:
                print("📊 Detected CSV content (may be mislabeled as Excel), parsing as CSV...")
                return self.parse_csv_response(response.content)
            elif 'excel' in content_type or 'spreadsheet' in content_type or response.content.startswith(b'PK'):
                print("📊 Received Excel file, parsing...")
                return self.parse_excel_response(response.content)
            elif 'csv' in content_type or response.content.startswith(b'"'):
                print("📊 Received CSV file, parsing...")
                return self.parse_csv_response(response.content)
            else:
                print("📊 Received HTML response, checking for data...")
                return self.parse_download_html_response(response.text)

        except Exception as e:
            print(f"❌ Error in download_all_records: {e}")
            return None

    def parse_excel_response(self, content):
        """Parse Excel file content."""
        try:
            import io
            # Save content to temporary file for debugging
            with open("bse_download.xlsx", "wb") as f:
                f.write(content)
            print("💾 Saved Excel file as bse_download.xlsx for debugging")

            # Try different Excel engines
            engines = ['openpyxl', 'xlrd', None]

            for engine in engines:
                try:
                    print(f"🔄 Trying Excel engine: {engine or 'auto'}")
                    df = pd.read_excel(io.BytesIO(content), engine=engine)
                    print(f"📊 Successfully parsed Excel file with {len(df)} records using engine: {engine or 'auto'}")

                    # Clean column names
                    df.columns = df.columns.str.strip()

                    # Display column info for debugging
                    print(f"📋 Excel columns: {list(df.columns)}")

                    return df
                except Exception as engine_error:
                    print(f"❌ Engine {engine or 'auto'} failed: {engine_error}")
                    continue

            print("❌ All Excel engines failed")
            return None

        except Exception as e:
            print(f"❌ Error parsing Excel file: {e}")
            return None

    def parse_csv_response(self, content):
        """Parse CSV file content."""
        try:
            import io
            # Save content to temporary file for debugging
            with open("bse_download.csv", "wb") as f:
                f.write(content)
            print("💾 Saved CSV file as bse_download.csv for debugging")

            # Try to read CSV file with different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            df = None

            for encoding in encodings:
                try:
                    print(f"🔄 Trying encoding: {encoding}")
                    df = pd.read_csv(io.BytesIO(content), encoding=encoding)
                    print(f"📊 Successfully parsed CSV file with {len(df)} records using encoding: {encoding}")
                    break
                except UnicodeDecodeError as e:
                    print(f"❌ Encoding {encoding} failed: {e}")
                    continue

            if df is None:
                print("❌ All encodings failed")
                return None

            # Clean column names and standardize them to match web scraping format
            df.columns = df.columns.str.strip()

            # Map CSV columns to database field names based on actual CSV structure
            # Handle both CSV and web scraping column formats
            column_mapping = {
                'Security Code': 'Security Code',
                'Security Name': 'Security Name',
                'Name of Person': 'Name of Person',           # CSV format
                'Name of  Person': 'Name of  Person',         # Web format (double space)
                'Category of person': 'Category of Person',   # CSV format
                'Category of Person *': 'Category of Person *', # Web format (with asterisk)
                'Type of Securities held Prior to acquisition/Disposed)': 'Securities Held Prior Type',
                'Number of Securities held Prior to acquisition/Disposed': 'Securities Held Prior Number',
                '%   of  Securities held Prior to acquisition/Disposed': 'Securities Held Prior Percentage',
                'Type of Securities Acquired/Disposed/Pledge etc.': 'Securities Acquired/Disposed - Type of Securities',
                'Number of Securities Acquired/Disposed/Pledge etc.': 'Securities Acquired/Disposed - Number',
                'Value  of Securities Acquired/Disposed/Pledge etc': 'Securities Acquired/Disposed - Value',
                'Transaction Type ( Buy/Sale/Pledge/Revoke/Invoke)': 'Securities Acquired/Disposed - Transaction Type',
                'Type of Securities held Post  acquisition/Disposed/Pledge  etc': 'Securities Held Post Type',
                'Number of Securities held Post  acquisition/Disposed/Pledge etc': 'Securities Held Post Number',
                'Post-Transaction % of Shareholding': 'Securities Held Post Percentage',
                'Date of acquisition of shares/sale of shares/Date of Allotment(From date)': 'Transaction Date From',
                'Date of acquisition of shares/sale of shares/Date of Allotment( To date  )': 'Transaction Date To',
                'Date of Intimation to Company': 'Date of Intimation to Company',
                'Mode of Acquisition': 'Mode of Acquisition',
                'Trading in Derivative - Type of Contract': 'Trading in Derivatives - Type of Contract',
                'Derivative Contract  Specification': 'Derivative Contract Specification',
                'Derivatives - Buy Value (Notional)': 'Trading in Derivatives - Buy Value (Units)',
                'Derivatives - Buy-Number of Units(Contracts*lot size)': 'Trading in Derivatives - Buy Units',
                'Derivatives - Sales Value (Notional)': 'Trading in Derivatives - Sale Value (Units)',
                'Derivatives - Sales-Number of Units(Contracts * lot size)': 'Trading in Derivatives - Sale Units',
                'Exchange on which the Trade was executed': 'Exchange',
                'Reported to Exchange': 'Reported to Exchange'
            }

            # Note: The CSV already has combined columns 'Period ##', 'Securities held pre  Transaction',
            # and 'Securities held post Transaction', so we don't need to create them

            # Apply column mapping to rename columns
            print(f"📋 Original CSV columns: {list(df.columns)}")
            df = df.rename(columns=column_mapping)
            print(f"🔄 Mapped columns: {list(df.columns)}")

            print(f"📋 CSV columns: {list(df.columns)}")
            print(f"🔄 Standardized column names for processing")

            return df
        except Exception as e:
            print(f"❌ Error parsing CSV file: {e}")
            import traceback
            traceback.print_exc()
            return None

    def parse_download_html_response(self, html_content):
        """Parse HTML response from download request."""
        try:
            # Save response for debugging
            with open("bse_download_response.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("💾 Saved download response as bse_download_response.html for debugging")

            # Parse the HTML response (might contain more data than regular page)
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find("table", {"id": "ContentPlaceHolder1_gvData"})

            if not table:
                print("❌ No data table found in download response")
                return None

            # Use the same parsing logic but for potentially larger dataset
            return self.parse_table_from_soup(soup, table)

        except Exception as e:
            print(f"❌ Error parsing download HTML response: {e}")
            return None

    def parse_table_from_soup(self, soup, table):
        """Extract data from table element."""
        try:
            # Extract headers
            headers = []
            header_row = table.find_all("tr", class_="innertable_header1")[0]
            for th in header_row.find_all(["td", "th"]):
                if th.get("colspan") == "4":
                    headers.extend([
                        "Securities Acquired/Disposed - Type of Securities",
                        "Securities Acquired/Disposed - Number",
                        "Securities Acquired/Disposed - Value",
                        "Securities Acquired/Disposed - Transaction Type"
                    ])
                elif th.get("colspan") == "3":
                    headers.extend([
                        "Trading in Derivatives - Type of Contract",
                        "Trading in Derivatives - Buy Value (Units)",
                        "Trading in Derivatives - Sale Value (Units)"
                    ])
                else:
                    headers.append(th.text.strip())

            # Extract data rows
            data = []
            data_rows = table.find_all("tr", class_="TTRow")
            print(f"📊 Found {len(data_rows)} data rows in download response")

            for row in data_rows:
                cols = row.find_all("td")
                row_data = []
                for col in cols:
                    # Handle columns with nested spans
                    spans = col.find_all("span")
                    if len(spans) == 2:
                        # Combine number and percentage
                        row_data.append(f"{spans[0].text.strip()} ({spans[1].text.strip()})")
                    else:
                        # Clean text and handle multiline content
                        text = col.text.strip().replace('\n', ' ').replace('\r', ' ')
                        # Remove extra whitespace
                        text = ' '.join(text.split())
                        row_data.append(text)
                data.append(row_data)

            # Create DataFrame
            df = pd.DataFrame(data, columns=headers)
            df = df.dropna(how='all').dropna(axis=1, how='all')

            print(f"📊 Successfully parsed {len(df)} records from download response")
            return df

        except Exception as e:
            print(f"❌ Error parsing table from soup: {e}")
            return None

    def parse_securities_held(self, securities_held_str):
        """Parse securities held string to extract number and percentage."""
        # Handle pandas Series (in case of duplicate column names)
        if isinstance(securities_held_str, pd.Series):
            if securities_held_str.empty:
                return None, None
            securities_held_str = securities_held_str.iloc[0]  # Take first value

        # Handle None, NaN, or empty values
        if securities_held_str is None or pd.isna(securities_held_str) or str(securities_held_str).strip() == '':
            return None, None

        try:
            # Convert to string for processing
            securities_str = str(securities_held_str).strip()

            # Extract number (remove commas)
            number_match = re.search(r'^([\d,]+)', securities_str)
            number = int(number_match.group(1).replace(',', '')) if number_match else None

            # Extract percentage from parentheses
            percentage_match = re.search(r'\(([\d.]+)\)', securities_str)
            percentage = float(percentage_match.group(1)) if percentage_match else None

            return number, percentage
        except (ValueError, AttributeError) as e:
            print(f"⚠️ Error parsing securities held '{securities_held_str}': {e}")
            return None, None
    
    def parse_transaction_period(self, period_str):
        """Parse transaction period string to extract from and to dates."""
        if not period_str or pd.isna(period_str):
            return None, None
        
        try:
            dates = str(period_str).split()
            if len(dates) >= 2:
                from_date = datetime.strptime(dates[0], '%d/%m/%Y').date()
                to_date = datetime.strptime(dates[1], '%d/%m/%Y').date()
                return from_date, to_date
            elif len(dates) == 1:
                date = datetime.strptime(dates[0], '%d/%m/%Y').date()
                return date, date
        except ValueError:
            pass
        
        return None, None
    
    def clean_numeric_value(self, value_str):
        """Clean and convert numeric value string to float."""
        if not value_str or pd.isna(value_str):
            return None

        try:
            cleaned = str(value_str).replace(',', '').strip()
            if not cleaned or cleaned == '':
                return None
            return float(cleaned)
        except ValueError:
            return None

    def clean_integer_value(self, value_str):
        """Clean and convert numeric value string to integer."""
        if not value_str or pd.isna(value_str):
            return None

        try:
            cleaned = str(value_str).replace(',', '').strip()
            if not cleaned or cleaned == '':
                return None
            # Convert to float first to handle decimal values, then to int
            float_val = float(cleaned)
            return int(float_val)
        except (ValueError, TypeError):
            return None

    def safe_get_string(self, row, column_name, default=''):
        """Safely extract string value from row, handling pandas Series."""
        value = row.get(column_name, default)

        # Handle pandas Series (in case of duplicate column names)
        if isinstance(value, pd.Series):
            if value.empty:
                return default
            value = value.iloc[0]  # Take first value

        # Handle None, NaN, or empty values
        if value is None or pd.isna(value):
            return default

        return str(value).strip()

    def parse_date_field(self, date_str):
        """Parse date string in various formats."""
        if not date_str or date_str.strip() == '':
            return None

        try:
            # Try format: "25 Jun 2025"
            return datetime.strptime(date_str, '%d %b %Y').date()
        except ValueError:
            try:
                # Try format: "25/06/2025"
                return datetime.strptime(date_str, '%d/%m/%Y').date()
            except ValueError:
                return None

    def process_dataframe_to_records(self, df):
        """Convert DataFrame to database-ready records."""
        processed_records = []

        print(f"Processing {len(df)} rows from DataFrame")

        for _, row in df.iterrows():

            # Parse securities held pre-transaction from individual fields
            pre_number = self.clean_integer_value(
                self.safe_get_string(row, 'Securities Held Prior Number')
            )
            pre_percentage = self.clean_numeric_value(
                self.safe_get_string(row, 'Securities Held Prior Percentage')
            )

            # Parse securities held post-transaction from individual fields
            post_number = self.clean_integer_value(
                self.safe_get_string(row, 'Securities Held Post Number')
            )
            post_percentage = self.clean_numeric_value(
                self.safe_get_string(row, 'Securities Held Post Percentage')
            )

            # Parse transaction period from individual date columns
            from_date = None
            to_date = None

            # Parse from date
            date_from_str = self.safe_get_string(row, 'Transaction Date From')
            if date_from_str:
                try:
                    from_date = datetime.strptime(date_from_str, '%d %b %Y').date()
                except (ValueError, TypeError):
                    try:
                        from_date = datetime.strptime(date_from_str, '%d/%m/%Y').date()
                    except (ValueError, TypeError):
                        pass

            # Parse to date
            date_to_str = self.safe_get_string(row, 'Transaction Date To')
            if date_to_str:
                try:
                    to_date = datetime.strptime(date_to_str, '%d %b %Y').date()
                except (ValueError, TypeError):
                    try:
                        to_date = datetime.strptime(date_to_str, '%d/%m/%Y').date()
                    except (ValueError, TypeError):
                        pass

            # Parse reported date
            reported_date = None
            reported_str = self.safe_get_string(row, 'Reported to Exchange')
            if reported_str:
                try:
                    reported_date = datetime.strptime(reported_str, '%d %b %Y').date()
                except ValueError:
                    try:
                        reported_date = datetime.strptime(reported_str, '%d/%m/%Y').date()
                    except ValueError:
                        pass

            # Clean numeric values using safe extraction
            disposed_number = self.clean_integer_value(
                self.safe_get_string(row, 'Securities Acquired/Disposed - Number')
            )
            disposed_value = self.clean_numeric_value(
                self.safe_get_string(row, 'Securities Acquired/Disposed - Value')
            )

            # Ensure required fields are not empty (handle different column name formats)
            security_code = self.safe_get_string(row, 'Security Code')
            security_name = self.safe_get_string(row, 'Security Name')

            # Handle different person name column formats
            person_name = (self.safe_get_string(row, 'Name of Person') or
                          self.safe_get_string(row, 'Name of  Person') or  # Web format with double space
                          '')

            # Handle different person category column formats
            person_category = (self.safe_get_string(row, 'Category of Person') or
                              self.safe_get_string(row, 'Category of Person *') or  # Web format with asterisk
                              self.safe_get_string(row, 'Category of person') or   # CSV format lowercase
                              '')

            # Skip records with missing required fields
            if not security_code or not security_name or not person_name or not person_category:
                print(f"Skipping record with missing required fields: {security_code}, {security_name}, {person_name}, {person_category}")
                continue

            record = {
                'security_code': security_code,
                'security_name': security_name,
                'person_name': person_name,
                'person_category': person_category,
                'securities_held_pre_transaction': self.safe_get_string(row, 'Securities Held Prior Type'),
                'securities_held_pre_number': pre_number,
                'securities_held_pre_percentage': pre_percentage,
                'securities_acquired_disposed_type': self.safe_get_string(row, 'Securities Acquired/Disposed - Type of Securities'),
                'securities_acquired_disposed_number': disposed_number,
                'securities_acquired_disposed_value': disposed_value,
                'securities_acquired_disposed_transaction_type': self.safe_get_string(row, 'Securities Acquired/Disposed - Transaction Type'),
                'securities_held_post_transaction': self.safe_get_string(row, 'Securities Held Post Type'),
                'securities_held_post_number': post_number,
                'securities_held_post_percentage': post_percentage,
                'transaction_period_from': from_date,
                'transaction_period_to': to_date,
                'mode_of_acquisition': self.safe_get_string(row, 'Mode of Acquisition'),
                'derivatives_type_of_contract': self.safe_get_string(row, 'Trading in Derivatives - Type of Contract'),
                'derivative_contract_specification': self.safe_get_string(row, 'Derivative Contract Specification'),
                'derivatives_buy_value_units': self.safe_get_string(row, 'Trading in Derivatives - Buy Value (Units)'),
                'derivative_buy_units': self.safe_get_string(row, 'Trading in Derivatives - Buy Units'),
                'derivatives_sale_value_units': self.safe_get_string(row, 'Trading in Derivatives - Sale Value (Units)'),
                'derivative_sale_units': self.safe_get_string(row, 'Trading in Derivatives - Sale Units'),
                'exchange': self.safe_get_string(row, 'Exchange'),
                'intimation_date': self.parse_date_field(self.safe_get_string(row, 'Date of Intimation to Company')),
                'reported_to_exchange_date': reported_date,
                'nse_code': security_code  # Use security_code for trigger function compatibility
            }

            # Clean empty strings and replace with None
            for key, value in record.items():
                if value == '' or str(value) == 'nan':
                    record[key] = None

            processed_records.append(record)

        return processed_records

    def serialize_for_json(self, obj):
        """Convert objects to JSON-serializable format."""
        if hasattr(obj, 'isoformat'):  # datetime, date objects
            return obj.isoformat()
        return obj

    def prepare_records_for_api(self, records):
        """Prepare records for JSON serialization."""
        serialized_records = []

        for record in records:
            serialized_record = {}
            for key, value in record.items():
                serialized_record[key] = self.serialize_for_json(value)
            serialized_records.append(serialized_record)

        return serialized_records

    def save_to_database(self, records):
        """Save records to Supabase database using REST API."""
        if not self.supabase:
            print("Supabase headers not initialized. Skipping database save.")
            return False

        if not records:
            print("No records to save.")
            return True

        try:
            print(f"Saving {len(records)} records to database using REST API...")

            # Prepare records for JSON serialization
            serialized_records = self.prepare_records_for_api(records)

            # Supabase REST API endpoint - use original BSE table, not consolidated
            api_url = f"{SUPABASE_URL}/rest/v1/bse_insider_trading"

            # Insert records in batches to avoid timeout
            batch_size = 50
            total_inserted = 0
            total_duplicates = 0

            for i in range(0, len(serialized_records), batch_size):
                batch = serialized_records[i:i + batch_size]
                try:
                    # Use upsert to handle duplicates at database level
                    headers = self.supabase.copy()
                    headers['Prefer'] = 'resolution=ignore-duplicates'

                    response = self.session.post(
                        api_url,
                        headers=headers,
                        data=json.dumps(batch),
                        timeout=60
                    )

                    if response.status_code in [200, 201]:
                        total_inserted += len(batch)
                        print(f"✅ Inserted batch {i//batch_size + 1}: {len(batch)} records")
                    elif response.status_code == 409:
                        # Handle duplicates by trying individual records
                        print(f"⚠️ Batch {i//batch_size + 1} has duplicates, trying individual records...")
                        batch_inserted, batch_duplicates = self.insert_records_individually(batch, api_url)
                        total_inserted += batch_inserted
                        total_duplicates += batch_duplicates
                        print(f"📊 Batch {i//batch_size + 1}: {batch_inserted} new, {batch_duplicates} duplicates")
                    else:
                        print(f"❌ Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}")
                        # If it's a 400 error, try individual records to get more specific error info
                        if response.status_code == 400:
                            print(f"🔍 Trying individual records to identify the problematic data...")
                            batch_inserted, batch_duplicates = self.insert_records_individually(batch, api_url)
                            total_inserted += batch_inserted
                            total_duplicates += batch_duplicates
                        continue

                except Exception as e:
                    print(f"❌ Error inserting batch {i//batch_size + 1}: {e}")
                    continue

            if total_duplicates > 0:
                print(f"🎉 Successfully inserted {total_inserted} new BSE records to database")
                print(f"📊 Skipped {total_duplicates} duplicate records")
            else:
                print(f"🎉 Successfully inserted {total_inserted} BSE records to database")

            return total_inserted > 0

        except Exception as e:
            print(f"💥 Error saving to database: {e}")
            return False

    def insert_records_individually(self, batch, api_url):
        """Insert records one by one to handle duplicates gracefully."""
        inserted_count = 0
        duplicate_count = 0

        for record in batch:
            try:
                # Use upsert headers to handle duplicates gracefully
                headers = self.supabase.copy()
                headers['Prefer'] = 'resolution=ignore-duplicates'

                response = self.session.post(
                    api_url,
                    headers=headers,
                    data=json.dumps([record]),
                    timeout=30
                )

                if response.status_code in [200, 201]:
                    inserted_count += 1
                elif response.status_code == 409:
                    duplicate_count += 1
                    # Silently skip duplicates
                else:
                    print(f"⚠️ Individual record failed: {response.status_code} - {response.text}")
                    # Try to understand what's wrong with this specific record
                    if response.status_code == 400:
                        print(f"🔍 Record causing 400 error: {json.dumps(record, indent=2)}")

            except Exception as e:
                print(f"⚠️ Individual record error: {e}")
                continue

        return inserted_count, duplicate_count

    def run_scraper(self, days_back=30, save_csv=True):
        """Run the complete scraping process."""
        try:
            print("=== BSE Insider Trading Scraper (Enhanced) ===")
            print(f"🔍 Scraping data for the last {days_back} days...")
            print(f"📅 Date range: {(datetime.now() - timedelta(days=days_back)).strftime('%d/%m/%Y')} to {datetime.now().strftime('%d/%m/%Y')}")

            # Scrape data
            df = self.scrape_data(days_back)

            if df.empty:
                print("❌ No data found for the specified date range.")
                print("💡 This could be due to:")
                print("   - No insider trading activity in the date range")
                print("   - BSE website blocking the request")
                print("   - Network connectivity issues")
                return False

            print(f"✅ Successfully retrieved {len(df)} records")

            # Validate data quality
            self.validate_scraped_data(df)

            # Save to CSV if requested
            if save_csv:
                csv_filename = f"bse_insider_trading_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(csv_filename, index=False, encoding='utf-8')
                print(f"💾 Data saved to {csv_filename}")

            # Process and save to database
            print("🔄 Processing records for database insertion...")
            records = self.process_dataframe_to_records(df)

            if len(records) != len(df):
                print(f"⚠️ Warning: {len(df)} raw records processed into {len(records)} valid records")
                print(f"   {len(df) - len(records)} records were filtered out due to missing required fields")

            success = self.save_to_database(records)

            if success:
                print("✅ BSE scraping completed successfully!")
                print(f"📊 Final summary: {len(records)} records saved to database")
            else:
                print("⚠️ BSE scraping completed but database save failed.")

            return success

        except Exception as e:
            print(f"❌ BSE scraping failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def validate_scraped_data(self, df):
        """Validate the quality and completeness of scraped data."""
        print("🔍 Validating scraped data quality...")

        if df.empty:
            print("❌ DataFrame is empty")
            return

        print(f"📊 Data shape: {df.shape[0]} rows × {df.shape[1]} columns")
        print(f"🔍 Available columns: {list(df.columns)}")

        # Check for required columns (handle different naming conventions)
        required_columns_web = ['Security Code', 'Security Name', 'Name of  Person', 'Category of Person *']
        required_columns_csv = ['Security Code', 'Security Name', 'Name of Person', 'Category of person']

        # Check which set of columns is present
        web_columns_present = all(col in df.columns for col in required_columns_web)
        csv_columns_present = all(col in df.columns for col in required_columns_csv)

        if web_columns_present:
            print("✅ All required web scraping columns present")
            required_columns = required_columns_web
        elif csv_columns_present:
            print("✅ All required CSV download columns present")
            required_columns = required_columns_csv
        else:
            missing_web = [col for col in required_columns_web if col not in df.columns]
            missing_csv = [col for col in required_columns_csv if col not in df.columns]
            print(f"⚠️ Missing required columns:")
            print(f"   Web format missing: {missing_web}")
            print(f"   CSV format missing: {missing_csv}")
            required_columns = required_columns_web  # Default to web format for validation

        # Check data completeness
        for col in required_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                empty_count = (df[col] == '').sum()
                total_missing = null_count + empty_count
                if total_missing > 0:
                    print(f"⚠️ Column '{col}': {total_missing}/{len(df)} records have missing values")
                else:
                    print(f"✅ Column '{col}': Complete data")

        # Check for duplicate records
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            print(f"⚠️ Found {duplicate_count} duplicate records")
        else:
            print("✅ No duplicate records found")

        # Sample data preview
        print("📋 Sample of scraped data:")
        if len(df) > 0:
            # Use appropriate column names based on what's available
            sample_cols_web = ['Security Code', 'Security Name', 'Name of  Person']
            sample_cols_csv = ['Security Code', 'Security Name', 'Name of Person']

            # Try web format first, then CSV format
            available_cols = [col for col in sample_cols_web if col in df.columns]
            if not available_cols:
                available_cols = [col for col in sample_cols_csv if col in df.columns]

            if available_cols:
                print(df[available_cols].head(3).to_string(index=False))

        print("✅ Data validation completed")


def main():
    """Main function to run the BSE scraper."""
    scraper = BSEInsiderTradingScraper()

    print("🚀 Starting Enhanced BSE Insider Trading Scraper with Database Integration")
    print(f"📊 Database URL: {SUPABASE_URL}")
    print(f"🔑 API Key: {SUPABASE_KEY[:20]}...{SUPABASE_KEY[-10:]}")
    print("🔧 Enhanced Features:")
    print("   • Download All Records functionality")
    print("   • Alternative date range scraping")
    print("   • Comprehensive data validation")
    print("   • Improved error handling and logging")
    print("   • Multiple file format support (Excel, CSV, HTML)")

    # Run scraper for last 30 days (1 month)
    print(f"\n📅 Scraping insider trading data for the last 30 days")
    print(f"🎯 Target: Capture ALL available records (not just web page display limit)")

    success = scraper.run_scraper(days_back=30, save_csv=True)

    if success:
        print("\n🎉 BSE insider trading data successfully scraped and stored!")
        print("💡 If you got significantly more records than before (~26), the enhancement worked!")
    else:
        print("\n💥 BSE scraping process failed. Check the logs above.")
        print("💡 Try running again - BSE website can be temperamental.")


if __name__ == "__main__":
    main()
