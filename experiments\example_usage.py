#!/usr/bin/env python3
"""
Example usage of PDF Analyzer
"""

from tests.pdf_analyzer import PDFAnalyzer
import os

def example_usage():
    # Set your OpenRouter API key
    API_KEY = "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"  # Replace with your actual API key
    
    # Optional: Set your site info for OpenRouter rankings
    SITE_URL = "https://yoursite.com"  # Optional
    SITE_NAME = "Your App Name"        # Optional
    
    # Initialize the analyzer
    analyzer = PDFAnalyzer(
        api_key=API_KEY,
        site_url=SITE_URL,
        site_name=SITE_NAME
    )
    
    # Example PDF file path
    pdf_path = "https://nsearchives.nseindia.com/corporate/SWARAJENG_20062025163533_SELAGMThruVCNoticeAd20062025.pdf"  # Replace with your PDF file path
    
    try:
        # Different types of analysis you can perform:
        
        # 1. General Analysis
        print("Performing general analysis...")
        results = analyzer.analyze_pdf(pdf_path, analysis_type="general")
        print("Analysis:", results['analysis']['choices'][0]['message']['content'])
        
        # 2. Summary Analysis
        print("\nPerforming summary analysis...")
        results = analyzer.analyze_pdf(pdf_path, analysis_type="summary")
        print("Summary:", results['analysis']['choices'][0]['message']['content'])
        
        # 3. Entity Extraction
        print("\nExtracting entities...")
        results = analyzer.analyze_pdf(pdf_path, analysis_type="entities")
        print("Entities:", results['analysis']['choices'][0]['message']['content'])
        
        # 4. Insights Analysis
        print("\nGenerating insights...")
        results = analyzer.analyze_pdf(pdf_path, analysis_type="insights")
        print("Insights:", results['analysis']['choices'][0]['message']['content'])
        
    except FileNotFoundError:
        print(f"PDF file not found: {pdf_path}")
        print("Please make sure the PDF file exists and the path is correct.")
    except Exception as e:
        print(f"Error: {e}")

def simple_example():
    """Simple example with minimal setup"""
    API_KEY = "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"  # Replace with your actual API key
    pdf_file = "document.pdf"      # Replace with your PDF file
    
    analyzer = PDFAnalyzer(api_key=API_KEY)
    
    try:
        # Quick analysis
        results = analyzer.analyze_pdf(pdf_file)
        
        # Print the AI analysis
        if 'choices' in results['analysis']:
            analysis_text = results['analysis']['choices'][0]['message']['content']
            print("PDF Analysis:")
            print("=" * 50)
            print(analysis_text)
        else:
            print("No analysis received")
            
    except Exception as e:
        print(f"Error analyzing PDF: {e}")

if __name__ == "__main__":
    print("PDF Analyzer Example Usage")
    print("=" * 40)
    
    # Choose which example to run
    choice = input("Run (1) Full example or (2) Simple example? Enter 1 or 2: ")
    
    if choice == "1":
        example_usage()
    else:
        simple_example()
