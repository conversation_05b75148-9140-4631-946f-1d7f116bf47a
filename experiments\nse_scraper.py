#!/usr/bin/env python3
"""
NSE Insider Trading Scraper with Supabase Integration
Scrapes NSE insider trading data and stores it directly in Supabase database.
"""

import requests
import pandas as pd
import json
import time
import random
from datetime import datetime, timedelta
from urllib.parse import urlencode
import json
from typing import Dict, List, Optional, Tuple
import os
import warnings
import urllib3
import hashlib

# Suppress warnings
warnings.filterwarnings('ignore', message='urllib3 v2 only supports OpenSSL 1.1.1+')
# Suppress SSL warnings more broadly
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings('ignore', category=urllib3.exceptions.InsecureRequestWarning)

# Supabase configuration
SUPABASE_URL = "https://ikcpoevsbbqfyxnmlmgl.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlrY3BvZXZzYmJxZnl4bm1sbWdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMDc2MzMsImV4cCI6MjA2NjU4MzYzM30.1SGjVa7I_BgOO__d2WnGo22qgIs9BM3FUOVWF2__laE"

class NSEInsiderTradingScraper:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://www.nseindia.com"
        self.supabase = self._setup_supabase()
        # ScraperAPI configuration
        self.scraper_api_key = '1824bf75f1f89db3c0fd107041639855'
        self.scraper_api_url = 'https://api.scraperapi.com/'
        self.setup_session()
        
    def _setup_supabase(self):
        """Setup Supabase REST API headers."""
        if not SUPABASE_KEY or len(SUPABASE_KEY) < 50:
            print("Warning: Invalid SUPABASE_ANON_KEY")
            return None

        # Return headers for REST API calls
        return {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'resolution=merge-duplicates'  # For upsert functionality
        }
        
    def setup_session(self):
        """Setup session with realistic headers and cookies."""
        # Rotate between different user agents
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        headers = {
            'User-Agent': random.choice(user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate',  # Removed 'br' to avoid Brotli compression
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.nseindia.com/companies-listing/corporate-filings-insider-trading'
        }

        self.session.headers.update(headers)

    def _make_request(self, method, url, **kwargs):
        """Make HTTP request through ScraperAPI proxy following official documentation."""
        # ScraperAPI base payload with API key and target URL
        payload = {
            'api_key': self.scraper_api_key,
            'url': url,
            # Add ScraperAPI specific parameters for better success
            'render': 'false',  # Don't render JavaScript
            'country_code': 'IN',  # Use Indian IP
            'premium': 'true'  # Use premium proxy pool
        }

        if method.upper() == 'GET':
            # For GET requests, merge any additional params with the ScraperAPI payload
            params = kwargs.get('params', {})
            payload.update(params)
            kwargs.pop('params', None)  # Remove params from kwargs since they're in payload

            # Debug information (reduced verbosity)
            print(f"Making ScraperAPI request to: {url}")

            # Make request to ScraperAPI endpoint with all parameters
            return self.session.get('https://api.scraperapi.com', params=payload, **kwargs)

        elif method.upper() == 'POST':
            # For POST requests, ScraperAPI requires special handling
            # We'll construct the URL with API key and target URL as query parameters
            scraperapi_url = f"https://api.scraperapi.com?api_key={self.scraper_api_key}&url={url}"

            # Forward the POST data and other kwargs to the ScraperAPI URL
            return self.session.post(scraperapi_url, **kwargs)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

    def get_cookies(self):
        """Get necessary cookies by visiting NSE pages."""
        try:
            print("Getting session cookies...")

            # Try direct approach first (like main.py)
            print("Attempting direct session establishment...")
            try:
                # Visit homepage first
                response = self.session.get(self.base_url, timeout=30)
                print(f"Homepage status: {response.status_code}")

                # Add random delay
                time.sleep(random.uniform(1, 3))

                # Visit insider trading page
                insider_url = f"{self.base_url}/companies-listing/corporate-filings-insider-trading"
                response = self.session.get(insider_url, timeout=30)
                print(f"Insider trading page status: {response.status_code}")

                # Add another delay
                time.sleep(random.uniform(1, 2))

                if response.status_code == 200:
                    print("✅ Direct session establishment successful")
                    return True
                else:
                    print("Direct session establishment failed, trying proxy...")
                    raise Exception("Direct failed")

            except Exception:
                print("Using ScraperAPI proxy for session establishment...")

                # Visit homepage first
                response = self._make_request('GET', self.base_url, timeout=30)
                print(f"Proxy homepage status: {response.status_code}")

                # Add random delay
                time.sleep(random.uniform(1, 3))

                # Visit insider trading page
                insider_url = f"{self.base_url}/companies-listing/corporate-filings-insider-trading"
                response = self._make_request('GET', insider_url, timeout=30)
                print(f"Proxy insider trading page status: {response.status_code}")

                # Add another delay
                time.sleep(random.uniform(1, 2))

                return response.status_code == 200

        except Exception as e:
            print(f"Error getting cookies: {e}")
            return False
    
    def test_scraperapi_connection(self):
        """Test ScraperAPI connection with a simple request."""
        try:
            print("Testing ScraperAPI connection...")
            test_payload = {
                'api_key': self.scraper_api_key,
                'url': 'https://httpbin.org/ip'
            }
            response = self.session.get(self.scraper_api_url, params=test_payload, timeout=30)
            if response.status_code == 200:
                print("✅ ScraperAPI connection test successful")
                return True
            else:
                print(f"❌ ScraperAPI connection test failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ ScraperAPI connection test error: {e}")
            return False

    def fetch_insider_data(self, days_back=30):
        """Fetch insider trading data from NSE API using the correct endpoint."""
        # Test ScraperAPI connection first
        if not self.test_scraperapi_connection():
            print("ScraperAPI connection failed. Proceeding with direct requests...")

        # Calculate date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=days_back)

        from_date_str = from_date.strftime("%d-%m-%Y")
        to_date_str = to_date.strftime("%d-%m-%Y")

        print(f"Fetching NSE insider trading data from {from_date_str} to {to_date_str}")

        # Use the correct API endpoint from main.py
        api_url = f"{self.base_url}/api/corporates-pit"
        params = {
            'index': 'equities',
            'from_date': from_date_str,
            'to_date': to_date_str
        }

        try:
            # Add random delay to avoid rate limiting
            time.sleep(random.uniform(2, 5))

            print(f"Trying API endpoint: {api_url}")
            print(f"Parameters: {params}")

            # Use direct request for API call (like main.py) since session is already established
            print("Making direct API request (session already established via proxy)...")
            response = self.session.get(api_url, params=params, timeout=60)

            print(f"Response status: {response.status_code}")

            if response.status_code != 200:
                print(f"API request failed with status {response.status_code}")
                print(f"Response headers: {dict(response.headers)}")
                return None

            # Handle different content encodings
            content_encoding = response.headers.get('content-encoding', '').lower()
            content_type = response.headers.get('content-type', '').lower()

            print(f"Content-Type: {content_type}")
            print(f"Content-Encoding: {content_encoding}")

            # Handle response content
            response_content = None
            if 'br' in content_encoding:
                print("Brotli encoding detected. Attempting to decompress...")
                try:
                    import brotli
                    decompressed = brotli.decompress(response.content)
                    response_content = decompressed.decode('utf-8')
                except ImportError:
                    print("Brotli library not installed. Install with: pip install brotli")
                    return None
                except Exception as e:
                    print(f"Brotli decompression error: {e}")
                    return None
            else:
                response_content = response.text

            print(f"Response preview: {response_content[:200]}")

            # Check if response is JSON
            if 'application/json' not in content_type:
                print(f"Warning: Response is not JSON. Content-Type: {content_type}")
                print("This might be an error page or CAPTCHA challenge")

                # Save the response for debugging
                with open('nse_response_debug.html', 'w', encoding='utf-8') as f:
                    f.write(response_content)
                print("Response saved to 'nse_response_debug.html' for inspection")
                return None

            # Parse JSON
            try:
                data = json.loads(response_content)
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print("Response might be empty or malformed")
                return None

            # Check data structure
            if not isinstance(data, dict):
                print(f"Unexpected data type: {type(data)}")
                return None

            if 'data' not in data:
                print(f"No 'data' key found. Available keys: {list(data.keys())}")
                return None

            if not data['data']:
                print("No insider trading data found for the specified date range")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(data['data'])
            print(f"Successfully fetched {len(df)} records")

            return df, from_date_str, to_date_str

        except requests.exceptions.Timeout:
            print("ScraperAPI proxy request timed out. Trying direct request as fallback...")
            try:
                # Fallback to direct request without proxy
                response = self.session.get(api_url, params=params, timeout=60)
                print(f"Direct request status: {response.status_code}")

                if response.status_code != 200:
                    print(f"Direct request also failed with status {response.status_code}")
                    return None

                # Process the direct response the same way
                content_encoding = response.headers.get('content-encoding', '').lower()
                content_type = response.headers.get('content-type', '').lower()

                print(f"Direct request - Content-Type: {content_type}")
                print(f"Direct request - Content-Encoding: {content_encoding}")

                response_content = response.text
                print(f"Direct request response preview: {response_content[:200]}")

                if 'application/json' not in content_type:
                    print(f"Direct request - Response is not JSON. Content-Type: {content_type}")
                    return None

                try:
                    data = json.loads(response_content)
                    if isinstance(data, dict) and 'data' in data and data['data']:
                        df = pd.DataFrame(data['data'])
                        print(f"Successfully fetched {len(df)} records via direct request")
                        return df, from_date_str, to_date_str
                    else:
                        print("Direct request - No valid data found")
                        return None
                except json.JSONDecodeError:
                    print("Direct request - JSON decode error")
                    return None

            except Exception as fallback_error:
                print(f"Direct request fallback also failed: {fallback_error}")
                return None

        except requests.exceptions.ConnectionError:
            print("Connection error. Check your internet connection.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None


    
    def _clean_numeric_value(self, value):
        """Clean numeric values, handling dashes and invalid data."""
        if pd.isna(value) or value is None:
            return None

        try:
            raw_val = str(value).strip()
            if raw_val in ['-', '', 'nan', 'None', 'null']:
                return None

            cleaned_val = float(raw_val.replace(',', ''))
            return cleaned_val if cleaned_val != 0 else None
        except (ValueError, TypeError):
            return None

    def _generate_record_hash(self, record):
        """Generate a unique hash for a record to detect duplicates."""
        # Use key fields that uniquely identify a transaction
        key_fields = [
            str(record.get('security_code', '')),
            str(record.get('person_name', '')),
            str(record.get('securities_acquired_disposed_number', '')),
            str(record.get('securities_acquired_disposed_value', '')),
            str(record.get('securities_acquired_disposed_transaction_type', '')),
            str(record.get('broadcast_date_time', '')),
            str(record.get('intimation_date', ''))
        ]

        # Create a string from key fields and hash it
        key_string = '|'.join(key_fields)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()

    def _detect_duplicates_in_batch(self, records):
        """Detect and remove duplicates within a batch of records."""
        seen_hashes = set()
        unique_records = []
        duplicate_count = 0

        for record in records:
            record_hash = self._generate_record_hash(record)

            if record_hash not in seen_hashes:
                seen_hashes.add(record_hash)
                # Add hash to record for database storage
                record['record_hash'] = record_hash
                unique_records.append(record)
            else:
                duplicate_count += 1

        return unique_records, duplicate_count

    def _get_existing_record_hashes(self, date_range_days=30):
        """Get existing record hashes from database for the date range."""
        if not self.supabase:
            return set()

        try:
            # Calculate date range for checking existing records
            to_date = datetime.now()
            from_date = to_date - timedelta(days=date_range_days)

            all_hashes = set()
            offset = 0
            limit = 1000  # Smaller batches for better performance

            while True:
                # Query existing records in batches
                api_url = f"{SUPABASE_URL}/rest/v1/nse_insider_trading"
                params = {
                    'select': 'record_hash',
                    'broadcast_date_time': f'gte.{from_date.isoformat()}',
                    'limit': str(limit),
                    'offset': str(offset),
                    'order': 'id'
                }

                response = self.session.get(api_url, headers=self.supabase, params=params, timeout=30)

                if response.status_code == 200:
                    batch_records = response.json()
                    if not batch_records:  # No more records
                        break

                    batch_hashes = {record.get('record_hash') for record in batch_records if record.get('record_hash')}
                    all_hashes.update(batch_hashes)

                    print(f"Fetched batch {offset//limit + 1}: {len(batch_hashes)} hashes")

                    if len(batch_records) < limit:  # Last batch
                        break

                    offset += limit
                else:
                    print(f"Warning: Could not fetch existing records (status: {response.status_code})")
                    break

            print(f"Found {len(all_hashes)} existing record hashes in database")
            return all_hashes

        except Exception as e:
            print(f"Warning: Error fetching existing records: {e}")
            return set()

    def _filter_existing_records(self, records, existing_hashes):
        """Filter out records that already exist in the database."""
        new_records = []
        duplicate_count = 0

        for record in records:
            record_hash = record.get('record_hash')
            if record_hash and record_hash in existing_hashes:
                duplicate_count += 1
            else:
                new_records.append(record)

        return new_records, duplicate_count

    def clean_data(self, df):
        """Clean and standardize the NSE data."""
        if df is None or df.empty:
            return None

        try:
            print("Cleaning NSE data...")

            # Display column names for debugging
            print(f"Available columns: {list(df.columns)}")

            # Basic data cleaning
            df = df.dropna(how='all')

            # Remove any completely empty columns
            df = df.dropna(axis=1, how='all')

            print(f"Cleaned data: {len(df)} records")
            return df

        except Exception as e:
            print(f"Error cleaning data: {e}")
            return None
    
    def process_dataframe_to_records(self, df):
        """Convert NSE DataFrame to database-ready records."""
        processed_records = []
        
        # Process NSE data to database format
        
        for _, row in df.iterrows():
            try:
                # Parse dates
                intimation_date = None
                broadcast_date_time = None
                transaction_from = None
                transaction_to = None
                reported_date = None
                
                # Try to parse various date fields (NSE format)
                date_fields = {
                    'intimDt': 'intimation_date',
                    'date': 'broadcast_date_time',
                    'acqfromDt': 'transaction_from',
                    'acqtoDt': 'transaction_to'
                }

                for field_name, target_var in date_fields.items():
                    if field_name in row and pd.notna(row[field_name]):
                        try:
                            parsed_date = pd.to_datetime(row[field_name])
                            if target_var == 'intimation_date':
                                intimation_date = parsed_date.date()
                            elif target_var == 'broadcast_date_time':
                                broadcast_date_time = parsed_date
                                # Extract date from broadcast_date_time for reported_to_exchange_date
                                reported_date = parsed_date.date()
                            elif target_var == 'transaction_from':
                                transaction_from = parsed_date.date()
                            elif target_var == 'transaction_to':
                                transaction_to = parsed_date.date()
                        except:
                            pass
                
                # Extract numeric values (NSE field names)
                securities_number = None
                securities_value = None
                pre_number = None
                post_number = None
                buy_quantity = None
                sell_quantity = None

                # NSE uses different field names
                numeric_fields = {
                    'secVal': 'securities_value',
                    'secAcq': 'pre_number',
                    'befAcqSharesNo': 'pre_number',
                    'afterAcqSharesNo': 'post_number',
                    'buyQuantity': 'buy_quantity',
                    'sellquantity': 'sell_quantity'
                }

                for field_name, target_var in numeric_fields.items():
                    if field_name in row and pd.notna(row[field_name]):
                        try:
                            # Clean the value - handle dashes, empty strings, etc.
                            raw_val = str(row[field_name]).strip()
                            if raw_val in ['-', '', 'nan', 'None', 'null']:
                                continue

                            val = float(raw_val.replace(',', ''))
                            if target_var == 'securities_value':
                                securities_value = val if val != 0 else None
                            elif target_var == 'pre_number':
                                pre_number = int(val) if val != 0 else None
                            elif target_var == 'post_number':
                                post_number = int(val) if val != 0 else None
                            elif target_var == 'buy_quantity':
                                buy_quantity = int(val) if val != 0 else None
                            elif target_var == 'sell_quantity':
                                sell_quantity = int(val) if val != 0 else None
                        except (ValueError, TypeError):
                            # Skip invalid numeric values
                            continue

                # Determine transaction number based on buy/sell
                if buy_quantity and buy_quantity > 0:
                    securities_number = buy_quantity
                elif sell_quantity and sell_quantity > 0:
                    securities_number = sell_quantity
                
                # Ensure required fields have values
                person_name = row.get('acqName', row.get('personName', row.get('name', '')))
                person_category = row.get('personCategory', row.get('category', ''))
                security_code = row.get('symbol', row.get('scrip', ''))
                security_name = row.get('company', row.get('companyName', ''))

                # Handle missing person_name
                if not person_name or str(person_name).strip() in ['', '-', 'nan', 'None']:
                    person_name = 'Unknown Person'  # Default name

                # Skip records with missing security_code
                if not security_code or str(security_code).strip() in ['', '-', 'nan', 'None']:
                    print(f"Skipping record with missing security_code")
                    continue

                # Provide default for person_category if missing
                if not person_category or str(person_category).strip() in ['', '-', 'nan', 'None']:
                    person_category = 'Other'  # Default category

                record = {
                    'security_code': str(security_code),
                    'nse_code': str(security_code),  # Add nse_code field for trigger function
                    'security_name': str(security_name),
                    'person_name': str(person_name),
                    'person_category': str(person_category),
                    'securities_held_pre_transaction': str(row.get('secAcq', '')),
                    'securities_held_pre_number': pre_number,
                    'securities_held_pre_percentage': self._clean_numeric_value(row.get('befAcqSharesPer')),
                    'securities_acquired_disposed_type': str(row.get('secType', row.get('securityType', row.get('typeOfSecurity', '')))),
                    'securities_acquired_disposed_number': securities_number,
                    'securities_acquired_disposed_value': securities_value,
                    'securities_acquired_disposed_transaction_type': str(row.get('tdpTransactionType', row.get('acqMode', row.get('acquisitionMode', '')))),
                    'securities_held_post_transaction': str(row.get('securitiesTypePost', row.get('secPost', ''))),
                    'securities_held_post_number': post_number,
                    'securities_held_post_percentage': self._clean_numeric_value(row.get('afterAcqSharesPer')),
                    'transaction_period_from': transaction_from,
                    'transaction_period_to': transaction_to,
                    'mode_of_acquisition': str(row.get('acqMode', row.get('acquisitionMode', ''))),
                    'derivatives_type_of_contract': None,
                    'derivatives_buy_value_units': None,
                    'derivatives_sale_value_units': None,
                    'reported_to_exchange_date': reported_date,
                    'intimation_date': intimation_date,
                    'broadcast_date_time': broadcast_date_time,
                    'record_hash': None  # Will be populated by duplicate detection
                }
                
                # Clean empty strings, dashes, and invalid values
                for key, value in record.items():
                    if value == '' or str(value) in ['nan', '-', 'None', 'null']:
                        record[key] = None
                    elif isinstance(value, str) and value.strip() == '':
                        record[key] = None
                
                processed_records.append(record)

            except Exception as e:
                print(f"Error processing row: {e}")
                continue

        print(f"📝 Processed {len(processed_records)} records from DataFrame")
        return processed_records

    def serialize_for_json(self, obj):
        """Convert objects to JSON-serializable format."""
        if hasattr(obj, 'isoformat'):  # datetime, date objects
            return obj.isoformat()
        return obj

    def prepare_records_for_api(self, records):
        """Prepare records for JSON serialization."""
        serialized_records = []

        for record in records:
            serialized_record = {}
            for key, value in record.items():
                serialized_record[key] = self.serialize_for_json(value)
            serialized_records.append(serialized_record)

        return serialized_records

    def save_to_database(self, records):
        """Save records to Supabase database using REST API with duplicate detection."""
        if not self.supabase:
            print("Supabase headers not initialized. Skipping database save.")
            return False

        if not records:
            print("No records to save.")
            return True

        try:
            print(f"Processing {len(records)} records for database insertion...")

            # Step 1: Detect and remove duplicates within the batch
            unique_records, batch_duplicates = self._detect_duplicates_in_batch(records)
            if batch_duplicates > 0:
                print(f"🔍 Removed {batch_duplicates} duplicate records within the batch")

            # Step 2: Check against existing records in database
            print("🔍 Checking for existing records in database...")
            existing_hashes = self._get_existing_record_hashes()
            new_records, db_duplicates = self._filter_existing_records(unique_records, existing_hashes)

            if db_duplicates > 0:
                print(f"🔍 Found {db_duplicates} records that already exist in database")

            total_duplicates = batch_duplicates + db_duplicates
            if total_duplicates > 0:
                print(f"📊 Total duplicates detected and skipped: {total_duplicates}")

            if not new_records:
                print("ℹ️ No new records to insert (all were duplicates)")
                print(f"\n📊 FINAL SUMMARY:")
                print(f"   Total records processed: {len(records)}")
                print(f"   Duplicates within batch: {batch_duplicates}")
                print(f"   Duplicates in database: {db_duplicates}")
                print(f"   New records inserted: 0")
                print("✅ All records were duplicates - no insertion needed")
                return True

            print(f"💾 Inserting {len(new_records)} new unique records to database...")

            # Prepare records for JSON serialization
            serialized_records = self.prepare_records_for_api(new_records)

            # Supabase REST API endpoint
            api_url = f"{SUPABASE_URL}/rest/v1/nse_insider_trading"

            # Insert records in batches to avoid timeout
            batch_size = 50
            total_inserted = 0
            failed_batches = 0

            for i in range(0, len(serialized_records), batch_size):
                batch = serialized_records[i:i + batch_size]
                try:
                    # Use upsert to handle any remaining duplicates at database level
                    headers = self.supabase.copy()
                    headers['Prefer'] = 'resolution=ignore-duplicates'

                    response = self.session.post(
                        api_url,
                        headers=headers,
                        data=json.dumps(batch),
                        timeout=60
                    )

                    if response.status_code in [200, 201]:
                        total_inserted += len(batch)
                        print(f"✅ Inserted batch {i//batch_size + 1}: {len(batch)} records")
                    else:
                        failed_batches += 1
                        print(f"❌ Batch {i//batch_size + 1} failed: {response.status_code} - {response.text}")
                        continue

                except Exception as e:
                    failed_batches += 1
                    print(f"❌ Error inserting batch {i//batch_size + 1}: {e}")
                    continue

            # Summary
            print(f"\n📊 INSERTION SUMMARY:")
            print(f"   Total records processed: {len(records)}")
            print(f"   Duplicates within batch: {batch_duplicates}")
            print(f"   Duplicates in database: {db_duplicates}")
            print(f"   New records inserted: {total_inserted}")
            print(f"   Failed batches: {failed_batches}")
            print(f"🎉 Successfully inserted {total_inserted} NSE records to database")

            return total_inserted > 0

        except Exception as e:
            print(f"💥 Error saving to database: {e}")
            return False

    def save_data(self, df, from_date, to_date):
        """Save DataFrame to CSV file."""
        try:
            filename = f"nse_insider_trading_{from_date.replace('-', '')}_{to_date.replace('-', '')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8')
            print(f"Data saved to {filename}")
            return filename
        except Exception as e:
            print(f"Error saving CSV: {e}")
            return None

    def run_scraper(self, days_back=30, save_csv=True):
        """Run the complete NSE scraping process."""
        try:
            print("=== NSE Insider Trading Scraper ===")
            print(f"Scraping data for the last {days_back} days...")

            # Get cookies first
            if not self.get_cookies():
                print("Failed to get session cookies. Continuing anyway...")

            # Add delay before API call
            time.sleep(random.uniform(2, 4))

            # Fetch data
            result = self.fetch_insider_data(days_back=days_back)

            if result is None:
                print("\nFailed to fetch NSE data. This could be due to:")
                print("1. NSE's anti-scraping measures (most likely)")
                print("2. Network connectivity issues")
                print("3. API endpoint changes")
                print("4. CAPTCHA challenges")
                print("\nAlternative solutions:")
                print("- Use Selenium with headless Chrome")
                print("- Try different time intervals")
                print("- Use proxy servers")
                print("- Consider NSE's official data feeds")
                return False

            df, from_date, to_date = result

            # Clean data
            df_clean = self.clean_data(df)

            if df_clean is None or df_clean.empty:
                print("Data cleaning failed or no data available")
                return False

            # Save to CSV if requested
            if save_csv:
                csv_filename = self.save_data(df_clean, from_date, to_date)
                if csv_filename:
                    print(f"Data saved to {csv_filename}")

            # Process and save to database
            records = self.process_dataframe_to_records(df_clean)
            success = self.save_to_database(records)

            if success:
                print("✅ NSE scraping completed successfully!")
                return True
            else:
                print("⚠️ NSE scraping completed but database save failed.")
                return False

        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return False
        except Exception as e:
            print(f"❌ NSE scraping failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main function to run the NSE scraper."""
    scraper = NSEInsiderTradingScraper()

    print("🚀 Starting NSE Insider Trading Scraper with Database Integration")
    print(f"📊 Database URL: {SUPABASE_URL}")
    print(f"🔑 API Key: {SUPABASE_KEY[:20]}...{SUPABASE_KEY[-10:]}")
    print("ℹ️ Note: NSE has strong anti-scraping measures. Using ScraperAPI proxy for better success rate.")

    # Run scraper for last 30 days
    success = scraper.run_scraper(days_back=30, save_csv=True)

    if success:
        print("\n🎉 NSE insider trading data successfully processed and stored!")
    else:
        print("\n💥 NSE scraping process failed. Check the logs above.")


if __name__ == "__main__":
    main()
