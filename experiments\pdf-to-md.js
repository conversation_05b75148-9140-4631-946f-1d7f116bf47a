const axios = require('axios');
const pdf = require('pdf-parse');
const fs = require('fs');
const he = require('he');

// Enhanced text processing utilities for LLM/RAG optimization
class DocumentProcessor {
  constructor() {
    this.metadata = {};
    this.sections = [];
    this.tables = [];
    this.footnotes = [];
    this.headerStyles = {};
  }

  // Language detection - always return English since we ignore other languages
  detectLanguage(text) {
    return 'en';
  }

  // Enhanced Unicode normalization - English only, ignore other languages
  normalizeUnicode(text) {
    if (!text || typeof text !== 'string') return '';

    // Normalize Unicode characters
    text = text.normalize('NFKC');

    // Remove non-English characters - keep only English, numbers, and basic punctuation
    // This will ignore Gurmukhi, Arabic, Chinese, and other non-Latin scripts
    text = text.replace(/[^\u0020-\u007E\u00A0-\u00FF\u2000-\u206F\u2070-\u209F\u20A0-\u20CF\u2100-\u214F\u2150-\u218F\u2190-\u21FF\u2200-\u22FF]/g, '');

    // Remove problematic characters
    text = text.replace(/[\u0000-\u001F\u007F-\u009F\u200B-\u200D\uFEFF\uFFF0-\uFFFF]/g, '');

    // Fix common PDF artifacts
    text = text.replace(/\u00A0+/g, ' ')            // Non-breaking spaces
               .replace(/\u2013|\u2014/g, '-')     // En/em dashes
               .replace(/\u2018|\u2019/g, "'")     // Smart quotes
               .replace(/\u201C|\u201D/g, '"')     // Smart quotes
               .replace(/\u2026/g, '...')           // Ellipsis
               .replace(/\u00AD/g, '')              // Soft hyphens
               .replace(/(\w)-\s+(\w)/g, '$1$2');   // Fix broken words

    // Decode HTML entities
    text = he.decode(text);

    // Clean up multiple spaces and empty lines
    text = text.replace(/\s+/g, ' ');
    text = text.replace(/\n\s*\n/g, '\n');

    return text.trim();
  }

  // Fix Gurmukhi encoding
  fixGurmukhiEncoding(text) {
    if (!text || typeof text !== 'string') return '';

    // Common Gurmukhi character corruption patterns found in PDFs
    const gurmukhiFixes = [
      // Vowels (Independent)
      [/à¨\u0085/g, 'ਅ'], [/à¨\u0086/g, 'ਆ'], [/à¨\u0087/g, 'ਇ'], [/à¨\u0088/g, 'ਈ'],
      [/à¨\u0089/g, 'ਉ'], [/à¨\u008A/g, 'ਊ'], [/à¨\u008F/g, 'ਏ'], [/à¨\u0090/g, 'ਐ'],
      [/à¨\u0093/g, 'ਓ'], [/à¨\u0094/g, 'ਔ'],

      // Consonants
      [/à¨\u0095/g, 'ਕ'], [/à¨\u0096/g, 'ਖ'], [/à¨\u0097/g, 'ਗ'], [/à¨\u0098/g, 'ਘ'],
      [/à¨\u0099/g, 'ਙ'], [/à¨\u009A/g, 'ਚ'], [/à¨\u009B/g, 'ਛ'], [/à¨\u009C/g, 'ਜ'],
      [/à¨\u009D/g, 'ਝ'], [/à¨\u009E/g, 'ਞ'], [/à¨\u009F/g, 'ਟ'], [/à¨\u00A0/g, 'ਠ'],
      [/à¨\u00A1/g, 'ਡ'], [/à¨\u00A2/g, 'ਢ'], [/à¨\u00A3/g, 'ਣ'], [/à¨\u00A4/g, 'ਤ'],
      [/à¨\u00A5/g, 'ਥ'], [/à¨\u00A6/g, 'ਦ'], [/à¨\u00A7/g, 'ਧ'], [/à¨\u00A8/g, 'ਨ'],
      [/à¨\u00AA/g, 'ਪ'], [/à¨\u00AB/g, 'ਫ'], [/à¨\u00AC/g, 'ਬ'], [/à¨\u00AD/g, 'ਭ'],
      [/à¨\u00AE/g, 'ਮ'], [/à¨\u00AF/g, 'ਯ'], [/à¨\u00B0/g, 'ਰ'], [/à¨\u00B2/g, 'ਲ'],
      [/à¨\u00B3/g, 'ਲ਼'], [/à¨\u00B5/g, 'ਵ'], [/à¨\u00B6/g, 'ਸ਼'], [/à¨\u00B8/g, 'ਸ'],
      [/à¨\u00B9/g, 'ਹ'],

      // Vowel signs (matras)
      [/à©\u0081/g, 'ਾ'], [/à©\u0082/g, 'ਿ'], [/à©\u0083/g, 'ੀ'], [/à©\u0084/g, 'ੁ'],
      [/à©\u0085/g, 'ੂ'], [/à©\u0087/g, 'ੇ'], [/à©\u0088/g, 'ੈ'], [/à©\u008B/g, 'ੋ'],
      [/à©\u008C/g, 'ੌ'],

      // Special characters and punctuation
      [/à¨\u00A5/g, '।'], //     danda (full stop)
      [/à¨\u00A6/g, '॥'], // Gurmukhi double danda

      // Alternative encoding patterns
      [/Í≈/g, 'ਪਾ'], [/Â∂/g, 'ਤੇ'], [/Á∂/g, 'ਦੇ'], [/Á∆/g, 'ਦੀ'], [/Á≈/g, 'ਦਾ'],
      [/È∂/g, 'ਨੇ'], [/È≈/g, 'ਨਾ'], [/È∆/g, 'ਨੀ'], [/Ò¬∆/g, 'ਲਈ'], [/√∂/g, 'ਸੇ'],
      [/√≈/g, 'ਸਾ'], [/√∆/g, 'ਸੀ'], [/'∂/g, 'ਕੇ'], [/'≈/g, 'ਕਾ'], [/'∆/g, 'ਕੀ'],
      [/◊∂/g, 'ਗੇ'], [/◊≈/g, 'ਗਾ'], [/◊∆/g, 'ਗੀ'], [/Ó∂/g, 'ਮੇ'], [/Ó≈/g, 'ਮਾ'],
      [/Ó∆/g, 'ਮੀ'], [/Ú∂/g, 'ਵੇ'], [/Ú≈/g, 'ਵਾ'], [/Ú∆/g, 'ਵੀ'], [/‹∂/g, 'ਜੇ'],
      [/‹≈/g, 'ਜਾ'], [/‹∆/g, 'ਜੀ'], [/Ï∂/g, 'ਬੇ'], [/Ï≈/g, 'ਬਾ'], [/Ï∆/g, 'ਬੀ'],
      [/Ò∂/g, 'ਲੇ'], [/Ò≈/g, 'ਲਾ'], [/Ò∆/g, 'ਲੀ'], [/⁄∂/g, 'ਚੇ'], [/⁄≈/g, 'ਚਾ'],
      [/⁄∆/g, 'ਚੀ'], [/Ë∂/g, 'ਧੇ'], [/Ë≈/g, 'ਧਾ'], [/Ë∆/g, 'ਧੀ'], [/Ù∂/g, 'ਸ਼ੇ'],
      [/Ù≈/g, 'ਸ਼ਾ'], [/Ù∆/g, 'ਸ਼ੀ'], [/Í∞/g, 'ਪੁ'], [/Ó∞/g, 'ਮੁ'], [/√∞/g, 'ਸੁ'],
      [/'∞/g, 'ਕੁ'], [/‹∞/g, 'ਜੁ'], [/Ò∞/g, 'ਲੁ'], [/Ú∞/g, 'ਵੁ'], [/Ë∞/g, 'ਧੁ'],

      // Common words and phrases
      [/«''≈/g, 'ਕਿਹਾ'], [/«◊¡≈/g, 'ਗਿਆ'], [/'∆Â≈/g, 'ਕੀਤਾ'], [/'∆Â∆/g, 'ਕੀਤੀ'],
      [/«Ú⁄/g, 'ਵਿਚ'], [/«ÈÔ∞'Â/g, 'ਨਿਯੁਕਤ'], [/√≥‹∆Ú/g, 'ਸੰਜੀਵ'], [/√≥Ó∂ÒÈ/g, 'ਸਮੇਲਨ'],
      [/¡≈Í‰∂/g, 'ਆਪਣੇ'], [/¡≈Í‰∆/g, 'ਆਪਣੀ'], [/¡≈Í‰≈/g, 'ਆਪਣਾ'], [/¿∞È∑a/g, 'ਉਨ੍ਹਾਂ'],
      [/¿∞√/g, 'ਉਸ'], [/¿∞√Á∂/g, 'ਉਸਦੇ'], [/¿∞√Á∆/g, 'ਉਸਦੀ'], [/¿∞√Á≈/g, 'ਉਸਦਾ'],
      [/«¬√/g, 'ਇਸ'], [/«¬√Á∂/g, 'ਇਸਦੇ'], [/«¬√Á∆/g, 'ਇਸਦੀ'], [/«¬√Á≈/g, 'ਇਸਦਾ'],
      [/ÒØ'a/g, 'ਲੋਕਾਂ'], [/ÒØ'/g, 'ਲੋਕ'], [/√≈∂/g, 'ਸਾਰੇ'], [/√≈∆/g, 'ਸਾਰੀ'],
      [/√≈≈/g, 'ਸਾਰਾ'], [/√Ì/g, 'ਸਭ'], [/√Ì∂/g, 'ਸਭੇ'], [/√Ì∆/g, 'ਸਭੀ'],

      // Numbers in Gurmukhi
      [/¬∂'/g, 'ਇੱਕ'], [/ÁØ/g, 'ਦੋ'], [/«ÂoÈ/g, 'ਤਿੰਨ'], [/⁄≈/g, 'ਚਾਰ'], [/Í≥‹/g, 'ਪੰਜ'],
      [/¤∂/g, 'ਛੇ'], [/√oÂ/g, 'ਸੱਤ'], [/¡o·/g, 'ਅੱਠ'], [/ÈΩ/g, 'ਨੌ'], [/Á√/g, 'ਦਸ'],
    ];

    // Apply all Gurmukhi fixes
    for (const [pattern, replacement] of gurmukhiFixes) {
      text = text.replace(pattern, replacement);
    }

    // Fix remaining corrupted patterns
    text = text.replace(/[àáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ][¨©ª«¬­®¯°±²³´µ¶·¸¹º»¼½¾¿]/g, '[CORRUPTED_GURMUKHI]');

    return text;
  }

  // Fix common encoding issues
  fixEncodingIssues(text) {
    if (!text || typeof text !== 'string') return '';

    // Common encoding fixes
    const commonEncodingFixes = [
      [/â€™/g, "'"], [/â€œ/g, '"'], [/â€/g, '"'], 
      [/â€"/g, '–'], [/â€"/g, '—'], [/â€¢/g, '•'], 
      [/â€¦/g, '…'], [/Ã¡/g, 'á'], [/Ã©/g, 'é'], 
      [/Ã­/g, 'í'], [/Ã³/g, 'ó'], [/Ãº/g, 'ú'],
      [/Ã±/g, 'ñ'], [/Ã¼/g, 'ü'], [/Ã¶/g, 'ö'], 
      [/Ã¤/g, 'ä'], [/Ã /g, 'à'], [/â€“/g, '-']
    ];

    for (const [pattern, replacement] of commonEncodingFixes) {
      text = text.replace(pattern, replacement);
    }

    return text;
  }

  // Extract document metadata with improved patterns
  extractMetadata(text) {
    const metadata = {
      title: '',
      date: '',
      company: '',
      documentType: '',
      reference: '',
      subject: '',
      language: this.detectLanguage(text)
    };

    // Improved date detection
    const datePatterns = [
      /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/,
      /(\d{4}[-\/]\d{1,2}[-\/]\d{1,2})/,
      /(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4})/i,
      /(Date:\s*([^\n]+))/i
    ];

    for (const pattern of datePatterns) {
      const match = text.match(pattern);
      if (match) {
        metadata.date = match[1].replace(/^Date:\s*/i, '').trim();
        break;
      }
    }

    // Extract company names
    const companyPattern = /([A-Z][A-Z\s&.,-]+(?:LIMITED|LTD|PRIVATE|PVT|INC|CORP|PLC|LLP))\b/gi;
    const companies = [...new Set([...text.matchAll(companyPattern)].map(m => m[0]))];
    metadata.company = companies.length > 0 ? companies[0] : '';

    // Extract subject/reference
    const subjectMatch = text.match(/(?:Subject|Ref(?:erence)?|Re):\s*([^\n]{10,120})/i);
    if (subjectMatch) metadata.subject = subjectMatch[1].trim();

    // Document type classification
    const typePatterns = [
      { pattern: /(annual\s+general\s+meeting|AGM)/i, type: 'AGM Notice' },
      { pattern: /(resignation|retirement)/i, type: 'Resignation Notice' },
      { pattern: /(dividend|interest)/i, type: 'Dividend Notice' },
      { pattern: /(IEPF|investor\s+education)/i, type: 'IEPF Notice' },
      { pattern: /(financial\s+statement|balance\s+sheet)/i, type: 'Financial Report' },
      { pattern: /(board\s+meeting)/i, type: 'Board Meeting Notice' }
    ];

    for (const {pattern, type} of typePatterns) {
      if (pattern.test(text)) {
        metadata.documentType = type;
        break;
      }
    }

    // Extract title from first meaningful line
    const lines = text.split('\n').filter(line => line.trim().length > 30);
    if (lines.length > 0) {
      metadata.title = lines[0].substring(0, 120).trim();
    }

    this.metadata = metadata;
    return metadata;
  }

  // Improved table detection with multi-line support
  detectTables(text) {
    const lines = text.split('\n');
    const tables = [];
    let currentTable = [];
    let inTable = false;
    let columnCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Split line into potential columns
      const columns = line.split(/\s{2,}/).filter(c => c);
      const hasHeaderPattern = /^(Sl?\.?No?\.?|Name|Description|Date|Amount|Value|%|Qty)/i.test(line);
      const hasTableData = columns.length > 1 && (
        /\d[\d,.]*\s*[%₹$€£]?/.test(line) ||  // Numeric data
        hasHeaderPattern
      );

      if (hasTableData) {
        // Initialize or continue table
        if (!inTable) {
          inTable = true;
          currentTable = [line];
          columnCount = columns.length;
        } else {
          // Check for consistent structure
          if (Math.abs(columns.length - columnCount) <= 1) {
            currentTable.push(line);
          } else {
            // Table structure changed, finalize current table
            if (currentTable.length >= 2) tables.push(this.formatTable(currentTable));
            currentTable = [line];
            columnCount = columns.length;
          }
        }
      } else if (inTable) {
        // Handle multi-line table cells
        if (currentTable.length > 0 && line.length < 100 && !/[.!?]$/.test(line)) {
          currentTable[currentTable.length - 1] += ' ' + line;
        } else {
          // Finalize table
          if (currentTable.length >= 2) tables.push(this.formatTable(currentTable));
          inTable = false;
          currentTable = [];
        }
      }
    }

    // Finalize last table
    if (inTable && currentTable.length >= 2) {
      tables.push(this.formatTable(currentTable));
    }

    this.tables = tables;
    return tables;
  }

  // Enhanced table formatting with alignment detection
  formatTable(tableLines) {
    if (tableLines.length < 2) return tableLines.join('\n');

    // Parse rows with improved cell splitting
    const rows = tableLines.map(line => {
      // Handle complex cell boundaries
      return line.split(/(?<!\w\s)\s{2,}(?!\s)/).map(cell => cell.trim());
    });

    // Determine column count and alignment
    const colCount = Math.max(...rows.map(row => row.length));
    const alignments = Array(colCount).fill('left');
    
    // Detect numeric columns for right alignment
    for (let col = 0; col < colCount; col++) {
      const numericCount = rows.slice(1).filter(row => 
        row[col] && /^[\d,.\s]*[%₹$€£]?$/.test(row[col])
      ).length;
      
      if (numericCount > rows.length / 2) {
        alignments[col] = 'right';
      }
    }

    // Build markdown table
    let markdown = '\n';
    
    // Header row
    markdown += '| ' + rows[0].join(' | ') + ' |\n';
    
    // Alignment row
    markdown += '| ' + alignments.map(a => a === 'right' ? '---:' : '---').join(' | ') + ' |\n';
    
    // Data rows
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i];
      // Handle rows with missing cells
      const paddedRow = [...row, ...Array(colCount - row.length).fill('')];
      markdown += '| ' + paddedRow.join(' | ') + ' |\n';
    }
    
    return markdown;
  }

  // Intelligent text chunking with context preservation
  chunkText(text, maxChunkSize = 1000, overlap = 150) {
    const paragraphSeparator = '\n\n';
    const chunks = [];
    
    // First split by meaningful paragraphs
    const paragraphs = text.split(paragraphSeparator).filter(p => p.trim());
    
    for (const paragraph of paragraphs) {
      // Process large paragraphs individually
      if (paragraph.length > maxChunkSize) {
        const sentences = paragraph.split(/(?<=[.!?])\s+/);
        let currentChunk = '';
        
        for (const sentence of sentences) {
          const trimmed = sentence.trim();
          if (!trimmed) continue;
          
          if (currentChunk.length + trimmed.length > maxChunkSize) {
            if (currentChunk) chunks.push(currentChunk);
            currentChunk = trimmed;
          } else {
            currentChunk += (currentChunk ? ' ' : '') + trimmed;
          }
        }
        
        if (currentChunk) chunks.push(currentChunk);
      } else {
        chunks.push(paragraph);
      }
    }
    
    // Add overlap between chunks for context
    const finalChunks = [];
    for (let i = 0; i < chunks.length; i++) {
      let chunk = chunks[i];
      
      // Add prefix from previous chunk
      if (i > 0) {
        const prevEnd = finalChunks[i-1].slice(-overlap);
        chunk = prevEnd + ' ' + chunk;
      }
      
      // Add suffix to next chunk
      if (i < chunks.length - 1) {
        const nextStart = chunks[i+1].substring(0, Math.min(overlap, chunks[i+1].length));
        chunk += ' ' + nextStart;
      }
      
      finalChunks.push(chunk);
    }
    
    return finalChunks;
  }

  // Detect document sections based on formatting patterns
  detectSections(lines) {
    const sections = [];
    let currentSection = { title: '', content: [] };

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Detect section headers by content patterns
      const isHeader = (
        line.text.length > 10 &&
        line.text.length < 100 &&
        line.text === line.text.toUpperCase() &&
        !/[.!?]$/.test(line.text) &&
        !line.text.includes(' ')
      );
      
      if (isHeader) {
        // Finalize current section
        if (currentSection.title || currentSection.content.length > 0) {
          sections.push({...currentSection});
        }
        // Start new section
        currentSection = {
          title: line.text,
          content: []
        };
      } else {
        currentSection.content.push(line.text);
      }
    }
    
    // Add final section
    if (currentSection.title || currentSection.content.length > 0) {
      sections.push({...currentSection});
    }
    
    this.sections = sections;
    return sections;
  }
}

// Improved text extraction with style analysis
async function render_page(pageData) {
  const render_options = {
    normalizeWhitespace: false,
    disableCombineTextItems: false
  };

  try {
    const textContent = await pageData.getTextContent(render_options);
    return extractTextFromContent(textContent);
  } catch (err) {
    console.error('Error in text extraction:', err);
    return '';
  }
}

// Enhanced text extraction with style preservation
function extractTextFromContent(textContent) {
  if (!textContent || !textContent.items) return '';
  
  // Group items by approximate Y position (lines)
  const lines = [];
  let currentLine = { items: [], minY: Infinity, maxY: -Infinity };
  
  textContent.items.forEach(item => {
    const y = item.transform[5];
    
    // New line detection
    if (currentLine.items.length > 0 && 
        Math.abs(y - currentLine.minY) > (currentLine.maxY - currentLine.minY) * 0.5) {
      lines.push({...currentLine});
      currentLine = { items: [], minY: Infinity, maxY: -Infinity };
    }
    
    // Update current line
    currentLine.items.push({
      text: item.str,
      x: item.transform[4],
      y: y,
      width: item.width || 0,
      height: item.height || 0
    });
    currentLine.minY = Math.min(currentLine.minY, y);
    currentLine.maxY = Math.max(currentLine.maxY, y);
  });
  
  // Add last line
  if (currentLine.items.length > 0) lines.push(currentLine);
  
  // Build text output
  let text = '';
  lines.forEach(line => {
    // Sort items left to right
    line.items.sort((a, b) => a.x - b.x);
    
    let lineText = '';
    let lastX = -Infinity;
    
    line.items.forEach(item => {
      // Add space if significant gap
      if (item.x > lastX + (item.width || 10)) {
        lineText += ' ';
      }
      
      lineText += item.text;
      lastX = item.x + (item.width || 0);
    });
    
    text += lineText.trim() + '\n';
  });
  
  return text;
}

// Enhanced Markdown conversion with style handling
function cleanTextToMarkdown(text) {
  if (!text || typeof text !== 'string') return '';
  
  const processor = new DocumentProcessor();
  text = processor.normalizeUnicode(text);
  
  // Extract metadata
  const metadata = processor.extractMetadata(text);
  
  // Pre-process text
  text = text
    .replace(/\r\n/g, '\n')
    .replace(/\r/g, '\n')
    .replace(/\n{3,}/g, '\n\n')
    .replace(/([^\n])\n([^\n])/g, '$1 $2') // Fix single newlines
    .replace(/\s*([.,:;!?])/g, '$1')       // Fix punctuation spacing
    .replace(/(\d)\s+(\d)/g, '$1$2');      // Fix number separation

  // Split into lines for section detection
  const lines = text.split('\n').map(line => ({
    text: line.trim()
  })).filter(line => line.text);
  
  processor.detectSections(lines);
  
  // Build Markdown
  let markdown = '---\n';
  markdown += `title: "${metadata.title || 'Corporate Document'}"\n`;
  markdown += `date: "${metadata.date || 'Unknown'}"\n`;
  markdown += `company: "${metadata.company || 'Unknown Company'}"\n`;
  markdown += `type: "${metadata.documentType || 'Notice'}"\n`;
  markdown += `language: "${metadata.language}"\n`;
  markdown += `subject: "${metadata.subject || ''}"\n`;
  markdown += '---\n\n';
  
  markdown += `# ${metadata.title || metadata.documentType}\n\n`;
  
  // Add sections
  processor.sections.forEach((section, index) => {
    if (section.title) {
      markdown += `## ${section.title}\n\n`;
    } else if (index === 0) {
      markdown += '## Introduction\n\n';
    }
    
    section.content.forEach(line => {
      // Detect list items
      if (/^\s*[\-*•]|\d+\./.test(line)) {
        markdown += line.replace(/^\s*/, '- ') + '\n';
      } 
      // Detect blockquotes
      else if (/^\s*>/.test(line)) {
        markdown += '> ' + line.replace(/^\s*>/, '') + '\n';
      }
      // Regular paragraph
      else {
        markdown += line + '\n\n';
      }
    });
  });
  
  // Add tables
  if (processor.tables.length > 0) {
    markdown += '\n## Tables\n\n';
    processor.tables.forEach((table, i) => {
      markdown += `### Table ${i+1}\n`;
      markdown += table + '\n';
    });
  }
  
  // Add document summary
  markdown += '\n---\n\n## Document Summary\n';
  markdown += `- **Sections:** ${processor.sections.length}\n`;
  markdown += `- **Tables:** ${processor.tables.length}\n`;
  markdown += `- **Word Count:** ${text.split(/\s+/).length}\n`;
  markdown += `- **Language:** ${metadata.language}\n`;
  
  return markdown;
}

// Main function with improved error handling
async function pdfUrlToMarkdown(pdfUrl, outputFile = null) {
  try {
    console.log(`Processing: ${pdfUrl}`);
    
    // Download PDF
    const response = await axios.get(pdfUrl, {
      responseType: 'arraybuffer',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/pdf',
        'Referer': 'https://www.nseindia.com/'
      },
      timeout: 30000
    });

    if (!response.data) throw new Error('Empty PDF response');
    
    // Parse PDF
    const data = await pdf(response.data, {
      max: 0,
      pagerender: render_page
    });
    
    // Convert to Markdown
    const markdown = cleanTextToMarkdown(data.text);
    
    // Output results
    if (outputFile) {
      fs.writeFileSync(outputFile, markdown);
      return `Saved to ${outputFile}`;
    }
    return markdown;
  } catch (error) {
    console.error('Processing failed:');
    console.error(`- URL: ${pdfUrl}`);
    console.error(`- Error: ${error.message}`);
    
    if (error.response) {
      console.error(`- HTTP Status: ${error.response.status}`);
    }
    
    throw new Error('Conversion failed. See logs for details.');
  }
}

// Command-line execution
if (require.main === module) {
  if (process.argv.length < 3) {
    console.log('Intelligent PDF-to-Markdown Converter v4.0');
    console.log('Usage: node pdf-to-md.js <pdf-url> [output-file.md]');
    console.log('Features:');
    console.log('- Punjabi/Gurmukhi language support');
    console.log('- Intelligent table extraction');
    console.log('- Section-aware document structuring');
    console.log('- Enhanced metadata extraction');
    console.log('- Context-preserving text chunking');
    process.exit(1);
  }

  const pdfUrl = process.argv[2];
  const outputFile = process.argv[3] || null;

  pdfUrlToMarkdown(pdfUrl, outputFile)
    .then(result => {
      if (!outputFile) console.log('\n' + result);
      else console.log(`✓ Converted successfully: ${result}`);
    })
    .catch(err => {
      console.error(err.message);
      process.exit(1);
    });
}

module.exports = { pdfUrlToMarkdown, cleanTextToMarkdown };