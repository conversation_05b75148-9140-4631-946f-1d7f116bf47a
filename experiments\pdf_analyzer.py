#!/usr/bin/env python3
"""
PDF Analyzer using OpenRouter API
Extracts text from PDF files and analyzes them using AI models via OpenRouter.
"""

import requests
import json
import sys
import os
from pathlib import Path
import argparse
from typing import Optional, Dict, Any
import tempfile
from urllib.parse import urlparse

try:
    import PyPDF2
except ImportError:
    print("PyPDF2 not found. Installing...")
    os.system("pip install PyPDF2")
    import PyPDF2

try:
    import pdfplumber
except ImportError:
    print("pdfplumber not found. Installing...")
    os.system("pip install pdfplumber")
    import pdfplumber


class PDFAnalyzer:
    def __init__(self, api_key: str, site_url: str = "", site_name: str = "PDF Analyzer"):
        """
        Initialize PDF Analyzer with OpenRouter API credentials.
        
        Args:
            api_key: OpenRouter API key
            site_url: Optional site URL for rankings
            site_name: Optional site name for rankings
        """
        self.api_key = api_key
        self.site_url = site_url
        self.site_name = site_name
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """Extract text from PDF using PyPDF2."""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text.strip()
        except Exception as e:
            print(f"Error extracting text with PyPDF2: {e}")
            return ""
    
    def extract_text_pdfplumber(self, pdf_path: str) -> str:
        """Extract text from PDF using pdfplumber (better for complex layouts)."""
        try:
            text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            return text.strip()
        except Exception as e:
            print(f"Error extracting text with pdfplumber: {e}")
            return ""
    
    def download_pdf_from_url(self, url: str) -> str:
        """
        Download PDF from URL to temporary file.

        Args:
            url: URL to PDF file

        Returns:
            Path to downloaded temporary file
        """
        print(f"Downloading PDF from URL: {url}")

        try:
            # Set headers to mimic a browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/pdf,application/octet-stream,*/*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # Download the PDF with longer timeout and headers
            print("Attempting to download PDF (this may take a moment)...")
            response = requests.get(url, timeout=60, stream=True, headers=headers)
            response.raise_for_status()

            # Check if response is actually a PDF
            content_type = response.headers.get('content-type', '').lower()
            if 'pdf' not in content_type and 'application/octet-stream' not in content_type:
                print(f"Warning: Content-Type is '{content_type}', may not be a PDF")

            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')

            # Write PDF content to temporary file with progress indication
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    temp_file.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rDownload progress: {percent:.1f}%", end='', flush=True)

            print()  # New line after progress
            temp_file.close()

            # Verify file size
            file_size = os.path.getsize(temp_file.name)
            print(f"PDF downloaded successfully: {file_size} bytes")
            print(f"Temporary file: {temp_file.name}")

            return temp_file.name

        except requests.exceptions.Timeout:
            raise ValueError(f"Download timed out. The server may be slow or the file may be large.")
        except requests.exceptions.RequestException as e:
            raise ValueError(f"Failed to download PDF from URL: {e}")

    def is_url(self, path: str) -> bool:
        """Check if the given path is a URL."""
        parsed = urlparse(path)
        return parsed.scheme in ('http', 'https')

    def extract_pdf_text(self, pdf_path: str, method: str = "pdfplumber") -> str:
        """
        Extract text from PDF file or URL.

        Args:
            pdf_path: Path to PDF file or URL
            method: Extraction method ('pdfplumber' or 'pypdf2')

        Returns:
            Extracted text content
        """
        temp_file = None

        try:
            # Handle URL downloads
            if self.is_url(pdf_path):
                temp_file = self.download_pdf_from_url(pdf_path)
                actual_path = temp_file
            else:
                if not os.path.exists(pdf_path):
                    raise FileNotFoundError(f"PDF file not found: {pdf_path}")
                actual_path = pdf_path

            print(f"Extracting text from PDF using {method}...")

            if method == "pdfplumber":
                text = self.extract_text_pdfplumber(actual_path)
                if not text:  # Fallback to PyPDF2 if pdfplumber fails
                    print("pdfplumber failed, trying PyPDF2...")
                    text = self.extract_text_pypdf2(actual_path)
            else:
                text = self.extract_text_pypdf2(actual_path)
                if not text:  # Fallback to pdfplumber if PyPDF2 fails
                    print("PyPDF2 failed, trying pdfplumber...")
                    text = self.extract_text_pdfplumber(actual_path)

            if not text:
                raise ValueError("Could not extract text from PDF")

            return text

        finally:
            # Clean up temporary file if it was created
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                    print("Temporary file cleaned up")
                except OSError:
                    pass  # Ignore cleanup errors
    
    def analyze_with_openrouter(self, text: str, analysis_type: str = "general") -> Dict[str, Any]:
        """
        Analyze text using OpenRouter API.
        
        Args:
            text: Text content to analyze
            analysis_type: Type of analysis to perform
            
        Returns:
            API response with analysis
        """
        # Truncate text if too long (most models have token limits)
        max_chars = 8000  # Approximate token limit consideration
        if len(text) > max_chars:
            text = text[:max_chars] + "...\n[Content truncated due to length]"
        
        # Define different analysis prompts
        prompts = {
            "general": f"""Please analyze this PDF document and provide:
1. Document Summary (2-3 sentences)
2. Document Type/Category
3. Key Topics/Themes
4. Important Information/Highlights
5. Main Entities (people, organizations, dates, etc.)

Document Content:
{text}""",
            
            "summary": f"""Please provide a comprehensive summary of this document, highlighting the most important points and key information:

{text}""",
            
            "entities": f"""Extract and list all important entities from this document including:
- People/Names
- Organizations/Companies
- Dates
- Locations
- Financial figures
- Key terms/concepts

Document:
{text}""",
            
            "insights": f"""Analyze this document and provide insights about:
- Purpose and intent
- Target audience
- Key decisions or actions mentioned
- Important deadlines or dates
- Potential implications or significance

Document:
{text}"""
        }
        
        prompt = prompts.get(analysis_type, prompts["general"])
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        if self.site_url:
            headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            headers["X-Title"] = self.site_name
        
        payload = {
            "model": "deepseek/deepseek-r1-0528:free",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }
        
        try:
            print("Analyzing document with AI...")
            response = requests.post(
                self.api_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=60
            )
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Response: {e.response.text}")
            raise
    
    def analyze_pdf(self, pdf_path: str, analysis_type: str = "general", 
                   extraction_method: str = "pdfplumber") -> Dict[str, Any]:
        """
        Complete PDF analysis workflow.
        
        Args:
            pdf_path: Path to PDF file
            analysis_type: Type of analysis ('general', 'summary', 'entities', 'insights')
            extraction_method: PDF extraction method
            
        Returns:
            Analysis results
        """
        # Extract text from PDF
        text = self.extract_pdf_text(pdf_path, extraction_method)
        
        # Analyze with OpenRouter
        analysis = self.analyze_with_openrouter(text, analysis_type)
        
        return {
            "pdf_path": pdf_path,
            "text_length": len(text),
            "analysis_type": analysis_type,
            "analysis": analysis
        }


def main():
    parser = argparse.ArgumentParser(description="Analyze PDF documents using OpenRouter AI")
    parser.add_argument("pdf_path", help="Path to PDF file or URL to PDF")
    parser.add_argument("--api-key", required=True, help="OpenRouter API key")
    parser.add_argument("--analysis-type", choices=["general", "summary", "entities", "insights"], 
                       default="general", help="Type of analysis to perform")
    parser.add_argument("--extraction-method", choices=["pdfplumber", "pypdf2"], 
                       default="pdfplumber", help="PDF text extraction method")
    parser.add_argument("--site-url", default="", help="Site URL for OpenRouter rankings")
    parser.add_argument("--site-name", default="PDF Analyzer", help="Site name for OpenRouter rankings")
    parser.add_argument("--output", help="Output file to save results (JSON format)")
    
    args = parser.parse_args()
    
    try:
        # Initialize analyzer
        analyzer = PDFAnalyzer(
            api_key=args.api_key,
            site_url=args.site_url,
            site_name=args.site_name
        )
        
        # Analyze PDF
        results = analyzer.analyze_pdf(
            pdf_path=args.pdf_path,
            analysis_type=args.analysis_type,
            extraction_method=args.extraction_method
        )
        
        # Display results
        print("\n" + "="*60)
        print("PDF ANALYSIS RESULTS")
        print("="*60)
        print(f"File: {results['pdf_path']}")
        print(f"Text Length: {results['text_length']} characters")
        print(f"Analysis Type: {results['analysis_type']}")
        print("\nAI Analysis:")
        print("-"*40)
        
        if 'choices' in results['analysis'] and results['analysis']['choices']:
            content = results['analysis']['choices'][0]['message']['content']
            print(content)
        else:
            print("No analysis content received")
            print("Full response:", json.dumps(results['analysis'], indent=2))
        
        # Save to file if requested
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to: {args.output}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
