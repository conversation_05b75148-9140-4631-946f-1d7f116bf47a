// Test script to verify Gurmukhi Unicode fixes
console.log('Testing Gurmukhi Unicode Fixes');
console.log('='.repeat(50));

// Test the corrupted text sample provided by the user
const corruptedText = "R?rl 6 H fe\'a◊√ Í≈‡∆ ÚÒØ∫ Í≥‹≈Ï Á∂ 'Í≈‡∆ ̆ Ï»Ê ÍoË Âo' Ó ̃Ï»Â '∆Â≈«ÚÚ∂' «√≥◊Ò≈";

console.log('\nOriginal corrupted text:');
console.log(corruptedText);

// Simple function to apply some of the fixes we implemented
function fixGurmukhiText(text) {
    if (!text || typeof text !== 'string') return '';

    // Apply some of the common fixes from our implementation
    const fixes = [
        // Common words and phrases
        [/Í≈‡∆/g, 'ਪਾਰਤੀ'],
        [/ÚÒØ∫/g, 'ਵਲੋਂ'],
        [/Í≥‹≈Ï/g, 'ਪੰਜਾਬ'],
        [/Á∂/g, 'ਦੇ'],
        [/«√≥◊Ò≈/g, 'ਸਿੰਗਲਾ'],
        [/«ÚÚ∂'/g, 'ਵਿਵੇਕ'],
        [/Á∆/g, 'ਦੀ'],
        [/È∂/g, 'ਨੇ'],
        [/È≈/g, 'ਨਾ'],
        [/È∆/g, 'ਨੀ'],
        [/Ò¬∆/g, 'ਲਈ'],
        [/√∂/g, 'ਸੇ'],
        [/√≈/g, 'ਸਾ'],
        [/√∆/g, 'ਸੀ'],
        [/'∂/g, 'ਕੇ'],
        [/'≈/g, 'ਕਾ'],
        [/'∆/g, 'ਕੀ'],
        [/◊∂/g, 'ਗੇ'],
        [/◊≈/g, 'ਗਾ'],
        [/◊∆/g, 'ਗੀ'],
        [/Ó∂/g, 'ਮੇ'],
        [/Ó≈/g, 'ਮਾ'],
        [/Ó∆/g, 'ਮੀ'],
        [/Ú∂/g, 'ਵੇ'],
        [/Ú≈/g, 'ਵਾ'],
        [/Ú∆/g, 'ਵੀ'],
        [/‹∂/g, 'ਜੇ'],
        [/‹≈/g, 'ਜਾ'],
        [/‹∆/g, 'ਜੀ'],
        [/Ï∂/g, 'ਬੇ'],
        [/Ï≈/g, 'ਬਾ'],
        [/Ï∆/g, 'ਬੀ'],
        [/Ò∂/g, 'ਲੇ'],
        [/Ò≈/g, 'ਲਾ'],
        [/Ò∆/g, 'ਲੀ'],
        [/⁄∂/g, 'ਚੇ'],
        [/⁄≈/g, 'ਚਾ'],
        [/⁄∆/g, 'ਚੀ'],
        [/Ë∂/g, 'ਧੇ'],
        [/Ë≈/g, 'ਧਾ'],
        [/Ë∆/g, 'ਧੀ'],
        [/Ù∂/g, 'ਸ਼ੇ'],
        [/Ù≈/g, 'ਸ਼ਾ'],
        [/Ù∆/g, 'ਸ਼ੀ'],
        [/«''≈/g, 'ਕਿਹਾ'],
        [/«◊¡≈/g, 'ਗਿਆ'],
        [/'∆Â≈/g, 'ਕੀਤਾ'],
        [/'∆Â∆/g, 'ਕੀਤੀ'],
        [/«Ú⁄/g, 'ਵਿਚ'],
    ];

    // Apply all fixes
    for (const [pattern, replacement] of fixes) {
        text = text.replace(pattern, replacement);
    }

    return text;
}

const fixedText = fixGurmukhiText(corruptedText);

console.log('\nAfter applying Gurmukhi fixes:');
console.log(fixedText);

console.log('\n' + '='.repeat(50));
console.log('Individual word tests:');

const testWords = [
    'Í≈‡∆',
    'ÚÒØ∫',
    'Í≥‹≈Ï',
    'Á∂',
    'Í∞',
    'Ó∞',
    'È∂',
    'È≈',
    'È∆'
];

testWords.forEach(word => {
    const fixed = fixGurmukhiText(word);
    console.log(`${word} -> ${fixed}`);
});

console.log('\n' + '='.repeat(50));
console.log('Test completed!');
