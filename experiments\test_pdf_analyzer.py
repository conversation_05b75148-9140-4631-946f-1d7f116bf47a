#!/usr/bin/env python3
"""
Test script for PDF Analyzer with different PDF sources
"""

from tests.pdf_analyzer import PDFAnalyzer
import sys

def test_with_sample_pdf():
    """Test with a reliable sample PDF URL"""
    
    API_KEY = "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"
    
    # Use a reliable sample PDF URL (this is a sample PDF from Mozilla)
    sample_pdf_url = "https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf"
    
    print("Testing PDF Analyzer with sample PDF...")
    print(f"PDF URL: {sample_pdf_url}")
    print("=" * 60)
    
    try:
        analyzer = PDFAnalyzer(api_key=API_KEY)
        
        # Test PDF analysis
        print("Starting analysis...")
        results = analyzer.analyze_pdf(sample_pdf_url, analysis_type="summary")
        
        print("\n" + "="*60)
        print("ANALYSIS RESULTS")
        print("="*60)
        
        if 'choices' in results['analysis'] and results['analysis']['choices']:
            analysis_content = results['analysis']['choices'][0]['message']['content']
            print(analysis_content)
        else:
            print("No analysis content received")
            print("Full API response:")
            print(results['analysis'])
        
        print("\n" + "="*60)
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Error during test: {e}")
        return False
    
    return True

def test_with_nse_pdf():
    """Test with the NSE PDF (may have connection issues)"""
    
    API_KEY = "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"
    nse_pdf_url = "https://nsearchives.nseindia.com/corporate/SWARAJENG_20062025163533_SELAGMThruVCNoticeAd20062025.pdf"
    
    print("Testing PDF Analyzer with NSE PDF...")
    print(f"PDF URL: {nse_pdf_url}")
    print("=" * 60)
    
    try:
        analyzer = PDFAnalyzer(api_key=API_KEY)
        
        # Test PDF analysis
        print("Starting analysis...")
        results = analyzer.analyze_pdf(nse_pdf_url, analysis_type="general")
        
        print("\n" + "="*60)
        print("NSE PDF ANALYSIS RESULTS")
        print("="*60)
        
        if 'choices' in results['analysis'] and results['analysis']['choices']:
            analysis_content = results['analysis']['choices'][0]['message']['content']
            print(analysis_content)
        else:
            print("No analysis content received")
            print("Full API response:")
            print(results['analysis'])
        
        print("\n" + "="*60)
        print("NSE PDF test completed successfully!")
        
    except Exception as e:
        print(f"Error during NSE PDF test: {e}")
        return False
    
    return True

def main():
    print("PDF Analyzer Test Suite")
    print("=" * 40)
    
    choice = input("Test with (1) Sample PDF or (2) NSE PDF or (3) Both? Enter 1, 2, or 3: ")
    
    if choice == "1":
        test_with_sample_pdf()
    elif choice == "2":
        test_with_nse_pdf()
    elif choice == "3":
        print("Testing with sample PDF first...")
        if test_with_sample_pdf():
            print("\n" + "="*60)
            print("Now testing with NSE PDF...")
            test_with_nse_pdf()
    else:
        print("Invalid choice. Testing with sample PDF...")
        test_with_sample_pdf()

if __name__ == "__main__":
    main()
