"""
Final BSE Corporate Announcements Scraper
Working version based on actual testing with correct field names
"""

import bsescraper
import pandas as pd
from datetime import datetime, timedelta

def get_company_code(bs, company_name):
    """Get company code for a given company name"""
    try:
        code = bs.get_code(name=company_name)
        return code
    except Exception as e:
        print(f"❌ Error getting code for {company_name}: {e}")
        return None

def get_announcements_for_company(bs, company_name, company_code, start_date, end_date, category="Company Update"):
    """Get announcements for a specific company"""
    
    start_str = start_date.strftime('%d/%m/%Y')
    end_str = end_date.strftime('%d/%m/%Y')
    
    try:
        announcements = bs.get_corporate_ann(
            code=company_code,
            category=category,
            startdate=start_str,
            enddate=end_str
        )
        
        if announcements and len(announcements) > 0:
            # Add company info to each announcement
            for ann in announcements:
                if isinstance(ann, dict):
                    ann['company_name'] = company_name
                    ann['company_code'] = company_code
                    ann['category'] = category
            
            return announcements
        else:
            return []
            
    except Exception as e:
        print(f"❌ Error getting announcements for {company_name}: {e}")
        return []

def display_announcements(announcements, title="Announcements"):
    """Display announcements in a formatted way"""
    
    if not announcements:
        print("No announcements to display")
        return
    
    print(f"\n📊 {title}")
    print("=" * 80)
    
    for i, ann in enumerate(announcements[:10], 1):  # Show first 10
        if isinstance(ann, dict):
            headline = ann.get('Headline', 'No headline')
            subject = ann.get('Subject', 'No subject')
            date = ann.get('Date', 'No date')
            company = ann.get('company_name', 'Unknown company')
            
            print(f"\n{i}. 🏢 {company}")
            print(f"   📰 {headline}")
            print(f"   📝 {subject}")
            print(f"   📅 {date}")
        else:
            print(f"{i}. {ann}")
        
        print("-" * 80)
    
    if len(announcements) > 10:
        print(f"\n... and {len(announcements) - 10} more announcements")

def save_announcements(bs, announcements, filename):
    """Save announcements to CSV file"""
    
    if not announcements:
        print("No data to save")
        return False
    
    try:
        df = bs.dataframe(announcements)
        result = bs.save(df, filename)
        
        if result == "Saved":
            print(f"✅ Data saved to {filename}.csv")
            return True
        else:
            print(f"❌ Failed to save data")
            return False
            
    except Exception as e:
        print(f"❌ Error saving to CSV: {e}")
        return False

def main():
    """Main function to demonstrate BSE announcements scraping"""
    
    print("🏢 Final BSE Corporate Announcements Scraper")
    print("=" * 60)
    print("✅ Working version with correct field names")
    print()
    
    # Initialize BSE scraper
    bs = bsescraper.BSE()
    
    # Companies to scrape (you can add more)
    companies_to_scrape = [
        "HDFC Bank Ltd",
        "Reliance Industries Ltd",
        "TCS Ltd",
        "Infosys Ltd",
        "ICICI Bank Ltd"
    ]
    
    # Date range - last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"📅 Date range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")
    print()
    
    all_announcements = []
    successful_companies = []
    
    # Get announcements for each company
    for company_name in companies_to_scrape:
        print(f"🔍 Processing: {company_name}")
        
        # Get company code
        company_code = get_company_code(bs, company_name)
        
        if not company_code:
            print(f"❌ Could not find company code for {company_name}")
            continue
        
        print(f"✅ Company code: {company_code}")
        
        # Get announcements
        announcements = get_announcements_for_company(
            bs, company_name, company_code, start_date, end_date
        )
        
        if announcements:
            print(f"✅ Found {len(announcements)} announcements")
            all_announcements.extend(announcements)
            successful_companies.append(company_name)
            
            # Save individual company data
            filename = company_name.lower().replace(" ", "_").replace("ltd", "").strip("_")
            save_announcements(bs, announcements, f"{filename}_announcements")
            
        else:
            print(f"❌ No announcements found")
        
        print("-" * 50)
    
    # Display summary
    print(f"\n📊 SCRAPING SUMMARY")
    print("=" * 60)
    print(f"Companies processed: {len(companies_to_scrape)}")
    print(f"Successful companies: {len(successful_companies)}")
    print(f"Total announcements: {len(all_announcements)}")
    
    if successful_companies:
        print(f"\n✅ Successful companies:")
        for company in successful_companies:
            company_announcements = [ann for ann in all_announcements if ann.get('company_name') == company]
            print(f"  - {company}: {len(company_announcements)} announcements")
    
    # Save combined data
    if all_announcements:
        print(f"\n💾 Saving combined data...")
        save_announcements(bs, all_announcements, "all_bse_announcements")
        
        # Display sample announcements
        display_announcements(all_announcements, "Sample BSE Corporate Announcements")
        
        # Show data structure
        if len(all_announcements) > 0:
            print(f"\n📋 Data Structure:")
            sample = all_announcements[0]
            if isinstance(sample, dict):
                print(f"Available fields: {list(sample.keys())}")
    
    # Additional categories test
    if successful_companies:
        print(f"\n🔍 Testing Additional Categories")
        print("=" * 60)
        
        test_company = successful_companies[0]
        test_code = get_company_code(bs, test_company)
        
        categories = ["Results", "Board Meeting", "Corp. Action"]
        
        for category in categories:
            print(f"\nTesting {category} for {test_company}...")
            announcements = get_announcements_for_company(
                bs, test_company, test_code, start_date, end_date, category
            )
            
            if announcements:
                print(f"✅ Found {len(announcements)} {category} announcements")
                save_announcements(bs, announcements, f"{test_company.lower().replace(' ', '_')}_{category.lower().replace(' ', '_')}")
            else:
                print(f"❌ No {category} announcements found")
    
    print(f"\n🎉 Scraping completed!")
    print(f"📁 Check the generated CSV files for detailed data")

if __name__ == "__main__":
    main()
