-- =====================================================
-- Master Corporate Announcements Table Schema
-- Unified table for NSE and BSE corporate announcements
-- =====================================================

-- Create the master corporate announcements table
CREATE TABLE IF NOT EXISTS master_corporate_announcements (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Deduplication key (primary identifier across exchanges)
    pdf_hash VARCHAR(255) UNIQUE NOT NULL,
    
    -- Source tracking columns
    nse_source_id BIGINT REFERENCES nse_corporate_announcements(id),
    bse_source_id UUID REFERENCES bse_corporate_announcements(id),
    source_exchange VARCHAR(10) NOT NULL CHECK (source_exchange IN ('NSE', 'BSE')),
    has_nse_data BOOLEAN DEFAULT FALSE,
    has_bse_data BOOLEAN DEFAULT FALSE,
    
    -- Unified company information
    company_name VARCHAR(500),
    nse_symbol VARCHAR(50),        -- NSE-specific symbol
    bse_code VARCHAR(20),          -- BSE-specific code
    
    -- Unified announcement content
    subject TEXT,                  -- Primary subject/headline
    headline TEXT,                 -- BSE headline or NSE subject
    details TEXT,                  -- NSE details or BSE more content
    category VARCHAR(200),         -- BSE categoryname
    subcategory VARCHAR(200),      -- BSE subcatname
    classification TEXT,           -- BSE-specific parsed classification
    parsed_subject TEXT,           -- BSE-specific parsed subject
    
    -- Unified timing information
    announcement_date TIMESTAMPTZ, -- Primary announcement date
    submission_date TIMESTAMPTZ,   -- News submission date
    dissemination_date TIMESTAMPTZ,-- Dissemination date
    broadcast_date TIMESTAMPTZ,    -- NSE broadcast date
    receipt_date TIMESTAMPTZ,      -- NSE receipt date
    time_difference VARCHAR(50),   -- Time difference info
    difference_seconds INTEGER,    -- NSE difference in seconds
    
    -- Unified attachment information
    attachment_url TEXT,           -- Primary attachment URL
    attachment_file TEXT,          -- BSE attachment file
    file_size_bytes INTEGER,       -- File size in bytes
    file_size_text VARCHAR(50),    -- NSE file size text
    xml_data_url TEXT,             -- BSE XML data URL
    
    -- Exchange-specific metadata
    announcement_type VARCHAR(50), -- BSE announcement type
    file_status VARCHAR(10),       -- BSE file status
    critical_news VARCHAR(10),     -- BSE critical news flag
    nsurl TEXT,                    -- BSE company URL
    
    -- Data quality and processing
    is_duplicate BOOLEAN DEFAULT FALSE,
    duplicate_check_status VARCHAR(20) DEFAULT 'pending',
    data_completeness_score DECIMAL(3,2), -- Score 0.00-1.00 based on filled fields
    
    -- Audit columns
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    scraped_at TIMESTAMPTZ,
    last_nse_update TIMESTAMPTZ,
    last_bse_update TIMESTAMPTZ
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_pdf_hash ON master_corporate_announcements(pdf_hash);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_source_exchange ON master_corporate_announcements(source_exchange);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_company_name ON master_corporate_announcements(company_name);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_announcement_date ON master_corporate_announcements(announcement_date);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_nse_symbol ON master_corporate_announcements(nse_symbol);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_bse_code ON master_corporate_announcements(bse_code);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_category ON master_corporate_announcements(category);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_has_nse_data ON master_corporate_announcements(has_nse_data);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_has_bse_data ON master_corporate_announcements(has_bse_data);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_duplicate_status ON master_corporate_announcements(is_duplicate, duplicate_check_status);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_source_tracking ON master_corporate_announcements(source_exchange, has_nse_data, has_bse_data);
CREATE INDEX IF NOT EXISTS idx_master_corp_ann_date_range ON master_corporate_announcements(announcement_date, source_exchange);

-- Add comments for documentation
COMMENT ON TABLE master_corporate_announcements IS 'Unified master table consolidating corporate announcements from NSE and BSE exchanges';
COMMENT ON COLUMN master_corporate_announcements.pdf_hash IS 'Primary deduplication key across all exchanges';
COMMENT ON COLUMN master_corporate_announcements.source_exchange IS 'Primary source exchange (NSE or BSE)';
COMMENT ON COLUMN master_corporate_announcements.has_nse_data IS 'Indicates if this record has data from NSE';
COMMENT ON COLUMN master_corporate_announcements.has_bse_data IS 'Indicates if this record has data from BSE';
COMMENT ON COLUMN master_corporate_announcements.data_completeness_score IS 'Score indicating data completeness (0.00-1.00)';

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_master_corp_ann_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_master_corp_ann_updated_at
    BEFORE UPDATE ON master_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION update_master_corp_ann_updated_at();
