-- Migration: Add PDF duplicate detection system
-- Project: corporate_announcements (yvuwseolouiqhoxsieop)
-- Date: 2025-07-16
-- Description: Adds duplicate detection columns, indexes, and functions for PDF attachments

BEGIN;

-- ============================================================================
-- 1. ADD COLUMNS TO EXISTING TABLES
-- ============================================================================

-- Add is_duplicate column to bse_corporate_announcements
ALTER TABLE bse_corporate_announcements 
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE NOT NULL;

-- Add pdf_hash column to store SHA-256 hash of PDF content
ALTER TABLE bse_corporate_announcements 
ADD COLUMN IF NOT EXISTS pdf_hash VARCHAR(64);

-- Add is_duplicate column to nse_corporate_announcements
ALTER TABLE nse_corporate_announcements 
ADD COLUMN IF NOT EXISTS is_duplicate BOOLEAN DEFAULT FALSE NOT NULL;

-- Add pdf_hash column to store SHA-256 hash of PDF content
ALTER TABLE nse_corporate_announcements 
ADD COLUMN IF NOT EXISTS pdf_hash VARCHAR(64);

-- ============================================================================
-- 2. CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Index on PDF URLs for fast lookup
CREATE INDEX IF NOT EXISTS idx_bse_attachmentfile 
ON bse_corporate_announcements(attachmentfile) 
WHERE attachmentfile IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_nse_attachment_url 
ON nse_corporate_announcements(attachment_url) 
WHERE attachment_url IS NOT NULL;

-- Index on PDF hashes for fast duplicate detection
CREATE INDEX IF NOT EXISTS idx_bse_pdf_hash 
ON bse_corporate_announcements(pdf_hash) 
WHERE pdf_hash IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_nse_pdf_hash 
ON nse_corporate_announcements(pdf_hash) 
WHERE pdf_hash IS NOT NULL;

-- Composite index for duplicate detection queries
CREATE INDEX IF NOT EXISTS idx_bse_duplicate_detection 
ON bse_corporate_announcements(is_duplicate, pdf_hash, created_at);

CREATE INDEX IF NOT EXISTS idx_nse_duplicate_detection 
ON nse_corporate_announcements(is_duplicate, pdf_hash, created_at);

-- ============================================================================
-- 3. CREATE UNIFIED PDF TRACKING TABLE
-- ============================================================================

-- Table to track all PDF URLs and their hashes across both exchanges
CREATE TABLE IF NOT EXISTS pdf_registry (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pdf_url TEXT NOT NULL,
    pdf_hash VARCHAR(64),
    source_table VARCHAR(50) NOT NULL, -- 'bse_corporate_announcements' or 'nse_corporate_announcements'
    source_record_id TEXT NOT NULL, -- UUID for BSE, bigint for NSE (stored as text)
    first_seen_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    hash_computed_at TIMESTAMP WITH TIME ZONE,
    hash_computation_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'computed', 'failed'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for pdf_registry
CREATE UNIQUE INDEX IF NOT EXISTS idx_pdf_registry_url_unique 
ON pdf_registry(pdf_url);

CREATE INDEX IF NOT EXISTS idx_pdf_registry_hash 
ON pdf_registry(pdf_hash) 
WHERE pdf_hash IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_pdf_registry_source 
ON pdf_registry(source_table, source_record_id);

-- ============================================================================
-- 4. CREATE DUPLICATE DETECTION FUNCTIONS
-- ============================================================================

-- Function to check if a PDF URL already exists in the registry
CREATE OR REPLACE FUNCTION check_pdf_duplicate(
    p_pdf_url TEXT,
    p_source_table VARCHAR(50),
    p_source_record_id TEXT
) RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    existing_count INTEGER;
BEGIN
    -- Return FALSE if URL is NULL or empty
    IF p_pdf_url IS NULL OR TRIM(p_pdf_url) = '' THEN
        RETURN FALSE;
    END IF;
    
    -- Check if this exact URL already exists from a different record
    SELECT COUNT(*) INTO existing_count
    FROM pdf_registry 
    WHERE pdf_url = p_pdf_url 
    AND NOT (source_table = p_source_table AND source_record_id = p_source_record_id);
    
    RETURN existing_count > 0;
END;
$$;

-- Function to register a new PDF in the registry
CREATE OR REPLACE FUNCTION register_pdf(
    p_pdf_url TEXT,
    p_source_table VARCHAR(50),
    p_source_record_id TEXT,
    p_pdf_hash VARCHAR(64) DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    registry_id UUID;
BEGIN
    -- Return NULL if URL is NULL or empty
    IF p_pdf_url IS NULL OR TRIM(p_pdf_url) = '' THEN
        RETURN NULL;
    END IF;
    
    -- Insert or update the registry entry
    INSERT INTO pdf_registry (pdf_url, source_table, source_record_id, pdf_hash, hash_computation_status)
    VALUES (
        p_pdf_url, 
        p_source_table, 
        p_source_record_id, 
        p_pdf_hash,
        CASE WHEN p_pdf_hash IS NOT NULL THEN 'computed' ELSE 'pending' END
    )
    ON CONFLICT (pdf_url) DO UPDATE SET
        updated_at = NOW(),
        pdf_hash = COALESCE(EXCLUDED.pdf_hash, pdf_registry.pdf_hash),
        hash_computation_status = CASE 
            WHEN EXCLUDED.pdf_hash IS NOT NULL THEN 'computed' 
            ELSE pdf_registry.hash_computation_status 
        END,
        hash_computed_at = CASE 
            WHEN EXCLUDED.pdf_hash IS NOT NULL THEN NOW() 
            ELSE pdf_registry.hash_computed_at 
        END
    RETURNING id INTO registry_id;
    
    RETURN registry_id;
END;
$$;

-- ============================================================================
-- 5. CREATE TRIGGERS FOR AUTOMATIC DUPLICATE DETECTION
-- ============================================================================

-- Trigger function for BSE table
CREATE OR REPLACE FUNCTION bse_duplicate_detection_trigger()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Only process if attachmentfile is not null
    IF NEW.attachmentfile IS NOT NULL AND TRIM(NEW.attachmentfile) != '' THEN
        -- Check for duplicates and set the flag
        NEW.is_duplicate := check_pdf_duplicate(
            NEW.attachmentfile, 
            'bse_corporate_announcements', 
            NEW.id::TEXT
        );
        
        -- Register the PDF in the registry
        PERFORM register_pdf(
            NEW.attachmentfile,
            'bse_corporate_announcements',
            NEW.id::TEXT,
            NEW.pdf_hash
        );
    END IF;
    
    RETURN NEW;
END;
$$;

-- Trigger function for NSE table
CREATE OR REPLACE FUNCTION nse_duplicate_detection_trigger()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Only process if attachment_url is not null
    IF NEW.attachment_url IS NOT NULL AND TRIM(NEW.attachment_url) != '' THEN
        -- Check for duplicates and set the flag
        NEW.is_duplicate := check_pdf_duplicate(
            NEW.attachment_url, 
            'nse_corporate_announcements', 
            NEW.id::TEXT
        );
        
        -- Register the PDF in the registry
        PERFORM register_pdf(
            NEW.attachment_url,
            'nse_corporate_announcements',
            NEW.id::TEXT,
            NEW.pdf_hash
        );
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create triggers
DROP TRIGGER IF EXISTS bse_duplicate_detection ON bse_corporate_announcements;
CREATE TRIGGER bse_duplicate_detection
    BEFORE INSERT OR UPDATE ON bse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION bse_duplicate_detection_trigger();

DROP TRIGGER IF EXISTS nse_duplicate_detection ON nse_corporate_announcements;
CREATE TRIGGER nse_duplicate_detection
    BEFORE INSERT OR UPDATE ON nse_corporate_announcements
    FOR EACH ROW
    EXECUTE FUNCTION nse_duplicate_detection_trigger();

-- ============================================================================
-- 6. UPDATE EXISTING RECORDS
-- ============================================================================

-- Populate pdf_registry with existing BSE records
INSERT INTO pdf_registry (pdf_url, source_table, source_record_id, first_seen_at)
SELECT
    attachmentfile,
    'bse_corporate_announcements',
    id::TEXT,
    COALESCE(created_at, NOW())
FROM bse_corporate_announcements
WHERE attachmentfile IS NOT NULL
AND TRIM(attachmentfile) != ''
AND attachmentfile NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined')
ON CONFLICT (pdf_url) DO NOTHING;

-- Populate pdf_registry with existing NSE records
INSERT INTO pdf_registry (pdf_url, source_table, source_record_id, first_seen_at)
SELECT
    attachment_url,
    'nse_corporate_announcements',
    id::TEXT,
    COALESCE(created_at, NOW())
FROM nse_corporate_announcements
WHERE attachment_url IS NOT NULL
AND TRIM(attachment_url) != ''
AND attachment_url NOT IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '-', '', 'undefined')
ON CONFLICT (pdf_url) DO NOTHING;

-- Mark existing duplicates based on URL matching
WITH duplicate_urls AS (
    SELECT pdf_url, COUNT(*) as url_count
    FROM pdf_registry
    GROUP BY pdf_url
    HAVING COUNT(*) > 1
),
ranked_records AS (
    SELECT
        pr.pdf_url,
        pr.source_table,
        pr.source_record_id,
        ROW_NUMBER() OVER (PARTITION BY pr.pdf_url ORDER BY pr.first_seen_at ASC) as rn
    FROM pdf_registry pr
    INNER JOIN duplicate_urls du ON pr.pdf_url = du.pdf_url
)
-- Update BSE records
UPDATE bse_corporate_announcements
SET is_duplicate = TRUE
WHERE id::TEXT IN (
    SELECT source_record_id
    FROM ranked_records
    WHERE source_table = 'bse_corporate_announcements'
    AND rn > 1
);

-- Update NSE records
UPDATE nse_corporate_announcements
SET is_duplicate = TRUE
WHERE id::TEXT IN (
    SELECT source_record_id
    FROM ranked_records
    WHERE source_table = 'nse_corporate_announcements'
    AND rn > 1
);

-- ============================================================================
-- 7. CREATE UTILITY FUNCTIONS
-- ============================================================================

-- Function to get duplicate statistics
CREATE OR REPLACE FUNCTION get_duplicate_stats()
RETURNS TABLE(
    table_name TEXT,
    total_records BIGINT,
    records_with_pdfs BIGINT,
    duplicate_records BIGINT,
    unique_pdfs BIGINT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'bse_corporate_announcements'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(CASE WHEN attachmentfile IS NOT NULL AND TRIM(attachmentfile) != '' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN is_duplicate = TRUE THEN 1 END)::BIGINT,
        COUNT(DISTINCT attachmentfile)::BIGINT
    FROM bse_corporate_announcements
    
    UNION ALL
    
    SELECT 
        'nse_corporate_announcements'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(CASE WHEN attachment_url IS NOT NULL AND TRIM(attachment_url) != '' THEN 1 END)::BIGINT,
        COUNT(CASE WHEN is_duplicate = TRUE THEN 1 END)::BIGINT,
        COUNT(DISTINCT attachment_url)::BIGINT
    FROM nse_corporate_announcements;
END;
$$;

COMMIT;

-- ============================================================================
-- 8. VERIFICATION QUERIES
-- ============================================================================

-- Check the new columns exist
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name IN ('bse_corporate_announcements', 'nse_corporate_announcements')
AND column_name IN ('is_duplicate', 'pdf_hash')
ORDER BY table_name, column_name;

-- Check indexes were created
SELECT indexname, tablename, indexdef 
FROM pg_indexes 
WHERE tablename IN ('bse_corporate_announcements', 'nse_corporate_announcements', 'pdf_registry')
AND indexname LIKE '%duplicate%' OR indexname LIKE '%pdf%' OR indexname LIKE '%attachment%'
ORDER BY tablename, indexname;

-- Get initial statistics
SELECT * FROM get_duplicate_stats();
