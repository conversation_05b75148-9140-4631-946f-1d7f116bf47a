-- Rollback Migration: Remove PDF duplicate detection system
-- Project: corporate_announcements (yvuwseolouiqhoxsieop)
-- Date: 2025-07-16
-- Description: Removes duplicate detection columns, indexes, functions, and triggers

BEGIN;

-- ============================================================================
-- 1. DROP TRIGGERS
-- ============================================================================

DROP TRIGGER IF EXISTS bse_duplicate_detection ON bse_corporate_announcements;
DROP TRIGGER IF EXISTS nse_duplicate_detection ON nse_corporate_announcements;

-- ============================================================================
-- 2. DROP FUNCTIONS
-- ============================================================================

DROP FUNCTION IF EXISTS bse_duplicate_detection_trigger();
DROP FUNCTION IF EXISTS nse_duplicate_detection_trigger();
DROP FUNCTION IF EXISTS check_pdf_duplicate(TEXT, VARCHAR(50), TEXT);
DROP FUNCTION IF EXISTS register_pdf(TEXT, VARCHAR(50), TEXT, VARCHAR(64));
DROP FUNCTION IF EXISTS get_duplicate_stats();

-- ============================================================================
-- 3. DROP INDEXES
-- ============================================================================

DROP INDEX IF EXISTS idx_bse_attachmentfile;
DROP INDEX IF EXISTS idx_nse_attachment_url;
DROP INDEX IF EXISTS idx_bse_pdf_hash;
DROP INDEX IF EXISTS idx_nse_pdf_hash;
DROP INDEX IF EXISTS idx_bse_duplicate_detection;
DROP INDEX IF EXISTS idx_nse_duplicate_detection;
DROP INDEX IF EXISTS idx_pdf_registry_url_unique;
DROP INDEX IF EXISTS idx_pdf_registry_hash;
DROP INDEX IF EXISTS idx_pdf_registry_source;

-- ============================================================================
-- 4. DROP TABLES
-- ============================================================================

DROP TABLE IF EXISTS pdf_registry;

-- ============================================================================
-- 5. REMOVE COLUMNS
-- ============================================================================

-- Remove columns from bse_corporate_announcements
ALTER TABLE bse_corporate_announcements 
DROP COLUMN IF EXISTS is_duplicate;

ALTER TABLE bse_corporate_announcements 
DROP COLUMN IF EXISTS pdf_hash;

-- Remove columns from nse_corporate_announcements
ALTER TABLE nse_corporate_announcements 
DROP COLUMN IF EXISTS is_duplicate;

ALTER TABLE nse_corporate_announcements 
DROP COLUMN IF EXISTS pdf_hash;

COMMIT;

-- ============================================================================
-- 6. VERIFICATION QUERIES
-- ============================================================================

-- Verify columns were removed
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name IN ('bse_corporate_announcements', 'nse_corporate_announcements')
AND column_name IN ('is_duplicate', 'pdf_hash');

-- Verify table was dropped
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'pdf_registry';

-- Verify functions were dropped
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_name IN (
    'bse_duplicate_detection_trigger',
    'nse_duplicate_detection_trigger',
    'check_pdf_duplicate',
    'register_pdf',
    'get_duplicate_stats'
);

-- Verify triggers were dropped
SELECT trigger_name, event_object_table 
FROM information_schema.triggers 
WHERE trigger_name IN ('bse_duplicate_detection', 'nse_duplicate_detection');
