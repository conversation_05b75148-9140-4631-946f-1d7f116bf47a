# NSE Corporate Announcements Scraper

A dedicated Python scraper for fetching and processing NSE (National Stock Exchange) corporate announcements data.

## Features

- **Real-time Data Fetching**: Retrieves latest corporate announcements from NSE API
- **Duplicate Detection**: Advanced hash-based duplicate detection system
- **Database Integration**: Automatic saving to Supabase database
- **CSV Export**: Persistent CSV storage with timestamped backups
- **Proxy Support**: Built-in ScraperAPI proxy support for reliable access
- **Error Handling**: Comprehensive error handling and retry mechanisms

## Installation

1. Clone the repository and navigate to the NSE Corporate Announcements directory
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables in `.env` file:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SCRAPERAPI_KEY=your_scraperapi_key
   ```

## Usage

### Command Line Interface

```bash
# Fetch recent corporate announcements
python -m nse_corporate_announcements.main

# Fetch announcements for specific index
python -m nse_corporate_announcements.main --index equities

# Save to custom file
python -m nse_corporate_announcements.main --output my_announcements.csv

# Summary only (no file saving)
python -m nse_corporate_announcements.main --summary-only
```

### Python API

```python
from nse_corporate_announcements import NSECorporateAnnouncementsScraper

with NSECorporateAnnouncementsScraper() as scraper:
    # Fetch corporate announcements
    df = scraper.fetch_corporate_announcements(index="equities")
    
    if df is not None:
        print(f"Fetched {len(df)} corporate announcements")
        
        # Save to CSV
        scraper.save_to_csv(df)
        
        # Save to database
        scraper.save_to_database(df)
```

## Configuration

The scraper can be configured through the `config.py` file:

- **NSE_BASE_URL**: NSE website base URL
- **SAVE_CSV**: Enable/disable CSV saving
- **USE_DATABASE**: Enable/disable database integration
- **OUTPUT_FOLDER**: Directory for CSV files
- **PROXY_CONFIG**: ScraperAPI proxy configuration

## Data Fields

The scraper extracts the following fields from corporate announcements:

- `symbol`: Stock symbol
- `desc`: Announcement description/subject
- `sm_name`: Company name
- `an_dt`: Announcement date
- `exchdisstime`: Exchange dissemination time
- `attchmntfile`: Attachment file URL
- `attchmnttext`: Attachment text/details
- `seq_id`: Sequence ID
- `smindustry`: Industry classification
- `difference`: Time difference
- `filesize`: Attachment file size

## Database Schema

The scraper saves data to a Supabase table with the following structure:

- `symbol`: Company symbol
- `company_name`: Full company name
- `subject`: Announcement subject
- `details`: Detailed announcement text
- `broadcast_date_time`: Broadcast timestamp
- `receipt`: Receipt timestamp
- `dissemination`: Dissemination timestamp
- `difference_seconds`: Time difference in seconds
- `attachment_url`: URL to attachment file
- `file_size`: Size of attachment file
- `record_hash`: Unique hash for duplicate detection

## Error Handling

The scraper includes comprehensive error handling:

- **Connection Errors**: Automatic retry with proxy fallback
- **Rate Limiting**: Built-in delays and retry mechanisms
- **Data Validation**: Validates response format and content
- **Duplicate Detection**: Prevents duplicate records in database and CSV

## Dependencies

- `requests`: HTTP requests
- `pandas`: Data manipulation
- `python-dotenv`: Environment variable management
- `beautifulsoup4`: HTML parsing (if needed)
- `lxml`: XML parsing support

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please check the documentation or create an issue in the repository.
