"""
Command-line argument parser for NSE Corporate Announcements Scraper
Handles all argument parsing and validation logic using shared CLI framework.
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseArgumentParser, ValidationHelper


class NSECorporateAnnouncementsArgumentParser(BaseArgumentParser):
    """NSE Corporate Announcements specific argument parser using shared framework"""

    def __init__(self):
        super().__init__(
            exchange_name="NSE Corporate Announcements",
            description="NSE Corporate Announcements Data Scraper"
        )

    def _add_exchange_specific_arguments(self):
        """Add NSE Corporate Announcements specific arguments"""
        # Index type argument
        self.parser.add_argument(
            '--index',
            type=str,
            default='equities',
            help='Index type for corporate announcements (default: equities)'
        )

    def _get_date_format(self) -> str:
        """Get the date format string for NSE"""
        return "DD-MM-YYYY"

    def _get_examples(self) -> str:
        """Get example usage strings for NSE Corporate Announcements"""
        return """
Examples:
  python -m nse_corporate_announcements.main                    # Fetch recent corporate announcements
  python -m nse_corporate_announcements.main --index equities   # Fetch equities announcements
  python -m nse_corporate_announcements.main --output my_data.csv  # Custom output file
        """


def create_parser():
    """Create and configure the argument parser (legacy compatibility)"""
    nse_parser = NSECorporateAnnouncementsArgumentParser()
    return nse_parser.create_parser()


def validate_arguments(args):
    """Validate parsed arguments and exit if invalid (enhanced with shared validation)"""
    # Use shared validation helper
    if not ValidationHelper.validate_date_arguments(args):
        sys.exit(1)

    # Validate output filename
    if args.output:
        validated_filename = ValidationHelper.validate_output_filename(args.output)
        if not validated_filename:
            sys.exit(1)
        args.output = validated_filename


def parse_arguments():
    """Parse and validate command-line arguments"""
    parser = create_parser()
    args = parser.parse_args()
    validate_arguments(args)
    return args
