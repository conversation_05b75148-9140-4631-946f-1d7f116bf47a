"""
CSV Parser for NSE Corporate Announcements Scraper
Handles CSV file operations with duplicate detection and persistent storage.
"""

import pandas as pd
import os
from datetime import datetime
import sys

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .config import OUTPUT_FOLDER


class CSVParser:
    """Handles persistent CSV storage with duplicate detection"""

    # Default persistent CSV filename
    PERSISTENT_CSV_FILE = "nse_corporate_announcements_persistent.csv"

    @staticmethod
    def _load_existing_data(csv_path):
        """
        Load existing data from CSV file if it exists

        Args:
            csv_path (str): Path to CSV file

        Returns:
            pandas.DataFrame: Existing data or empty DataFrame
        """
        try:
            if os.path.exists(csv_path):
                existing_df = pd.read_csv(csv_path)
                print(f"📂 Loaded {len(existing_df)} existing records from {csv_path}")
                return existing_df
            else:
                print(f"📂 No existing file found at {csv_path}, will create new file")
                return pd.DataFrame()
        except Exception as e:
            print(f"⚠️ Error loading existing CSV data: {e}")
            return pd.DataFrame()

    @staticmethod
    def _merge_with_existing_data(new_df, existing_df):
        """
        Merge new data with existing data, removing duplicates

        Args:
            new_df (pandas.DataFrame): New data to add
            existing_df (pandas.DataFrame): Existing data

        Returns:
            tuple: (merged_df, new_records_count, duplicate_count)
        """
        try:
            if existing_df.empty:
                print("📊 No existing data, all records are new")
                return new_df, len(new_df), 0

            # Ensure both DataFrames have record_hash column
            if 'record_hash' not in new_df.columns:
                print("⚠️ New data missing record_hash column")
                return new_df, len(new_df), 0

            if 'record_hash' not in existing_df.columns:
                print("⚠️ Existing data missing record_hash column, treating all as new")
                return new_df, len(new_df), 0

            # Find duplicates based on record_hash
            existing_hashes = set(existing_df['record_hash'].dropna())
            new_df['is_duplicate'] = new_df['record_hash'].isin(existing_hashes)
            
            # Separate new and duplicate records
            duplicate_records = new_df[new_df['is_duplicate']]
            truly_new_records = new_df[~new_df['is_duplicate']]
            
            # Remove the temporary is_duplicate column
            truly_new_records = truly_new_records.drop('is_duplicate', axis=1)
            
            duplicate_count = len(duplicate_records)
            new_count = len(truly_new_records)
            
            print(f"🔍 Found {duplicate_count} duplicate records, {new_count} new records")
            
            if new_count > 0:
                # Combine existing and new data
                merged_df = pd.concat([existing_df, truly_new_records], ignore_index=True)
                return merged_df, new_count, duplicate_count
            else:
                return existing_df, 0, duplicate_count

        except Exception as e:
            print(f"⚠️ Error merging data: {e}")
            # Fallback: just return new data
            return new_df, len(new_df), 0

    @staticmethod
    def save_dataframe_to_csv(df, from_date, to_date, filename=None, subfolder=None):
        """
        Save DataFrame to CSV with persistent storage and duplicate detection

        Args:
            df (pandas.DataFrame): Data to save
            from_date (str): Start date (for timestamped files)
            to_date (str): End date (for timestamped files)
            filename (str, optional): Custom filename
            subfolder (str, optional): Subfolder name

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if df is None or df.empty:
                print("⚠️ No data to save")
                return False

            # Create output directory
            output_dir = OUTPUT_FOLDER
            if subfolder:
                output_dir = os.path.join(OUTPUT_FOLDER, subfolder)
            
            os.makedirs(output_dir, exist_ok=True)

            # Determine filename
            if filename is None:
                filename = CSVParser.PERSISTENT_CSV_FILE

            csv_path = os.path.join(output_dir, filename)

            # Load existing data
            existing_df = CSVParser._load_existing_data(csv_path)

            # Merge with existing data
            merged_df, new_count, duplicate_count = CSVParser._merge_with_existing_data(df, existing_df)

            # Save merged data
            merged_df.to_csv(csv_path, index=False, encoding='utf-8')

            print(f"💾 Saved to: {csv_path}")
            print(f"📊 Total records in file: {len(merged_df)}")
            print(f"📊 New records added: {new_count}")
            print(f"📊 Duplicates skipped: {duplicate_count}")

            # Also save a timestamped copy if we have new data
            if new_count > 0:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                timestamped_filename = f"nse_corporate_announcements_{timestamp}.csv"
                timestamped_path = os.path.join(output_dir, timestamped_filename)
                
                # Save only the new records to timestamped file
                new_records_df = df[~df['record_hash'].isin(existing_df.get('record_hash', pd.Series()))]
                new_records_df.to_csv(timestamped_path, index=False, encoding='utf-8')
                print(f"📅 Timestamped copy saved: {timestamped_path}")

            return True

        except Exception as e:
            print(f"❌ Error saving CSV: {e}")
            return False

    @staticmethod
    def get_file_size(filepath):
        """Get human-readable file size"""
        try:
            size_bytes = os.path.getsize(filepath)
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
        except:
            return "Unknown"
