"""
Data Fetcher for NSE Corporate Announcements Scraper
Handles API requests and data retrieval from NSE corporate announcements endpoint.
"""

import requests
import json
import pandas as pd
import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .config import NSE_BASE_URL, REQUEST_TIMEOUT
from .session_manager import SessionManager


class NSECorporateAnnouncementsDataFetcher:
    """Handles data fetching from NSE Corporate Announcements API"""

    def __init__(self, session_manager: SessionManager):
        """
        Initialize data fetcher with session manager

        Args:
            session_manager: SessionManager instance for handling requests
        """
        self.session_manager = session_manager
        self.session = session_manager.session
        self.base_url = NSE_BASE_URL

    def fetch_corporate_announcements(self, index="equities"):
        """
        Fetch corporate announcements data from NSE API

        Args:
            index (str): Index type (default: 'equities')

        Returns:
            pandas.DataFrame or None: Corporate announcements data or None if failed
        """
        try:
            # Corporate announcements API endpoint
            api_url = f"{self.base_url}/api/corporate-announcements"
            params = {'index': index}

            print(f"Fetching corporate announcements for index: {index}")
            print(f"API URL: {api_url}")

            # Use session manager for request with automatic proxy fallback
            response = self.session_manager.make_get_request(api_url, params=params, timeout=REQUEST_TIMEOUT)

            if not response:
                print("❌ API request failed - no response received")
                return None

            # Check response
            if not self._is_response_valid(response):
                return None

            # Parse JSON response
            df = self._parse_response(response.text)

            if df is not None:
                print(f"Successfully fetched {len(df)} corporate announcements")
                return df
            else:
                return None

        except requests.exceptions.Timeout:
            print("Request timed out. NSE server might be slow.")
            return None
        except requests.exceptions.ConnectionError:
            print("Connection error. Check your internet connection.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None

    def _is_response_valid(self, response):
        """
        Check if the response is valid

        Args:
            response: requests.Response object

        Returns:
            bool: True if response is valid, False otherwise
        """
        content_type = response.headers.get('content-type', 'Unknown')
        print(f"Response content type: {content_type}")

        if response.status_code != 200:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response content preview: {response.text[:500]}")
            return False

        # Expect JSON response for corporate announcements
        if 'application/json' not in content_type:
            print(f"Warning: Response is not JSON. Content-Type: {content_type}")
            print("This might be an error page or CAPTCHA challenge")
            return False

        return True

    def _parse_response(self, response_text):
        """
        Parse corporate announcements JSON response from NSE API

        Args:
            response_text (str): Raw JSON response text

        Returns:
            pandas.DataFrame or None: Parsed data or None if failed
        """
        try:
            print(f"Response length: {len(response_text)}")
            print(f"Response preview: {response_text[:200]}")

            # Parse JSON
            data = json.loads(response_text)

            # Check if data is valid
            if not isinstance(data, list) or len(data) == 0:
                print("No corporate announcements data found")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(data)
            print(f"Available columns: {list(df.columns)}")

            # Clean and format the data
            df = self._clean_data(df)

            return df

        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return None
        except Exception as e:
            print(f"Error parsing corporate announcements response: {e}")
            return None

    def _clean_data(self, df):
        """
        Clean and format corporate announcements data

        Args:
            df (pandas.DataFrame): Raw corporate announcements data

        Returns:
            pandas.DataFrame: Cleaned data with only specified columns
        """
        try:
            # Make a copy to avoid modifying original
            cleaned_df = df.copy()

            # Clean column names - remove extra spaces and standardize
            cleaned_df.columns = [col.strip().replace(' ', '_').lower() for col in cleaned_df.columns]

            # Define the columns we want to keep (in lowercase to match cleaned names)
            required_columns = [
                'symbol',
                'desc',
                'dt',
                'attchmntfile',
                'sm_name',
                'sm_isin',
                'an_dt',
                'seq_id',
                'smindustry',
                'attchmnttext',
                'exchdisstime',
                'difference',
                'filesize'
            ]

            # Filter to only keep required columns that exist in the data
            available_columns = [col for col in required_columns if col in cleaned_df.columns]
            missing_columns = [col for col in required_columns if col not in cleaned_df.columns]

            if missing_columns:
                print(f"Warning: Missing columns in data: {missing_columns}")

            # Select only the required columns
            cleaned_df = cleaned_df[available_columns]

            # Convert date columns to proper datetime format if they exist
            date_columns = [col for col in cleaned_df.columns if 'date' in col.lower() or col in ['dt', 'an_dt']]
            for date_col in date_columns:
                try:
                    cleaned_df[date_col] = pd.to_datetime(cleaned_df[date_col], errors='coerce')
                except:
                    pass

            # Remove rows with all NaN values
            cleaned_df = cleaned_df.dropna(how='all')

            # Add record hashes for duplicate detection
            from .hash_utils import add_record_hashes
            cleaned_df = add_record_hashes(cleaned_df)

            print(f"Cleaned data: {len(cleaned_df)} records with filtered columns: {list(cleaned_df.columns)}")
            print(f"Filtered out {len(df.columns) - len(cleaned_df.columns)} unnecessary columns")
            return cleaned_df

        except Exception as e:
            print(f"Error cleaning corporate announcements data: {e}")
            return df  # Return original if cleaning fails
