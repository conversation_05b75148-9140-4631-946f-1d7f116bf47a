"""
Hash utilities for NSE Corporate Announcements Scraper
Provides functions for generating unique hashes for duplicate detection.
"""

import hashlib
import pandas as pd
import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def generate_record_hash(row):
    """
    Generate a unique hash for a corporate announcement record to detect duplicates.

    Args:
        row (dict): Corporate announcement record data

    Returns:
        str: MD5 hash of the record
    """
    # Use key fields that uniquely identify a corporate announcement
    # Handle both CSV format and database format
    key_fields = [
        str(row.get('symbol', row.get('SYMBOL', ''))),
        str(row.get('desc', row.get('subject', ''))),  # subject/description
        str(row.get('sm_name', row.get('company_name', ''))),  # company name
        str(row.get('an_dt', row.get('receipt', ''))),  # announcement date
        str(row.get('seq_id', row.get('SEQ_ID', ''))),  # sequence ID for uniqueness
        str(row.get('attchmntfile', row.get('attachment_url', '')))  # attachment file
    ]

    # Create a string from key fields and hash it
    key_string = '|'.join(key_fields)
    return hashlib.md5(key_string.encode('utf-8')).hexdigest()


def add_record_hashes(dataframe):
    """
    Add record hash column to DataFrame for duplicate detection.

    Args:
        dataframe: pandas DataFrame with NSE corporate announcements data

    Returns:
        pandas DataFrame with added 'record_hash' column
    """
    try:
        if dataframe is None or dataframe.empty:
            return dataframe

        print(f"Generating hashes for {len(dataframe)} NSE corporate announcements records...")

        # Generate hash for each row
        dataframe['record_hash'] = dataframe.apply(
            lambda row: generate_record_hash(row), axis=1
        )

        # Check for any duplicate hashes within this dataset
        duplicate_hashes = dataframe['record_hash'].duplicated().sum()
        if duplicate_hashes > 0:
            print(f"⚠️ Found {duplicate_hashes} duplicate hashes within NSE corporate announcements dataset")

        print(f"✅ Generated {len(dataframe)} hashes for NSE corporate announcements records")
        return dataframe

    except Exception as e:
        print(f"❌ Error generating hashes: {e}")
        return dataframe
