"""
NSE Corporate Announcements Scraper
Main scraper class for fetching and processing NSE corporate announcements data.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .config import SAVE_CSV, USE_DATABASE, CSV_FILENAME
from .session_manager import SessionManager
from .data_fetcher import NSECorporateAnnouncementsDataFetcher
from .database_manager import NSECorporateAnnouncementsDatabaseManager
from .csv_parser import CSVParser


class NSECorporateAnnouncementsScraper:
    """Main scraper class for NSE Corporate Announcements"""

    def __init__(self):
        """Initialize the scraper with all necessary components"""
        # Initialize session manager
        self.session_manager = SessionManager()
        
        # Initialize data fetcher
        self.data_fetcher = NSECorporateAnnouncementsDataFetcher(self.session_manager)
        
        # Initialize database manager if enabled
        self.database_manager = None
        if USE_DATABASE:
            try:
                self.database_manager = NSECorporateAnnouncementsDatabaseManager()
                print("✅ Database integration enabled")
            except Exception as e:
                print(f"⚠️ Database integration failed: {e}")
                print("⚠️ Continuing with CSV-only mode")

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()

    def close(self):
        """Close the scraper and clean up resources"""
        if self.session_manager:
            self.session_manager.close_session()

    def fetch_corporate_announcements(self, index="equities"):
        """
        Fetch corporate announcements data

        Args:
            index (str): Index type (default: 'equities')

        Returns:
            pandas.DataFrame or None: Corporate announcements data or None if failed
        """
        print(f"Fetching corporate announcements for index: {index}...")
        return self.data_fetcher.fetch_corporate_announcements(index=index)

    def save_to_csv(self, df, filename=None):
        """
        Save corporate announcements DataFrame to CSV file

        Args:
            df (pandas.DataFrame): Corporate announcements data to save
            filename (str, optional): Custom filename. If None, uses default naming

        Returns:
            bool: True if successful, False otherwise
        """
        if not SAVE_CSV:
            print("CSV saving is disabled in configuration")
            return True

        # Add record_hash column if not present (required for duplicate detection)
        if 'record_hash' not in df.columns:
            from .hash_utils import generate_record_hash
            df = df.copy()  # Don't modify original
            df['record_hash'] = df.apply(generate_record_hash, axis=1)

        if filename is None:
            filename = CSV_FILENAME

        return CSVParser.save_dataframe_to_csv(df, "N/A", "N/A", filename, subfolder="corporate_announcements")

    def save_to_database(self, df):
        """
        Save corporate announcements DataFrame to Supabase database with duplicate detection

        Args:
            df (pandas.DataFrame): Corporate announcements data to save

        Returns:
            dict: Summary of database insertion results
        """
        if not USE_DATABASE:
            print("Database saving is disabled in configuration")
            return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}

        if self.database_manager is None:
            print("Database manager not available")
            return {'success': False, 'inserted': 0, 'duplicates': 0, 'errors': 1}

        try:
            # Convert DataFrame to list of dictionaries
            records = df.to_dict('records')

            # Insert records
            result = self.database_manager.insert_records(records)

            return result

        except Exception as e:
            print(f"Error saving corporate announcements to database: {e}")
            return {'success': False, 'inserted': 0, 'duplicates': 0, 'errors': len(df)}

    def test_connection(self):
        """Test connections to NSE and database"""
        print("🔌 Testing connections...")
        
        # Test NSE connection
        nse_ok = self.session_manager.test_connection()
        
        # Test database connection
        db_ok = True
        if USE_DATABASE and self.database_manager:
            db_ok = self.database_manager.test_connection()
        
        return nse_ok and db_ok
