# NSE Scraper

A modular Python package for scraping insider trading data from NSE (National Stock Exchange of India).

## Features

- **Modular Design**: Clean separation of concerns with dedicated modules for different functionalities
- **Insider Trading Data**: Specialized scraper for NSE insider trading information
- **CSV Data Parsing**: Handles malformed CSV responses from NSE API
- **Session Management**: Proper HTTP session handling with cookies and headers
- **Date Range Support**: Fetch data for custom date ranges or recent days (insider trading)
- **Organized Output**: All CSV files are automatically saved in a dedicated `nse_data` folder
- **Error Handling**: Comprehensive error handling and debugging support
- **Context Manager**: Supports Python context manager for resource cleanup

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

## Package Structure

```
nse_scraper/
├── __init__.py          # Package initialization
├── config.py            # Configuration and constants
├── session_manager.py   # HTTP session management
├── data_fetcher.py      # API requests and data retrieval
├── csv_parser.py        # CSV parsing and processing
├── scraper.py           # Main scraper class
├── main.py              # CLI interface
├── requirements.txt     # Dependencies
└── README.md           # This file
```

## Usage

### Basic Usage

```python
from nse_scraper import NSEInsiderTradingScraper

# Create scraper instance
scraper = NSEInsiderTradingScraper()

# Fetch data for the last 7 days
result = scraper.fetch_data(days_back=7)

if result:
    df, from_date, to_date = result
    print(f"Fetched {len(df)} records from {from_date} to {to_date}")
    
    # Save to CSV
    scraper.save_data_to_csv(df, from_date, to_date)

# Clean up
scraper.close()
```

### Using Context Manager (Recommended)

```python
from nse_scraper import NSEInsiderTradingScraper

with NSEInsiderTradingScraper() as scraper:
    result = scraper.fetch_data(days_back=30)
    
    if result:
        df, from_date, to_date = result
        scraper.save_data_to_csv(df, from_date, to_date)
        
        # Get data summary
        summary = scraper.get_data_summary(df)
        print(f"Total records: {summary['total_records']}")
        print(f"Companies: {len(summary['companies'])}")
```

### Custom Date Range

```python
with NSEInsiderTradingScraper() as scraper:
    result = scraper.fetch_data_for_date_range("01-01-2025", "07-01-2025")

    if result:
        df, from_date, to_date = result
        scraper.save_data_to_csv(df, from_date, to_date)
```

### Note on Corporate Announcements

Corporate announcements functionality has been moved to a separate dedicated scraper.
Please use the `nse_corporate_announcements` package for fetching corporate announcements data.

### Command Line Interface

```bash
# Insider Trading Data (default)
# Fetch last 7 days
python -m nse_scraper.main

# Fetch last 30 days
python -m nse_scraper.main --days 30

# Custom date range
python -m nse_scraper.main --from 01-01-2025 --to 07-01-2025

# Custom output file
python -m nse_scraper.main --output my_data.csv

# Show summary only (don't save CSV)
python -m nse_scraper.main --summary-only

# Note: Corporate announcements functionality has been moved to nse_corporate_announcements package
```

## Components

### NSEInsiderTradingScraper
Main scraper class that orchestrates all components.

### SessionManager
Handles HTTP session setup, headers, and cookie management.

### DataFetcher
Manages API requests and data retrieval from NSE endpoints.

### CSVParser
Parses malformed CSV responses from NSE API and converts them to pandas DataFrames.

### Configuration
All settings, headers, cookies, and constants are centralized in `config.py`.

## Output Organization

All CSV files are automatically saved in the `nse_data` folder:
- **Auto-generated filenames**: `nse_insider_trading_DD_MM_YYYY_to_DD_MM_YYYY.csv`
- **Custom filenames**: Saved with your specified name in the same folder
- **Folder creation**: The `nse_data` folder is created automatically if it doesn't exist

## Data Format

The scraper fetches insider trading data with the following typical columns:
- SYMBOL
- COMPANY
- REGULATION
- NAME OF THE ACQUIRER/DISPOSER
- CATEGORY OF PERSON
- TYPE OF SECURITY (PRIOR)
- NO. OF SECURITY (PRIOR)
- % SHAREHOLDING (PRIOR)
- And many more...

## Error Handling

The scraper includes comprehensive error handling:
- Network timeouts and connection errors
- Invalid CSV format handling
- HTML error page detection
- Graceful error recovery and user-friendly messages

## Requirements

- Python 3.6+
- pandas >= 1.3.0
- requests >= 2.25.0

## License

This project is for educational and research purposes only. Please respect NSE's terms of service and rate limits.
