"""
NSE Scraper Package

A modular Python package for scraping data from NSE (National Stock Exchange of India).
Supports both insider trading data and corporate announcements.

Main Components:
- NSEInsiderTradingScraper: Main scraper class
- SessionManager: Handles HTTP sessions and cookies
- DataFetcher: Manages API requests and data retrieval
- CSVParser: Parses malformed CSV responses from NSE
- config: Configuration settings and constants

Usage:
    from nse_scraper import NSEInsiderTradingScraper

    # Insider trading data
    with NSEInsiderTradingScraper() as scraper:
        result = scraper.fetch_data(days_back=7)
        if result:
            df, from_date, to_date = result
            scraper.save_data_to_csv(df, from_date, to_date)

    # Corporate announcements
    with NSEInsiderTradingScraper() as scraper:
        df = scraper.fetch_corporate_announcements()
        if df is not None:
            scraper.save_corporate_announcements_to_csv(df)
"""

from .scraper import NSEInsiderTradingScraper
from .session_manager import SessionManager
from .data_fetcher import DataFetcher
from .csv_parser import CSVParser
from . import config

__version__ = "1.0.0"
__author__ = "NSE Scraper Team"
__email__ = "<EMAIL>"

__all__ = [
    "NSEInsiderTradingScraper",
    "SessionManager", 
    "DataFetcher",
    "CSVParser",
    "config"
]
