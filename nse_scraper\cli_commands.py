"""
Command execution logic for NSE Scraper CLI
Handles the actual data fetching and processing operations using shared CLI framework.
"""

import sys
import os
from typing import Optional, Dict, Any

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseCommandExecutor, DataDisplayHelper
from shared.data_processor import FileManager
from .scraper import NSEInsiderTradingScraper


class NSECommandExecutor(BaseCommandExecutor):
    """NSE-specific command executor using shared framework"""

    def __init__(self):
        super().__init__("NSE")
        self.file_manager = FileManager("nse_data", "NSE")

    def _create_scraper(self):
        """Create and return NSE scraper instance"""
        return NSEInsiderTradingScraper()

    def _handle_list_files_command(self, args):
        """Handle the --list-files command"""
        files = self.file_manager.list_saved_files()
        DataDisplayHelper.display_file_list(files, "NSE")


    def _perform_scraping(self, scraper, args) -> Optional[Dict[str, Any]]:
        """Perform the actual scraping based on arguments"""
        # Fetch insider trading data
        if args.from_date and args.to_date:
            print(f"\n🔄 Fetching data for custom date range: {args.from_date} to {args.to_date}")
            result = scraper.fetch_data_for_date_range(args.from_date, args.to_date)
        else:
            print(f"\n🔄 Fetching data for the last {args.days} days...")
            result = scraper.fetch_data(days_back=args.days)

        if result:
            df, from_date, to_date = result
            return {
                'dataframe': df,
                'data_type': 'insider_trading',
                'start_date': from_date,
                'end_date': to_date
            }
        return None

    def _display_results(self, scraper, result: Dict[str, Any], args) -> bool:
        """Display scraping results and handle data saving"""
        dataframe = result['dataframe']
        data_type = result['data_type']
        start_date = result['start_date']
        end_date = result['end_date']

        print(f"\n✅ Data fetched successfully!")
        print(f"Date range: {start_date} to {end_date}")
        print(f"Total records: {len(dataframe)}")

        if len(dataframe) > 0:
            # Display summary using shared helper
            DataDisplayHelper.display_summary(dataframe, "NSE", data_type.replace('_', ' '))

            # Save data unless summary-only is specified
            if not args.summary_only:
                self._save_data(scraper, dataframe, data_type, start_date, end_date, args.output)
            else:
                print("\n📋 Summary-only mode: Data not saved")
        else:
            print("📊 No data found for the specified criteria.")

        return True


    def _save_data(self, scraper, dataframe, data_type: str, start_date: str, end_date: str, custom_filename: str = None):
        """Save data to CSV and database"""
        # Save to CSV
        csv_success = scraper.save_data_to_csv(dataframe, start_date, end_date, custom_filename)

        if not csv_success:
            print("⚠️ Warning: Failed to save data to CSV file")

        # Save to database
        db_result = scraper.save_data_to_database(dataframe)

        if not db_result['success']:
            print("⚠️ Warning: Database save had errors")


# Legacy functions for backward compatibility
def print_header():
    """Print application header (legacy compatibility)"""
    print(f"🏢 NSE Insider Trading Scraper")
    print("=" * 50)


def fetch_data(scraper, args):
    """Fetch data based on arguments (legacy compatibility)"""
    executor = NSECommandExecutor()
    result = executor._perform_scraping(scraper, args)
    if result:
        return result['dataframe'], result['start_date'], result['end_date']
    return None


def display_summary(scraper, df):
    """Display data summary (legacy compatibility)"""
    DataDisplayHelper.display_summary(df, "NSE", "insider trading")


def save_data(scraper, df, args, from_date, to_date):
    """Save data to CSV and database (legacy compatibility)"""
    if args.summary_only:
        print("\n📋 Skipping data save (--summary-only specified)")
        return

    executor = NSECommandExecutor()
    executor._save_data(scraper, df, 'insider_trading', from_date, to_date, args.output)


def execute_command(args):
    """Execute the main command based on parsed arguments"""
    # Use the new command executor
    executor = NSECommandExecutor()
    executor.execute_command(args)
