"""
Command-line argument parser for NSE Scraper
Handles all argument parsing and validation logic using shared CLI framework.
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.cli_framework import BaseArgumentParser, ValidationHelper


class NSEArgumentParser(BaseArgumentParser):
    """NSE-specific argument parser using shared framework"""

    def __init__(self):
        super().__init__(
            exchange_name="NSE",
            description="NSE Insider Trading Data Scraper"
        )

    def _add_exchange_specific_arguments(self):
        """Add NSE-specific arguments"""
        # No additional NSE-specific arguments currently
        pass

    def _get_date_format(self) -> str:
        """Get the date format string for NSE"""
        return "DD-MM-YYYY"

    def _get_examples(self) -> str:
        """Get example usage strings for NSE"""
        return """
Examples:
  python -m nse_scraper.main                    # Fetch last 7 days insider trading
  python -m nse_scraper.main --days 30          # Fetch last 30 days insider trading
  python -m nse_scraper.main --from 01-01-2025 --to 07-01-2025  # Custom range
  python -m nse_scraper.main --output my_data.csv  # Custom output file
        """


def create_parser():
    """Create and configure the argument parser (legacy compatibility)"""
    nse_parser = NSEArgumentParser()
    return nse_parser.create_parser()


def validate_arguments(args):
    """Validate parsed arguments and exit if invalid (enhanced with shared validation)"""
    # Use shared validation helper
    if not ValidationHelper.validate_date_arguments(args):
        sys.exit(1)

    # NSE-specific date format validation
    if args.from_date:
        try:
            datetime.strptime(args.from_date, '%d-%m-%Y')
            datetime.strptime(args.to_date, '%d-%m-%Y')
        except ValueError:
            print("❌ Error: Dates must be in DD-MM-YYYY format")
            sys.exit(1)

    # Validate output filename
    if args.output:
        validated_filename = ValidationHelper.validate_output_filename(args.output)
        if not validated_filename:
            sys.exit(1)
        args.output = validated_filename


def parse_arguments():
    """Parse and validate command-line arguments"""
    parser = create_parser()
    args = parser.parse_args()
    validate_arguments(args)
    return args
