"""
Configuration file for NSE Insider Trading Scraper
Contains essential constants and configuration settings.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import NSEConfig

# Create the configuration instance
config = NSEConfig()

# Export commonly used settings for backward compatibility
BASE_URL = config.BASE_URL
DEFAULT_DAYS_BACK = config.DEFAULT_DAYS_BACK
REQUEST_TIMEOUT = config.REQUEST_TIMEOUT
OUTPUT_FOLDER = config.OUTPUT_FOLDER
SUPABASE_URL = config.SUPABASE_URL
SUPABASE_ANON_KEY = config.SUPABASE_ANON_KEY
SAVE_CSV = config.SAVE_CSV
USE_DATABASE = config.USE_DATABASE

# NSE-specific settings
API_ENDPOINT = config.API_ENDPOINT
DEFAULT_INDEX = config.DEFAULT_INDEX
USE_CSV_FORMAT = config.USE_CSV_FORMAT

# Database settings
USE_DATABASE = True  # Set to True to enable Supabase storage
SAVE_CSV = True      # Set to True to also save CSV files
