"""
Cookie Manager for NSE Insider Trading Scraper
Handles dynamic cookie acquisition by visiting NSE pages.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_cookie_manager import NSECookieManager
from .config import config


class CookieManager(NSECookieManager):
    """
    NSE-specific cookie manager that inherits from the shared base class.

    This class maintains backward compatibility while using the shared
    cookie management functionality.
    """

    def __init__(self, session):
        """
        Initialize NSE cookie manager.

        Args:
            session: HTTP session object
        """
        super().__init__(session, config)
