"""
CSV Parser for NSE Insider Trading Data
Handles persistent CSV storage with duplicate detection based on unique identifiers.
"""

import pandas as pd
import os
from .config import OUTPUT_FOLDER
from .hash_utils import generate_record_hash


class CSVParser:
    """Handles persistent CSV storage with duplicate detection"""

    # Default persistent CSV filenames
    PERSISTENT_CSV_FILE = "nse_insider_trading_persistent.csv"



    @staticmethod
    def _load_existing_data(csv_path):
        """Load existing data from persistent CSV file."""
        if os.path.exists(csv_path):
            try:
                existing_df = pd.read_csv(csv_path)
                print(f"Loaded {len(existing_df)} existing records from {csv_path}")
                return existing_df
            except Exception as e:
                print(f"Error loading existing CSV: {e}")
                return pd.DataFrame()
        else:
            print(f"No existing CSV file found at {csv_path}")
            return pd.DataFrame()

    @staticmethod
    def _detect_duplicates(new_df, existing_df):
        """Detect and remove duplicates between new and existing data."""
        if existing_df.empty:
            # Add hash column to new data
            new_df['record_hash'] = new_df.apply(generate_record_hash, axis=1)
            return new_df, 0

        # Generate hashes for existing data if not present
        if 'record_hash' not in existing_df.columns:
            existing_df['record_hash'] = existing_df.apply(generate_record_hash, axis=1)

        # Generate hashes for new data
        new_df['record_hash'] = new_df.apply(generate_record_hash, axis=1)

        # Get existing hashes
        existing_hashes = set(existing_df['record_hash'].tolist())

        # Filter out duplicates
        unique_new_df = new_df[~new_df['record_hash'].isin(existing_hashes)]
        duplicate_count = len(new_df) - len(unique_new_df)

        return unique_new_df, duplicate_count
    
    @staticmethod
    def save_dataframe_to_csv(df, from_date, to_date, filename=None, subfolder="insider_trading"):
        """
        Save DataFrame to persistent CSV file with duplicate detection

        Args:
            df (pandas.DataFrame): New data to save
            from_date (str): Start date for logging
            to_date (str): End date for logging
            filename (str, optional): Custom filename. If None, uses persistent file
            subfolder (str): Subfolder within OUTPUT_FOLDER (default: "insider_trading")

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create main output folder if it doesn't exist
            if not os.path.exists(OUTPUT_FOLDER):
                os.makedirs(OUTPUT_FOLDER)
                print(f"Created main output folder: {OUTPUT_FOLDER}")

            # Create subfolder path
            subfolder_path = os.path.join(OUTPUT_FOLDER, subfolder)
            if not os.path.exists(subfolder_path):
                os.makedirs(subfolder_path)
                print(f"Created subfolder: {subfolder_path}")

            # Use persistent filename if not specified
            if filename is None:
                filename = CSVParser.PERSISTENT_CSV_FILE

            # Create full path with subfolder
            full_path = os.path.join(subfolder_path, filename)

            # Load existing data
            existing_df = CSVParser._load_existing_data(full_path)

            # Detect and remove duplicates
            unique_new_df, duplicate_count = CSVParser._detect_duplicates(df, existing_df)

            if duplicate_count > 0:
                print(f"🔍 Found and skipped {duplicate_count} duplicate records")

            if unique_new_df.empty:
                print("ℹ️ No new records to add (all were duplicates)")
                print(f"📊 Total records in persistent file: {len(existing_df)}")
                return True

            # Combine with existing data (no extra timestamp columns)
            if not existing_df.empty:
                # Ensure columns match
                for col in unique_new_df.columns:
                    if col not in existing_df.columns:
                        existing_df[col] = None
                for col in existing_df.columns:
                    if col not in unique_new_df.columns:
                        unique_new_df[col] = None

                combined_df = pd.concat([existing_df, unique_new_df], ignore_index=True)
            else:
                combined_df = unique_new_df

            # Save the combined DataFrame
            combined_df.to_csv(full_path, index=False)

            print(f"💾 Data saved to: {full_path}")
            print(f"📊 Added {len(unique_new_df)} new records")
            print(f"📊 Total records in file: {len(combined_df)}")
            print(f"📊 Duplicates skipped: {duplicate_count}")

            return True

        except Exception as e:
            print(f"Error saving to CSV: {e}")
            return False
