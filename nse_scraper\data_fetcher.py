"""
Data Fetcher for NSE Insider Trading Scraper
Handles API requests and data retrieval from NSE.
"""

import requests
import pandas as pd
import json
from datetime import datetime, timedelta
from urllib.parse import urlencode
from .config import BASE_URL, API_ENDPOINT, DEFAULT_INDEX, REQUEST_TIMEOUT, USE_CSV_FORMAT


class DataFetcher:
    """Handles data fetching from NSE API"""

    def __init__(self, session_manager):
        self.session_manager = session_manager
        self.session = session_manager.session if hasattr(session_manager, 'session') else session_manager
        self.base_url = BASE_URL
        self.api_endpoint = API_ENDPOINT
    
    def fetch_insider_data(self, days_back=7, index=DEFAULT_INDEX):
        """
        Fetch insider trading data from NSE JSON API

        Args:
            days_back (int): Number of days to look back from today
            index (str): Index type (default: 'equities')

        Returns:
            tuple: (DataFrame, from_date_str, to_date_str) or None if failed
        """
        try:
            # Calculate dates (fix date calculation - go back in time, not forward)
            todate = datetime.now()
            fromdate = todate - timedelta(days=days_back)

            # Format dates as required by NSE API
            to_date_str = todate.strftime('%d-%m-%Y')
            from_date_str = fromdate.strftime('%d-%m-%Y')

            print(f"Fetching data from {from_date_str} to {to_date_str}")

            # Construct API URL with CSV parameter
            api_url = f"{self.base_url}{self.api_endpoint}"
            params = {
                'index': index,
                'from_date': from_date_str,
                'to_date': to_date_str
            }

            # Add CSV parameter if configured
            if USE_CSV_FORMAT:
                params['csv'] = 'true'

            full_url = f"{api_url}?{urlencode(params)}"
            print(f"API URL: {full_url}")

            # Try direct request first (like the working script)
            print("Trying direct API request first...")
            try:
                response = self.session.get(api_url, params=params, timeout=REQUEST_TIMEOUT)
                print(f"Direct API response status: {response.status_code}")

                if response.status_code == 200:
                    print("✅ Direct API request successful")
                else:
                    print("Direct API request failed, trying with proxy...")
                    raise Exception("Direct request failed")

            except Exception:
                print("Using session manager with proxy for API request...")
                if hasattr(self.session_manager, 'make_get_request'):
                    response = self.session_manager.make_get_request(api_url, params=params, timeout=REQUEST_TIMEOUT)
                else:
                    # Fallback to direct session if session manager doesn't have the method
                    response = self.session.get(api_url, params=params, timeout=REQUEST_TIMEOUT)

                if response:
                    print(f"Proxy API response status: {response.status_code}")
                else:
                    print("API request failed - no response received")
                    return None

            # Check response
            if not self._is_response_valid(response):
                return None

            # Parse response based on format
            if USE_CSV_FORMAT:
                df = self._parse_csv_response(response.text)
            else:
                df = self._parse_json_response(response.text)

            if df is not None:
                print(f"Successfully fetched {len(df)} records")
                return df, from_date_str, to_date_str
            else:
                return None

        except requests.exceptions.Timeout:
            print("Request timed out. NSE server might be slow.")
            return None
        except requests.exceptions.ConnectionError:
            print("Connection error. Check your internet connection.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None
    
    def _is_response_valid(self, response):
        """
        Check if the response is valid

        Args:
            response: requests.Response object

        Returns:
            bool: True if response is valid, False otherwise
        """
        content_type = response.headers.get('content-type', 'Unknown')
        print(f"Response content type: {content_type}")

        if response.status_code != 200:
            print(f"HTTP Error: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")
            print(f"Response content preview: {response.text[:500]}")
            return False

        # Check content type based on expected format
        if USE_CSV_FORMAT:
            # For CSV requests, expect text/csv or text/plain
            if 'text/csv' not in content_type and 'text/plain' not in content_type and 'application/octet-stream' not in content_type:
                print(f"Warning: Expected CSV but got: {content_type}")
                # Don't fail immediately - some servers return different content types for CSV
        else:
            # For JSON requests, expect application/json
            if 'application/json' not in content_type:
                print(f"Warning: Response is not JSON. Content-Type: {content_type}")
                print("This might be an error page or CAPTCHA challenge")
                return False

        return True

    def _parse_csv_response(self, response_text):
        """
        Parse CSV response from NSE API

        Args:
            response_text (str): Raw CSV response text

        Returns:
            pandas.DataFrame or None: Parsed data or None if failed
        """
        try:
            print(f"Response length: {len(response_text)}")
            print(f"Response preview: {response_text[:200]}")

            # Check if response looks like CSV
            if not response_text.strip():
                print("Empty response received")
                return None

            # Check if it's an error page (HTML)
            if response_text.strip().startswith('<'):
                print("Received HTML response instead of CSV - might be blocked or error page")
                return None

            # Remove BOM if present
            if response_text.startswith('\ufeff'):
                response_text = response_text[1:]
            elif response_text.startswith('ï»¿'):
                response_text = response_text[3:]

            # Try to parse as CSV directly first
            try:
                import io
                csv_data = io.StringIO(response_text)
                df = pd.read_csv(csv_data)

                # Clean column names by removing newlines and extra spaces
                df.columns = [col.strip().replace('\n', '').replace('\r', '') for col in df.columns]

                print(f"Successfully parsed CSV with {len(df)} records")
                if len(df) > 0:
                    print(f"Available columns: {list(df.columns)}")
                    return df
                else:
                    print("No data found in CSV")
                    return None
            except Exception as e:
                print(f"Direct CSV parsing failed: {e}")
                # Fall back to manual parsing for malformed CSV
                return self._parse_malformed_csv(response_text)

        except Exception as e:
            print(f"CSV parsing error: {e}")
            return None

    def _parse_malformed_csv(self, response_text):
        """
        Parse malformed CSV response that might have header issues

        Args:
            response_text (str): Raw CSV response text

        Returns:
            pandas.DataFrame or None: Parsed data or None if failed
        """
        try:
            import io

            lines = response_text.split('\n')

            # Find where the actual data starts
            data_start_line = 0
            for i, line in enumerate(lines):
                if line.strip() and line.count(',') > 5:
                    data_start_line = i
                    break

            # Use data lines
            data_lines = lines[data_start_line:]
            data_content = '\n'.join(data_lines)

            # Parse the CSV
            csv_data = io.StringIO(data_content)
            df = pd.read_csv(csv_data)

            if len(df) == 0:
                print("No insider trading data found for the specified date range")
                return None

            return df

        except Exception as e:
            print(f"Malformed CSV parsing error: {e}")
            return None

    def _parse_json_response(self, response_text):
        """
        Parse JSON response from NSE API

        Args:
            response_text (str): Raw response text

        Returns:
            pandas.DataFrame or None: Parsed data or None if failed
        """
        try:
            data = json.loads(response_text)

            if not isinstance(data, dict) or 'data' not in data or not data['data']:
                print("No insider trading data found for the specified date range")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(data['data'])
            print(f"Available columns: {list(df.columns)}")

            return df

        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            return None
        except Exception as e:
            print(f"Error parsing JSON response: {e}")
            return None
    
    def fetch_custom_date_range(self, from_date, to_date, index=DEFAULT_INDEX):
        """
        Fetch insider trading data for a custom date range

        Args:
            from_date (str): Start date in 'DD-MM-YYYY' format
            to_date (str): End date in 'DD-MM-YYYY' format
            index (str): Index type (default: 'equities')

        Returns:
            tuple: (DataFrame, from_date_str, to_date_str) or None if failed
        """
        try:
            print(f"Fetching data from {from_date} to {to_date}")

            # Construct API URL with CSV parameter
            api_url = f"{self.base_url}{self.api_endpoint}"
            params = {
                'index': index,
                'from_date': from_date,
                'to_date': to_date
            }

            # Add CSV parameter if configured
            if USE_CSV_FORMAT:
                params['csv'] = 'true'

            full_url = f"{api_url}?{urlencode(params)}"
            print(f"API URL: {full_url}")

            # Try direct request first (like the working script)
            print("Trying direct API request first...")
            try:
                response = self.session.get(api_url, params=params, timeout=REQUEST_TIMEOUT)
                print(f"Direct API response status: {response.status_code}")

                if response.status_code == 200:
                    print("✅ Direct API request successful")
                else:
                    print("Direct API request failed, trying with proxy...")
                    raise Exception("Direct request failed")

            except Exception:
                print("Using session manager with proxy for API request...")
                if hasattr(self.session_manager, 'make_get_request'):
                    response = self.session_manager.make_get_request(api_url, params=params, timeout=REQUEST_TIMEOUT)
                else:
                    # Fallback to direct session if session manager doesn't have the method
                    response = self.session.get(api_url, params=params, timeout=REQUEST_TIMEOUT)

                if response:
                    print(f"Proxy API response status: {response.status_code}")
                else:
                    print("API request failed - no response received")
                    return None

            # Check response
            if not self._is_response_valid(response):
                return None

            # Parse response based on format
            if USE_CSV_FORMAT:
                df = self._parse_csv_response(response.text)
            else:
                df = self._parse_json_response(response.text)

            if df is not None:
                print(f"Successfully fetched {len(df)} records")
                return df, from_date, to_date
            else:
                return None

        except requests.exceptions.Timeout:
            print("Request timed out. NSE server might be slow.")
            return None
        except requests.exceptions.ConnectionError:
            print("Connection error. Check your internet connection.")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None








