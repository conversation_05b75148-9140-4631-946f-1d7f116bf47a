"""
Database Manager for NSE Scraper
Handles Supabase database operations for both insider trading and corporate announcements data.
"""

import requests
import json
import csv
import os
from datetime import datetime
from .config import SUPABASE_URL, SUPABASE_ANON_KEY
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.utils import calculate_unified_hash, fetch_cross_reference_code


class DatabaseManager:
    """Manages Supabase database operations for NSE insider trading data"""

    def __init__(self):
        """Initialize database manager for NSE insider trading data."""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = SUPABASE_ANON_KEY
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        self.data_type = 'insider_trading'
        self.table_name = 'nse_insider_trading'

    def normalize_null_values(self, value):
        """
        Normalize field values by converting string representations of null values to actual Python None.

        This function handles various null representations commonly found in NSE data and converts
        them to proper Python None values, which will become SQL NULL in the database.

        Args:
            value: The value to normalize (can be any type)

        Returns:
            str or None: Cleaned value or None if it represents a null value
        """
        # Handle actual None values
        if value is None:
            return None

        # Handle non-string types that might represent null
        if isinstance(value, float):
            # Check for NaN values
            if str(value).lower() in ['nan', 'inf', '-inf']:
                return None
            # Convert valid float to string for consistency
            return str(value)

        # Convert to string and strip whitespace for processing
        if not isinstance(value, str):
            str_value = str(value)
        else:
            str_value = value

        # Strip whitespace
        str_value = str_value.strip()

        # Check for empty string or whitespace-only strings
        if not str_value:
            return None

        # Comprehensive list of null representations (case-insensitive)
        null_representations = {
            # Standard null representations
            'null', 'nil', 'none', 'na', 'n/a',
            # Common variations
            '#n/a', '#na', '#null', '#nil',
            # Database null representations
            'nan', 'inf', '-inf',
            # Empty/undefined representations
            'undefined', 'empty', 'void',
            # Single character representations
            '-', '_', '.',
            # Text representations
            'not available', 'not applicable', 'no data',
            'missing', 'unknown', 'tbd', 'to be determined'
        }

        # Check if the lowercase string value is in our null representations
        if str_value.lower() in null_representations:
            return None

        # Check for patterns that indicate null values
        # Single repeated characters (like "---", "...", "___") but NOT zeros
        if len(str_value) <= 5 and len(set(str_value)) == 1 and str_value[0] in '-_.':
            return None

        # Return the cleaned string value (original with stripped whitespace)
        return str_value

    def _clean_value(self, value):
        """
        Legacy method for backward compatibility.
        Calls the new normalize_null_values method.

        Args:
            value: The value to clean

        Returns:
            str or None: Cleaned value or None if it represents null
        """
        return self.normalize_null_values(value)
    
    def _convert_csv_to_db_columns(self, csv_row):
        """
        Convert CSV column names to database column names for insider trading data

        Args:
            csv_row (dict): Row with CSV column names

        Returns:
            dict: Row with database column names
        """
        return self._convert_insider_trading_csv_to_db(csv_row)

    def _convert_insider_trading_csv_to_db(self, csv_row):
        """Convert insider trading CSV to database format"""
        column_mapping = {
            'SYMBOL': 'symbol',
            'COMPANY': 'company',
            'REGULATION': 'regulation',
            'NAME OF THE ACQUIRER/DISPOSER': 'name_of_the_acquirer_disposer',
            'CATEGORY OF PERSON': 'category_of_person',
            'TYPE OF SECURITY (PRIOR)': 'type_of_security_prior',
            'NO. OF SECURITY (PRIOR)': 'no_of_security_prior',
            '% SHAREHOLDING (PRIOR)': 'shareholding_prior',
            'TYPE OF SECURITY (ACQUIRED/DISPLOSED)': 'type_of_security_acquired_displosed',
            'NO. OF SECURITIES (ACQUIRED/DISPLOSED)': 'no_of_securities_acquired_displosed',
            'VALUE OF SECURITY (ACQUIRED/DISPLOSED)': 'value_of_security_acquired_displosed',
            'ACQUISITION/DISPOSAL TRANSACTION TYPE': 'acquisition_disposal_transaction_type',
            'TYPE OF SECURITY (POST)': 'type_of_security_post',
            'NO. OF SECURITY (POST)': 'no_of_security_post',
            '% POST': 'post_percentage',
            'DATE OF ALLOTMENT/ACQUISITION FROM': 'date_of_allotment_acquisition_from',
            'DATE OF ALLOTMENT/ACQUISITION TO': 'date_of_allotment_acquisition_to',
            'DATE OF INITMATION TO COMPANY': 'date_of_intimation_to_company',
            'MODE OF ACQUISITION': 'mode_of_acquisition',
            'DERIVATIVE TYPE SECURITY': 'derivative_type_security',
            'DERIVATIVE CONTRACT SPECIFICATION': 'derivative_contract_specification',
            'NOTIONAL VALUE(BUY)': 'notional_value_buy',
            'NUMBER OF UNITS/CONTRACT LOT SIZE (BUY)': 'number_of_units_contract_lot_size_buy',
            'NOTIONAL VALUE(SELL)': 'notional_value_sell',
            'NUMBER OF UNITS/CONTRACT LOT SIZE  (SELL)': 'number_of_units_contract_lot_size_sell',
            'EXCHANGE': 'exchange',
            'REMARK': 'remark',
            'BROADCASTE DATE AND TIME': 'broadcast_date_and_time',
            'XBRL': 'xbrl'
        }

        db_row = {}
        for csv_col, db_col in column_mapping.items():
            value = csv_row.get(csv_col, '')
            # Use the enhanced null normalization function
            normalized_value = self.normalize_null_values(value)
            db_row[db_col] = normalized_value

        return db_row



    def _save_duplicates_to_csv(self, duplicate_records, duplicate_type="insert"):
        """
        Save duplicate records to CSV file for analysis

        Args:
            duplicate_records (list): List of duplicate records
            duplicate_type (str): Type of duplicate detection ('pre-check' or 'insert')
        """
        try:
            if not duplicate_records:
                return

            # Create duplicates folder if it doesn't exist
            duplicates_folder = os.path.join(os.path.dirname(__file__), '..', 'duplicates')
            os.makedirs(duplicates_folder, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"nse_insider_trading_duplicates_{duplicate_type}_{timestamp}.csv"
            filepath = os.path.join(duplicates_folder, filename)

            # Write duplicates to CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                if duplicate_records:
                    # Get all unique fieldnames from all records to handle dynamic fields
                    all_fieldnames = set()
                    for record in duplicate_records:
                        all_fieldnames.update(record.keys())
                    fieldnames = sorted(list(all_fieldnames))

                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(duplicate_records)

            print(f"💾 Saved {len(duplicate_records)} duplicate records to: {filename}")

        except Exception as e:
            print(f"⚠️ Error saving duplicates to CSV: {e}")

    def check_existing_hashes(self, record_hashes):
        """
        Check which record hashes already exist in the database
        
        Args:
            record_hashes (list): List of record hashes to check
            
        Returns:
            set: Set of existing record hashes
        """
        try:
            if not record_hashes:
                return set()
            
            # Query existing hashes in batches
            existing_hashes = set()
            batch_size = 100
            
            for i in range(0, len(record_hashes), batch_size):
                batch = record_hashes[i:i + batch_size]
                hash_filter = ','.join([f'"{h}"' for h in batch])
                
                url = f"{self.supabase_url}/rest/v1/{self.table_name}"
                params = {
                    'select': 'record_hash',
                    'record_hash': f'in.({hash_filter})'
                }


                
                response = requests.get(url, headers=self.headers, params=params, timeout=30)

                if response.status_code == 200:
                    batch_results = response.json()
                    batch_hashes = {record['record_hash'] for record in batch_results}
                    existing_hashes.update(batch_hashes)


                else:
                    print(f"Warning: Failed to check existing hashes (status: {response.status_code})")
                    print(f"Response: {response.text}")
            
            return existing_hashes
            
        except Exception as e:
            print(f"Error checking existing hashes: {e}")
            return set()
    
    def insert_records(self, records):
        """
        Insert new records into the database with duplicate prevention
        
        Args:
            records (list): List of records to insert (with CSV column names)
            
        Returns:
            dict: Summary of insertion results
        """
        try:
            if not records:
                return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}
            
            print(f"Processing {len(records)} insider trading records for database insertion...")

            # Convert CSV format to database format and generate hashes
            db_records = []
            for record in records:
                db_record = self._convert_csv_to_db_columns(record)

                # For insider trading records, fetch cross-reference BSE code and calculate hash
                if self.data_type == 'insider_trading':
                    # Fetch BSE code using NSE symbol (with improved timeout and retry mechanism)
                    symbol = db_record.get('symbol', '')
                    if symbol:
                        bse_code = fetch_cross_reference_code('nse', symbol, self.supabase_url, self.supabase_key, max_retries=3, retry_delay=2)
                        if bse_code:
                            db_record['bse_code'] = bse_code

                    # Generate hash using unified function
                    db_record['record_hash'] = calculate_unified_hash(
                        'nse',
                        symbol=db_record.get('symbol', ''),
                        name_of_the_acquirer_disposer=db_record.get('name_of_the_acquirer_disposer', ''),
                        no_of_securities_acquired_displosed=db_record.get('no_of_securities_acquired_displosed', ''),
                        value_of_security_acquired_displosed=db_record.get('value_of_security_acquired_displosed', ''),
                        bse_code=db_record.get('bse_code', '')
                    )
                else:
                    # For corporate announcements, use existing hash generation
                    # Corporate announcements don't need cross-referencing
                    db_record['record_hash'] = self._generate_corporate_announcement_hash(record)

                if db_record.get('record_hash'):  # Only include records with valid hash
                    db_records.append(db_record)
            
            if not db_records:
                print("No valid records to insert")
                return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}
            
            # Check for existing hashes
            record_hashes = [record['record_hash'] for record in db_records]
            existing_hashes = self.check_existing_hashes(record_hashes)
            
            # Filter out duplicates and collect duplicate records
            new_records = []
            duplicate_records = []
            duplicate_count = 0

            for record in db_records:
                if record['record_hash'] in existing_hashes:
                    duplicate_count += 1
                    duplicate_records.append(record)
                else:
                    new_records.append(record)

            if duplicate_count > 0:
                print(f"🔍 Found {duplicate_count} duplicate insider trading records in database")
                # Save pre-check duplicates to CSV
                self._save_duplicates_to_csv(duplicate_records, "pre_check")

            if not new_records:
                print(f"ℹ️ No new insider trading records to insert (all were duplicates)")
                return {'success': True, 'inserted': 0, 'duplicates': duplicate_count, 'errors': 0}

            # Insert new records one by one to handle individual duplicates gracefully
            print(f"💾 Inserting {len(new_records)} new insider trading records to database...")

            total_inserted = 0
            total_errors = 0
            total_duplicates_at_insert = 0
            insert_duplicates = []  # Track records that are duplicates at insert time

            for i, record in enumerate(new_records):
                try:
                    url = f"{self.supabase_url}/rest/v1/{self.table_name}"



                    json_data = json.dumps([record], ensure_ascii=False, default=str)

                    response = requests.post(
                        url,
                        headers=self.headers,
                        data=json_data,
                        timeout=30
                    )

                    if response.status_code in [200, 201]:
                        total_inserted += 1
                        if (i + 1) % 25 == 0:  # Progress update every 25 records
                            print(f"✅ Inserted {i + 1}/{len(new_records)} records")
                    elif response.status_code == 409:  # Duplicate key constraint
                        total_duplicates_at_insert += 1
                        insert_duplicates.append(record)  # Track the duplicate record
                        if i < 5:  # Only show first few duplicate messages
                            print(f"🔍 Record {i + 1} was duplicate at insert time")
                    else:
                        total_errors += 1
                        print(f"❌ Record {i + 1} failed: {response.status_code} - {response.text}")

                except Exception as e:
                    total_errors += 1
                    print(f"❌ Error inserting record {i + 1}: {e}")

            # Final progress update
            if total_inserted > 0:
                print(f"✅ Completed: {total_inserted}/{len(new_records)} records inserted")

            # Save insert-time duplicates to CSV
            if insert_duplicates:
                self._save_duplicates_to_csv(insert_duplicates, "insert")

            print(f"\n📊 DATABASE INSERTION SUMMARY:")
            print(f"   Total records processed: {len(records)}")
            print(f"   Duplicates skipped (pre-check): {duplicate_count}")
            print(f"   Duplicates found at insert: {total_duplicates_at_insert}")
            print(f"   New records inserted: {total_inserted}")
            print(f"   Errors: {total_errors}")

            return {
                'success': total_errors == 0,
                'inserted': total_inserted,
                'duplicates': duplicate_count + total_duplicates_at_insert,
                'errors': total_errors
            }
            
        except Exception as e:
            print(f"💥 Error in database insertion: {e}")
            return {'success': False, 'inserted': 0, 'duplicates': 0, 'errors': len(records)}
    
    def get_record_count(self):
        """
        Get the total number of records in the database

        Returns:
            int: Total record count
        """
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {'select': 'count', 'head': 'true'}

            response = requests.head(url, headers=self.headers, params=params, timeout=30)

            if response.status_code == 200:
                count_header = response.headers.get('Content-Range', '0')
                # Content-Range format: "0-49/150" where 150 is total count
                if '/' in count_header:
                    return int(count_header.split('/')[-1])

            return 0

        except Exception as e:
            print(f"Error getting record count: {e}")
            return 0

    def test_null_normalization(self):
        """
        Test the null value normalization function with various inputs.
        This method helps verify that the normalize_null_values function works correctly.

        Returns:
            dict: Test results showing input values and their normalized outputs
        """
        test_cases = [
            # Standard null representations
            "null", "NULL", "Null", "nil", "NIL", "Nil",
            "N/A", "n/a", "NA", "na", "#N/A", "#NA",

            # Empty and whitespace
            "", "   ", "\t", "\n", "  \t  \n  ",

            # Single character representations
            "-", "_", ".",

            # Repeated characters
            "---", "...", "___", "----", ".....",

            # Other null-like values
            "none", "NONE", "None", "undefined", "UNDEFINED",
            "empty", "EMPTY", "missing", "unknown",
            "not available", "not applicable", "no data",

            # Numeric null representations
            float('nan'), "nan", "NaN", "NAN", "inf", "-inf",

            # Valid data that should NOT be converted to null
            "Company-Name", "Real Data", "123", "Valid Text",
            "N/A Company Ltd", "This is - a valid name",
            "Test Data with - dashes", "0123456789",
            "0", "00", "000", "0.0", "0.00",  # Zero values should be preserved

            # Edge cases
            None, 0, 1, True, False, [], {}
        ]

        results = {}
        print("\n🧪 TESTING NULL VALUE NORMALIZATION")
        print("=" * 60)

        for test_value in test_cases:
            try:
                normalized = self.normalize_null_values(test_value)
                results[str(test_value)] = normalized

                # Show the transformation
                if normalized is None:
                    print(f"✅ '{test_value}' → NULL")
                else:
                    print(f"📝 '{test_value}' → '{normalized}'")

            except Exception as e:
                results[str(test_value)] = f"ERROR: {e}"
                print(f"❌ '{test_value}' → ERROR: {e}")

        print("=" * 60)
        print(f"📊 Test completed: {len(test_cases)} cases processed")

        # Count how many were converted to null
        null_count = sum(1 for v in results.values() if v is None)
        preserved_count = len(results) - null_count

        print(f"🔄 Converted to NULL: {null_count}")
        print(f"📋 Preserved as data: {preserved_count}")

        return results
