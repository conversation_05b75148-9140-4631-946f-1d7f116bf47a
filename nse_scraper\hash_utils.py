"""
Hash Utilities for NSE Scraper
Provides consistent hash generation for duplicate detection across different data types.

Note: This module is being refactored to use shared.data_processor.UnifiedHashCalculator
for better consistency across scrapers.
"""

import hashlib
import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def generate_record_hash(row, data_type='insider_trading'):
    """
    Generate a unique hash for a record to detect duplicates.
    Works with both CSV format and database format records.

    Args:
        row (dict): Record data
        data_type (str): Type of data - 'insider_trading'

    Returns:
        str: MD5 hash of the record
    """
    return _generate_insider_trading_hash(row)


def _generate_insider_trading_hash(row):
    """
    Generate hash for insider trading records.

    Args:
        row (dict): Insider trading record data

    Returns:
        str: MD5 hash of the record
    """
    # Use key fields that uniquely identify a transaction
    # Handle both CSV format and database format
    # Exclude broadcast_date_and_time and include holdings prior/post for better duplicate detection
    key_fields = [
        str(row.get('symbol', row.get('SYMBOL', ''))),
        str(row.get('name_of_the_acquirer_disposer', row.get('NAME OF THE ACQUIRER/DISPOSER', ''))),
        str(row.get('no_of_securities_acquired_displosed', row.get('NO. OF SECURITIES (ACQUIRED/DISPLOSED)', ''))),
        str(row.get('value_of_security_acquired_displosed', row.get('VALUE OF SECURITY (ACQUIRED/DISPLOSED)', ''))),
        str(row.get('no_of_security_post', row.get('NO. OF SECURITY (POST)', ''))),
        str(row.get('no_of_security_prior', row.get('NO. OF SECURITY (PRIOR)', ''))),
        str(row.get('date_of_intimation_to_company', row.get('DATE OF INITMATION TO COMPANY', ''))),
        str(row.get('acquisition_disposal_transaction_type', row.get('ACQUISITION/DISPOSAL TRANSACTION TYPE', '')))
    ]

    # Create a string from key fields and hash it
    key_string = '|'.join(key_fields)
    return hashlib.md5(key_string.encode('utf-8')).hexdigest()





def add_record_hashes(dataframe, data_type='insider_trading'):
    """
    Add record hash column to DataFrame for duplicate detection.
    Uses the standardized hash function from shared.utils.calculate_unified_hash.

    Args:
        dataframe: pandas DataFrame with NSE insider trading data
        data_type (str): Type of data - 'insider_trading'

    Returns:
        pandas DataFrame with added 'record_hash' column
    """
    try:
        if dataframe is None or dataframe.empty:
            return dataframe

        from shared.utils import calculate_unified_hash

        print(f"Generating hashes for {len(dataframe)} NSE insider trading records using shared hash function...")

        # Generate hash for each row using the standardized hash function
        def calculate_nse_hash(row):
            try:
                return calculate_unified_hash('nse', **row.to_dict())
            except Exception as e:
                print(f"⚠️ Hash calculation error for row: {e}")
                # Fallback to legacy method for this row
                return generate_record_hash(row, data_type)

        dataframe['record_hash'] = dataframe.apply(calculate_nse_hash, axis=1)

        # Check for any duplicate hashes within this dataset
        duplicate_hashes = dataframe['record_hash'].duplicated().sum()
        if duplicate_hashes > 0:
            print(f"⚠️ Found {duplicate_hashes} duplicate hashes within NSE insider trading dataset")

        print(f"✅ Generated {len(dataframe)} hashes for NSE insider trading records")
        return dataframe

    except Exception as e:
        print(f"❌ Error generating hashes: {e}")
        # Fallback to legacy method if shared hash function fails
        print("🔄 Falling back to legacy hash calculation...")

        print(f"Generating hashes for {len(dataframe)} NSE insider trading records...")

        # Generate hash for each row with the specified data type
        dataframe['record_hash'] = dataframe.apply(
            lambda row: generate_record_hash(row, data_type), axis=1
        )

        # Check for any duplicate hashes within this dataset
        duplicate_hashes = dataframe['record_hash'].duplicated().sum()
        if duplicate_hashes > 0:
            print(f"⚠️ Found {duplicate_hashes} duplicate hashes within NSE insider trading dataset")

        print(f"✅ Generated {len(dataframe)} hashes for NSE insider trading records")
        return dataframe
