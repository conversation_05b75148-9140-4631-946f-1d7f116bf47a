"""
Main NSE Insider Trading Scraper Class
Combines all components to provide a unified interface for scraping NSE data.
"""

from .session_manager import Session<PERSON>anager
from .data_fetcher import DataFetcher
from .csv_parser import CSVParser
from .database_manager import DatabaseManager
from .config import DEFAULT_DAYS_BACK, USE_DATABASE, SAVE_CSV


class NSEInsiderTradingScraper:
    """Main scraper class that orchestrates all components"""
    
    def __init__(self):
        """Initialize the scraper with all necessary components"""
        self.session_manager = SessionManager()
        self.data_fetcher = DataFetcher(self.session_manager)
        self.database_manager = DatabaseManager() if USE_DATABASE else None
        print("NSE Insider Trading Scraper initialized")
        if USE_DATABASE:
            print("✅ Database integration enabled")
        if SAVE_CSV:
            print("✅ CSV saving enabled")
    
    def fetch_data(self, days_back=DEFAULT_DAYS_BACK):
        """
        Fetch insider trading data for the specified number of days
        
        Args:
            days_back (int): Number of days to look back from today
            
        Returns:
            tuple: (DataFrame, from_date_str, to_date_str) or None if failed
        """
        print(f"Fetching insider trading data for the last {days_back} days...")
        return self.data_fetcher.fetch_insider_data(days_back=days_back)
    
    def fetch_data_for_date_range(self, from_date, to_date):
        """
        Fetch insider trading data for a custom date range

        Args:
            from_date (str): Start date in 'DD-MM-YYYY' format
            to_date (str): End date in 'DD-MM-YYYY' format

        Returns:
            tuple: (DataFrame, from_date_str, to_date_str) or None if failed
        """
        print(f"Fetching insider trading data from {from_date} to {to_date}...")
        return self.data_fetcher.fetch_custom_date_range(from_date, to_date)


    
    def save_data_to_csv(self, df, from_date, to_date, filename=None):
        """
        Save insider trading DataFrame to CSV file with duplicate detection

        Args:
            df (pandas.DataFrame): Insider trading data to save
            from_date (str): Start date for logging
            to_date (str): End date for logging
            filename (str, optional): Custom filename. If None, uses persistent file

        Returns:
            bool: True if successful, False otherwise
        """
        if not SAVE_CSV:
            print("CSV saving is disabled in configuration")
            return True

        return CSVParser.save_dataframe_to_csv(df, from_date, to_date, filename, subfolder="insider_trading")




    def save_data_to_database(self, df):
        """
        Save DataFrame to Supabase database with duplicate detection

        Args:
            df (pandas.DataFrame): Data to save

        Returns:
            dict: Summary of database insertion results
        """
        if not USE_DATABASE or not self.database_manager:
            print("Database saving is disabled in configuration")
            return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}

        # Convert DataFrame to list of dictionaries
        records = df.to_dict('records')

        # Insert records to database
        return self.database_manager.insert_records(records)
    
    def get_data_summary(self, df):
        """
        Get a summary of the fetched data
        
        Args:
            df (pandas.DataFrame): Data to summarize
            
        Returns:
            dict: Summary information
        """
        if df is None or len(df) == 0:
            return {"total_records": 0, "columns": [], "companies": []}
        
        summary = {
            "total_records": len(df),
            "columns": df.columns.tolist(),
            "companies": df.iloc[:, 0].unique().tolist() if len(df.columns) > 0 else [],
            "date_range": {
                "earliest": None,
                "latest": None
            }
        }
        
        # Try to find date columns and get date range
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        if date_columns:
            try:
                import pandas as pd
                # Use the first date column found
                date_col = date_columns[0]
                dates = pd.to_datetime(df[date_col], errors='coerce').dropna()
                if not dates.empty:
                    summary["date_range"]["earliest"] = dates.min().strftime('%Y-%m-%d')
                    summary["date_range"]["latest"] = dates.max().strftime('%Y-%m-%d')
            except:
                pass
        
        return summary
    
    def close(self):
        """Close the scraper and clean up resources"""
        self.session_manager.close_session()
        print("Scraper closed")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
