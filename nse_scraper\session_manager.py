"""
Session Manager for NSE Insider Trading Scraper
Handles session setup and headers configuration.
"""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_session_manager import NSESessionManager
from .config import config


class SessionManager(NSESessionManager):
    """
    NSE-specific session manager that inherits from the shared base class.

    This class maintains backward compatibility while using the shared
    session management functionality.
    """

    def __init__(self):
        """
        Initialize NSE session manager.
        """
        super().__init__(config)
