# Deployment Scripts

This directory contains shell scripts for Phase 1 manual deployment of the scrapers to Google Cloud Platform.

## 📋 Scripts Overview

### 1. `deploy-phase1.sh`
**Purpose**: Initial infrastructure setup and configuration

**What it does**:
- Enables required Google Cloud APIs
- Creates Artifact Registry repository
- Creates service accounts with proper IAM roles
- Creates Secret Manager secrets (empty, need to be populated)

**Usage**:
```bash
./scripts/deploy-phase1.sh
```

**Prerequisites**:
- Authenticated with `gcloud auth login`
- Project ID configured in `config/scrapers-config.json`
- `jq` installed for JSON processing

### 2. `build-and-push.sh`
**Purpose**: Build Docker images and push to Artifact Registry

**What it does**:
- Configures Docker authentication with Artifact Registry
- Builds Docker images for all three scrapers
- Tags images with timestamp and latest tags
- Pushes images to Google Artifact Registry
- Verifies images in registry
- Cleans up local dangling images

**Usage**:
```bash
./scripts/build-and-push.sh
```

**Prerequisites**:
- Infrastructure setup completed (`deploy-phase1.sh`)
- Docker installed and running
- Dockerfiles present in scraper directories

### 3. `deploy-jobs.sh`
**Purpose**: Deploy Cloud Run jobs for all scrapers

**What it does**:
- Creates Cloud Run jobs for each scraper
- Configures resource limits (CPU, memory, timeout)
- Sets up environment variables and secrets
- Assigns service account permissions
- Verifies deployments

**Usage**:
```bash
./scripts/deploy-jobs.sh
```

**Prerequisites**:
- Images built and pushed (`build-and-push.sh`)
- Service accounts created
- Secrets populated in Secret Manager

### 4. `setup-scheduler.sh`
**Purpose**: Configure Cloud Scheduler for automated execution

**What it does**:
- Creates Cloud Scheduler jobs for each scraper
- Configures cron schedules based on config file
- Sets up retry policies and error handling
- Assigns scheduler service account permissions
- Optionally tests scheduler jobs

**Usage**:
```bash
./scripts/setup-scheduler.sh
```

**Prerequisites**:
- Cloud Run jobs deployed (`deploy-jobs.sh`)
- Scheduler service account created

## 🚀 Quick Start

### Complete Deployment Sequence

```bash
# 1. Make scripts executable
chmod +x scripts/*.sh

# 2. Setup infrastructure
./scripts/deploy-phase1.sh

# 3. Set secret values (replace with your actual secrets)
echo "your-supabase-anon-key" | gcloud secrets versions add supabase-anon-key --data-file=-
echo "your-supabase-service-key" | gcloud secrets versions add supabase-service-key --data-file=-

# 4. Build and push images
./scripts/build-and-push.sh

# 5. Deploy Cloud Run jobs
./scripts/deploy-jobs.sh

# 6. Setup scheduling
./scripts/setup-scheduler.sh
```

## ⚙️ Configuration

All scripts read configuration from `config/scrapers-config.json`. Key configuration sections:

### Project Configuration
```json
{
  "environment": {
    "project_id": "webscrapers-463817"
  },
  "global": {
    "region": "asia-south1",
    "artifact_registry": "asia-south1-docker.pkg.dev"
  }
}
```

### Scraper Configuration
```json
{
  "scrapers": {
    "bse-insider": {
      "name": "bse-insider-trading-scraper",
      "image_name": "bse-scraper",
      "dockerfile_path": "bse_scraper/Dockerfile",
      "build_context": ".",
      "schedule": {
        "cron": "0 1 * * *",
        "enabled": true
      }
    }
  }
}
```

### Service Accounts
```json
{
  "security": {
    "service_accounts": {
      "scraper_runner": {
        "name": "scraper-runner",
        "roles": [
          "roles/secretmanager.secretAccessor",
          "roles/logging.logWriter"
        ]
      }
    }
  }
}
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Permission Denied
```bash
# Check authentication
gcloud auth list

# Re-authenticate if needed
gcloud auth login
```

#### 2. Project Not Set
```bash
# Set project
gcloud config set project webscrapers-463817
```

#### 3. APIs Not Enabled
```bash
# Check enabled APIs
gcloud services list --enabled

# Enable required APIs manually
gcloud services enable run.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
```

#### 4. Docker Authentication Issues
```bash
# Configure Docker auth
gcloud auth configure-docker asia-south1-docker.pkg.dev
```

#### 5. Secret Access Issues
```bash
# Check secret exists
gcloud secrets describe supabase-anon-key

# Check service account permissions
gcloud secrets get-iam-policy supabase-anon-key
```

### Script Debugging

Enable debug mode for detailed output:
```bash
# Run with debug output
bash -x ./scripts/deploy-phase1.sh
```

### Verification Commands

```bash
# Check service accounts
gcloud iam service-accounts list

# Check Cloud Run jobs
gcloud run jobs list --region=asia-south1

# Check scheduler jobs
gcloud scheduler jobs list --location=asia-south1

# Check secrets
gcloud secrets list

# Check Artifact Registry
gcloud artifacts repositories list --location=asia-south1
```

## 📊 Monitoring

### Check Deployment Status

```bash
# Check all Cloud Run jobs
for job in $(gcloud run jobs list --region=asia-south1 --format="value(metadata.name)"); do
  echo "Job: $job"
  gcloud run jobs describe "$job" --region=asia-south1 --format="value(status.conditions[0].type,status.conditions[0].status)"
done

# Check all scheduler jobs
for job in $(gcloud scheduler jobs list --location=asia-south1 --format="value(name)"); do
  echo "Scheduler: $job"
  gcloud scheduler jobs describe "$job" --location=asia-south1 --format="value(state)"
done
```

### View Recent Logs

```bash
# View logs for specific scraper
gcloud logging read "resource.type=cloud_run_job AND labels.job_name=bse-insider-trading-scraper" --limit=10

# View scheduler logs
gcloud logging read "resource.type=cloud_scheduler_job" --limit=10
```

## 🔧 Maintenance

### Update Images

```bash
# Rebuild and redeploy
./scripts/build-and-push.sh
./scripts/deploy-jobs.sh
```

### Update Schedules

```bash
# Modify config/scrapers-config.json
# Then run:
./scripts/setup-scheduler.sh
```

### Clean Up Resources

```bash
# Delete Cloud Run jobs
gcloud run jobs delete bse-insider-trading-scraper --region=asia-south1 --quiet

# Delete scheduler jobs
gcloud scheduler jobs delete bse-insider-trading-scraper-scheduler --location=asia-south1 --quiet

# Delete service accounts
gcloud iam service-<NAME_EMAIL> --quiet
```

## 📚 Additional Resources

- [Google Cloud SDK Documentation](https://cloud.google.com/sdk/docs)
- [Cloud Run Jobs Documentation](https://cloud.google.com/run/docs/create-jobs)
- [Cloud Scheduler Documentation](https://cloud.google.com/scheduler/docs)
- [Artifact Registry Documentation](https://cloud.google.com/artifact-registry/docs)

---

*For Phase 2 automated deployment using GitHub Actions, see the main [DEPLOYMENT_GUIDE.md](../docs/DEPLOYMENT_GUIDE.md)*
