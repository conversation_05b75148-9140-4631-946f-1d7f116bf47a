#!/bin/bash

# Build and Push Docker Images to Artifact Registry
# This script builds Docker images for all scrapers and pushes them to Google Artifact Registry

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/scrapers-config.json"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ Error: jq is required but not installed. Please install jq first.${NC}"
    exit 1
fi

# Load configuration
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}❌ Error: Configuration file not found: $CONFIG_FILE${NC}"
    exit 1
fi

# Extract configuration values
PROJECT_ID=$(jq -r '.environment.project_id' "$CONFIG_FILE")
REGION=$(jq -r '.global.region' "$CONFIG_FILE")
ARTIFACT_REGISTRY_BASE=$(jq -r '.global.artifact_registry' "$CONFIG_FILE")
REPOSITORY_NAME="scrapers"

# Build full registry URL
REGISTRY_URL="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}"

echo -e "${BLUE}🐳 Building and Pushing Docker Images${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "Project ID: ${YELLOW}$PROJECT_ID${NC}"
echo -e "Registry URL: ${YELLOW}$REGISTRY_URL${NC}"
echo ""

# Function to configure Docker authentication
configure_docker_auth() {
    echo -e "${BLUE}🔐 Configuring Docker authentication...${NC}"
    gcloud auth configure-docker "${REGION}-docker.pkg.dev" --quiet
    echo -e "${GREEN}✅ Docker authentication configured${NC}"
}

# Function to build and push a single scraper image
build_and_push_scraper() {
    local scraper_key=$1
    local scraper_name=$(jq -r ".scrapers.$scraper_key.name" "$CONFIG_FILE")
    local image_name=$(jq -r ".scrapers.$scraper_key.image_name" "$CONFIG_FILE")
    local dockerfile_path=$(jq -r ".scrapers.$scraper_key.dockerfile_path" "$CONFIG_FILE")
    local build_context=$(jq -r ".scrapers.$scraper_key.build_context" "$CONFIG_FILE")
    
    echo -e "${BLUE}🔨 Building $scraper_name...${NC}"
    
    # Generate image tag with timestamp
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local image_tag="${REGISTRY_URL}/${image_name}:${timestamp}"
    local latest_tag="${REGISTRY_URL}/${image_name}:latest"
    
    # Build the Docker image
    echo -e "  Building image: $image_tag"
    docker build \
        -f "$PROJECT_ROOT/$dockerfile_path" \
        -t "$image_tag" \
        -t "$latest_tag" \
        "$PROJECT_ROOT/$build_context"
    
    # Push the images
    echo -e "  Pushing image: $image_tag"
    docker push "$image_tag"
    docker push "$latest_tag"
    
    echo -e "${GREEN}✅ $scraper_name built and pushed successfully${NC}"
    echo -e "  Image: $image_tag"
    echo -e "  Latest: $latest_tag"
    echo ""
    
    # Store the image URL for later use
    echo "$latest_tag" > "$PROJECT_ROOT/.${scraper_key}-image-url"
}

# Function to build all scrapers
build_all_scrapers() {
    echo -e "${BLUE}🏗️ Building all scraper images...${NC}"
    
    # Get list of scrapers from config
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        build_and_push_scraper "$scraper"
    done
}

# Function to clean up local images (optional)
cleanup_local_images() {
    echo -e "${BLUE}🧹 Cleaning up local images...${NC}"
    
    # Remove dangling images
    if docker images -f "dangling=true" -q | grep -q .; then
        docker rmi $(docker images -f "dangling=true" -q) 2>/dev/null || true
        echo -e "${GREEN}✅ Dangling images cleaned up${NC}"
    else
        echo -e "${YELLOW}📋 No dangling images to clean up${NC}"
    fi
}

# Function to verify images in registry
verify_images() {
    echo -e "${BLUE}🔍 Verifying images in registry...${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local image_name=$(jq -r ".scrapers.$scraper.image_name" "$CONFIG_FILE")
        echo -e "  Checking $image_name..."
        
        if gcloud artifacts docker images list "${REGISTRY_URL}/${image_name}" --limit=1 --quiet &>/dev/null; then
            echo -e "${GREEN}    ✅ $image_name found in registry${NC}"
        else
            echo -e "${RED}    ❌ $image_name not found in registry${NC}"
        fi
    done
}

# Function to display summary
display_summary() {
    echo -e "${GREEN}🎉 Build and Push Summary${NC}"
    echo -e "${GREEN}=========================${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
        local image_name=$(jq -r ".scrapers.$scraper.image_name" "$CONFIG_FILE")
        
        if [[ -f "$PROJECT_ROOT/.${scraper}-image-url" ]]; then
            local image_url=$(cat "$PROJECT_ROOT/.${scraper}-image-url")
            echo -e "${GREEN}✅ $scraper_name${NC}"
            echo -e "   Image: $image_url"
        else
            echo -e "${RED}❌ $scraper_name - Build failed${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "1. Deploy Cloud Run jobs: ./scripts/deploy-jobs.sh"
    echo -e "2. Setup Cloud Scheduler: ./scripts/setup-scheduler.sh"
}

# Main execution
main() {
    # Change to project root directory
    cd "$PROJECT_ROOT"
    
    configure_docker_auth
    build_all_scrapers
    cleanup_local_images
    verify_images
    display_summary
}

# Run main function
main "$@"
