#!/usr/bin/env python3
"""
Check PDF hash calculation status
Quick script to see how many records have hashes calculated.
"""

import requests
import json
import argparse

# Configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"

def check_table_status(supabase_key, table_name):
    """Check hash status for a specific table"""
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {supabase_key}',
        'apikey': supabase_key
    }
    
    try:
        # Get total records
        url = f"{SUPABASE_URL}/rest/v1/{table_name}"
        params = {'select': 'count', 'head': 'true'}
        response = requests.head(url, headers=headers, params=params, timeout=10)
        total_records = int(response.headers.get('Content-Range', '0').split('/')[-1])
        
        # Get records with PDF URLs
        pdf_url_column = 'attachmentfile' if table_name == 'bse_corporate_announcements' else 'attachment_url'
        params = {'select': 'count', 'head': 'true', pdf_url_column: 'not.is.null'}
        response = requests.head(url, headers=headers, params=params, timeout=10)
        records_with_pdfs = int(response.headers.get('Content-Range', '0').split('/')[-1])

        # Get records with hashes
        params = {'select': 'count', 'head': 'true', 'pdf_hash': 'not.is.null'}
        response = requests.head(url, headers=headers, params=params, timeout=10)
        records_with_hashes = int(response.headers.get('Content-Range', '0').split('/')[-1])

        # Get records needing hashes (have PDF but no hash)
        params = {
            'select': 'count',
            'head': 'true',
            pdf_url_column: 'not.is.null',
            'pdf_hash': 'is.null'
        }
        response = requests.head(url, headers=headers, params=params, timeout=10)
        records_needing_hashes = int(response.headers.get('Content-Range', '0').split('/')[-1])
        
        # Get duplicate records
        params = {'select': 'count', 'head': 'true', 'is_duplicate': 'eq.true'}
        response = requests.head(url, headers=headers, params=params, timeout=10)
        duplicate_records = int(response.headers.get('Content-Range', '0').split('/')[-1])
        
        # Get status breakdown
        status_counts = {}
        for status in ['pending', 'processing', 'completed', 'failed']:
            params = {'select': 'count', 'head': 'true', 'duplicate_check_status': f'eq.{status}'}
            response = requests.head(url, headers=headers, params=params, timeout=10)
            status_counts[status] = int(response.headers.get('Content-Range', '0').split('/')[-1])
        
        return {
            'table_name': table_name,
            'total_records': total_records,
            'records_with_pdfs': records_with_pdfs,
            'records_with_hashes': records_with_hashes,
            'records_needing_hashes': records_needing_hashes,
            'duplicate_records': duplicate_records,
            'status_counts': status_counts
        }
        
    except Exception as e:
        print(f"❌ Error checking {table_name}: {e}")
        return None

def print_status_report(status_data):
    """Print formatted status report"""
    if not status_data:
        return
    
    table_name = status_data['table_name']
    print(f"\n📊 {table_name.replace('_', ' ').title()}")
    print("-" * 50)
    print(f"Total records: {status_data['total_records']:,}")
    print(f"Records with PDFs: {status_data['records_with_pdfs']:,}")
    print(f"Records with hashes: {status_data['records_with_hashes']:,}")
    print(f"Records needing hashes: {status_data['records_needing_hashes']:,}")
    print(f"Duplicate records: {status_data['duplicate_records']:,}")
    
    if status_data['records_with_pdfs'] > 0:
        hash_percentage = (status_data['records_with_hashes'] / status_data['records_with_pdfs']) * 100
        print(f"Hash completion: {hash_percentage:.1f}%")
        
        if status_data['records_with_hashes'] > 0:
            duplicate_percentage = (status_data['duplicate_records'] / status_data['records_with_hashes']) * 100
            print(f"Duplicate rate: {duplicate_percentage:.1f}%")
    
    print(f"\nProcessing Status:")
    for status, count in status_data['status_counts'].items():
        print(f"  {status.capitalize()}: {count:,}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Check PDF hash calculation status')
    parser.add_argument('--supabase-key', required=True, help='Supabase API key')
    parser.add_argument('--table', choices=['bse_corporate_announcements', 'nse_corporate_announcements'], 
                       help='Check only specific table (default: both)')
    
    args = parser.parse_args()
    
    print("🔍 PDF Hash Calculation Status Report")
    print("=" * 60)
    
    tables = [args.table] if args.table else ['bse_corporate_announcements', 'nse_corporate_announcements']
    
    total_needing_hashes = 0
    total_with_hashes = 0
    
    for table_name in tables:
        status_data = check_table_status(args.supabase_key, table_name)
        if status_data:
            print_status_report(status_data)
            total_needing_hashes += status_data['records_needing_hashes']
            total_with_hashes += status_data['records_with_hashes']
    
    print("\n" + "=" * 60)
    print("📋 Summary")
    print("=" * 60)
    print(f"Total records with hashes: {total_with_hashes:,}")
    print(f"Total records needing hashes: {total_needing_hashes:,}")
    
    if total_needing_hashes > 0:
        print(f"\n💡 To calculate hashes for remaining records:")
        print(f"python scripts/calculate_all_pdf_hashes.py --supabase-key YOUR_KEY")
    else:
        print(f"\n✅ All records have hashes calculated!")

if __name__ == "__main__":
    main()

# Usage:
# python scripts/check_hash_status.py --supabase-key YOUR_KEY
# python scripts/check_hash_status.py --supabase-key YOUR_KEY --table bse_corporate_announcements
