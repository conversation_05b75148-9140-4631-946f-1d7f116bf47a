#!/usr/bin/env python3
"""
Database cleanup script to convert string representations of null values to actual NULL values.
This script will clean up any existing "null", "nil", "N/A", etc. string values in the database.
"""

import sys
import os
import requests
import json
from typing import List, Dict, Any

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import BaseConfig


class DatabaseCleanup:
    """Clean up null-like string values in the database"""
    
    def __init__(self):
        """Initialize database cleanup with Supabase configuration"""
        self.supabase_url = BaseConfig.SUPABASE_URL
        self.supabase_key = BaseConfig.SUPABASE_ANON_KEY
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        
        # Define null-like values that should be converted to NULL
        self.null_values = {
            'null', 'nil', 'NULL', 'NIL', 'NONE', 'None',
            'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 
            'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 
            'empty', 'EMPTY', '-', ''
        }
        
        # Define tables and their text columns to clean
        self.tables_config = {
            'nse_insider_trading': [
                'symbol', 'company', 'regulation', 'name_of_the_acquirer_disposer',
                'category_of_person', 'type_of_security_prior', 'no_of_security_prior',
                'shareholding_prior', 'type_of_security_acquired_displosed',
                'no_of_securities_acquired_displosed', 'value_of_security_acquired_displosed',
                'acquisition_disposal_transaction_type', 'type_of_security_post',
                'no_of_security_post', 'post_percentage', 'date_of_allotment_acquisition_from',
                'date_of_allotment_acquisition_to', 'date_of_intimation_to_company',
                'mode_of_acquisition', 'derivative_type_security', 'derivative_contract_specification',
                'notional_value_buy', 'number_of_units_contract_lot_size_buy',
                'notional_value_sell', 'number_of_units_contract_lot_size_sell',
                'exchange', 'remark', 'broadcast_date_and_time', 'xbrl'
            ],
            'nse_corporate_announcements': [
                'symbol', 'company_name', 'subject', 'details', 'attachment_url', 'file_size'
            ],
            'bse_insider_trading': [
                'security_code', 'security_name', 'name_of_person', 'category_of_person',
                'securities_held_pre_transaction', 'securities_acquired_disposed_type',
                'securities_acquired_disposed_number', 'securities_acquired_disposed_value',
                'securities_acquired_disposed_transaction_type', 'securities_held_post_transaction',
                'period', 'mode_of_acquisition', 'trading_derivatives_type_contract',
                'trading_derivatives_buy_value', 'trading_derivatives_sale_value',
                'reported_to_exchange'
            ]
        }
    
    def find_null_like_values(self, table_name: str, columns: List[str]) -> Dict[str, int]:
        """
        Find records with null-like string values in specified columns.
        
        Args:
            table_name (str): Name of the table to check
            columns (List[str]): List of columns to check
            
        Returns:
            Dict[str, int]: Dictionary with column names and count of null-like values
        """
        print(f"\n🔍 Checking table '{table_name}' for null-like string values...")
        
        results = {}
        
        for column in columns:
            try:
                # Create a query to count null-like values in this column
                null_conditions = [f"{column}.eq.{value}" for value in self.null_values if value]
                
                total_count = 0
                for condition in null_conditions:
                    url = f"{self.supabase_url}/rest/v1/{table_name}"
                    params = {
                        'select': 'count',
                        condition: 'true'
                    }
                    
                    response = requests.get(url, headers=self.headers, params=params, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data:
                            total_count += len(data)
                
                if total_count > 0:
                    results[column] = total_count
                    print(f"   📊 Column '{column}': {total_count} null-like values found")
                    
            except Exception as e:
                print(f"   ❌ Error checking column '{column}': {e}")
        
        if not results:
            print(f"   ✅ No null-like string values found in table '{table_name}'")
        
        return results
    
    def cleanup_table(self, table_name: str, columns: List[str]) -> Dict[str, Any]:
        """
        Clean up null-like string values in a table by setting them to NULL.
        
        Args:
            table_name (str): Name of the table to clean
            columns (List[str]): List of columns to clean
            
        Returns:
            Dict[str, Any]: Summary of cleanup results
        """
        print(f"\n🧹 Cleaning up table '{table_name}'...")
        
        total_updated = 0
        column_updates = {}
        
        for column in columns:
            try:
                updated_count = 0
                
                # Update each null-like value to NULL
                for null_value in self.null_values:
                    if not null_value:  # Skip empty string for now
                        continue
                        
                    url = f"{self.supabase_url}/rest/v1/{table_name}"
                    params = {f"{column}": f"eq.{null_value}"}
                    
                    update_data = {column: None}
                    
                    response = requests.patch(
                        url,
                        headers=self.headers,
                        params=params,
                        data=json.dumps(update_data),
                        timeout=30
                    )
                    
                    if response.status_code in [200, 204]:
                        # Count how many records were updated (if response includes count)
                        updated_count += 1
                    elif response.status_code != 404:  # 404 means no records found, which is OK
                        print(f"   ⚠️  Warning updating {column}={null_value}: {response.status_code}")
                
                if updated_count > 0:
                    column_updates[column] = updated_count
                    total_updated += updated_count
                    print(f"   ✅ Column '{column}': {updated_count} values cleaned")
                    
            except Exception as e:
                print(f"   ❌ Error cleaning column '{column}': {e}")
        
        return {
            'table': table_name,
            'total_updated': total_updated,
            'column_updates': column_updates
        }
    
    def run_cleanup(self, dry_run: bool = True) -> Dict[str, Any]:
        """
        Run the complete database cleanup process.
        
        Args:
            dry_run (bool): If True, only check for issues without fixing them
            
        Returns:
            Dict[str, Any]: Summary of cleanup results
        """
        print("🚀 Starting database cleanup process...")
        print(f"Mode: {'DRY RUN (checking only)' if dry_run else 'CLEANUP (fixing issues)'}")
        
        all_results = {}
        
        for table_name, columns in self.tables_config.items():
            if dry_run:
                # Just find and report null-like values
                results = self.find_null_like_values(table_name, columns)
                all_results[table_name] = {'found': results}
            else:
                # Actually clean up the values
                results = self.cleanup_table(table_name, columns)
                all_results[table_name] = results
        
        return all_results
    
    def print_summary(self, results: Dict[str, Any], dry_run: bool = True):
        """Print a summary of the cleanup results"""
        print("\n" + "="*60)
        print("📋 DATABASE CLEANUP SUMMARY")
        print("="*60)
        
        if dry_run:
            total_issues = 0
            for table_name, table_results in results.items():
                found = table_results.get('found', {})
                if found:
                    print(f"\n🔍 Table: {table_name}")
                    for column, count in found.items():
                        print(f"   - {column}: {count} null-like values")
                        total_issues += count
            
            if total_issues > 0:
                print(f"\n⚠️  Total null-like string values found: {total_issues}")
                print("💡 Run with dry_run=False to fix these issues")
            else:
                print("\n✅ No null-like string values found in database!")
        else:
            total_fixed = 0
            for table_name, table_results in results.items():
                updated = table_results.get('total_updated', 0)
                if updated > 0:
                    print(f"\n✅ Table: {table_name}")
                    print(f"   - Total values cleaned: {updated}")
                    total_fixed += updated
            
            if total_fixed > 0:
                print(f"\n🎉 Total null-like values fixed: {total_fixed}")
            else:
                print("\n✅ No issues found to fix!")


def main():
    """Main function to run the database cleanup"""
    cleanup = DatabaseCleanup()
    
    # First run a dry run to check for issues
    print("Phase 1: Checking for null-like string values...")
    dry_results = cleanup.run_cleanup(dry_run=True)
    cleanup.print_summary(dry_results, dry_run=True)
    
    # Ask user if they want to proceed with cleanup
    if any(table_results.get('found', {}) for table_results in dry_results.values()):
        response = input("\n❓ Do you want to proceed with cleanup? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            print("\nPhase 2: Cleaning up null-like string values...")
            cleanup_results = cleanup.run_cleanup(dry_run=False)
            cleanup.print_summary(cleanup_results, dry_run=False)
        else:
            print("🚫 Cleanup cancelled by user.")
    
    print("\n✨ Database cleanup process completed!")


if __name__ == "__main__":
    main()
