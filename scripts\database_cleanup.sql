-- Database Cleanup Script
-- This script converts string representations of null values to actual NULL values
-- Execute this script in your Supabase SQL editor or database management tool

BEGIN;

-- Define null-like values to be cleaned up
-- 'null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', 
-- '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-'

-- ============================================================================
-- NSE INSIDER TRADING TABLE CLEANUP
-- ============================================================================

-- Clean up NSE insider trading table
UPDATE nse_insider_trading 
SET symbol = NULL 
WHERE symbol IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET company = NULL 
WHERE company IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET regulation = NULL 
WHERE regulation IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET name_of_the_acquirer_disposer = NULL 
WHERE name_of_the_acquirer_disposer IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET category_of_person = NULL 
WHERE category_of_person IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET type_of_security_prior = NULL 
WHERE type_of_security_prior IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET mode_of_acquisition = NULL 
WHERE mode_of_acquisition IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_insider_trading 
SET remark = NULL 
WHERE remark IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

-- ============================================================================
-- NSE CORPORATE ANNOUNCEMENTS TABLE CLEANUP
-- ============================================================================

UPDATE nse_corporate_announcements 
SET symbol = NULL 
WHERE symbol IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_corporate_announcements 
SET company_name = NULL 
WHERE company_name IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_corporate_announcements 
SET subject = NULL 
WHERE subject IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_corporate_announcements 
SET details = NULL 
WHERE details IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_corporate_announcements 
SET attachment_url = NULL 
WHERE attachment_url IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE nse_corporate_announcements 
SET file_size = NULL 
WHERE file_size IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

-- ============================================================================
-- BSE INSIDER TRADING TABLE CLEANUP
-- ============================================================================

UPDATE bse_insider_trading 
SET security_code = NULL 
WHERE security_code IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET security_name = NULL 
WHERE security_name IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET name_of_person = NULL 
WHERE name_of_person IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET category_of_person = NULL 
WHERE category_of_person IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET mode_of_acquisition = NULL 
WHERE mode_of_acquisition IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET securities_held_pre_transaction = NULL 
WHERE securities_held_pre_transaction IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET securities_acquired_disposed_type = NULL 
WHERE securities_acquired_disposed_type IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

UPDATE bse_insider_trading 
SET reported_to_exchange = NULL 
WHERE reported_to_exchange IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-');

COMMIT;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================
-- Run these queries after the cleanup to verify the results

-- Check NSE insider trading for remaining null-like values
SELECT 'nse_insider_trading' as table_name, 'symbol' as column_name, COUNT(*) as null_like_count 
FROM nse_insider_trading 
WHERE symbol IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

SELECT 'nse_insider_trading', 'company', COUNT(*) 
FROM nse_insider_trading 
WHERE company IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

SELECT 'nse_insider_trading', 'mode_of_acquisition', COUNT(*) 
FROM nse_insider_trading 
WHERE mode_of_acquisition IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

-- Check NSE corporate announcements
SELECT 'nse_corporate_announcements', 'symbol', COUNT(*) 
FROM nse_corporate_announcements 
WHERE symbol IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

SELECT 'nse_corporate_announcements', 'company_name', COUNT(*) 
FROM nse_corporate_announcements 
WHERE company_name IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

-- Check BSE insider trading
SELECT 'bse_insider_trading', 'security_code', COUNT(*) 
FROM bse_insider_trading 
WHERE security_code IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

UNION ALL

SELECT 'bse_insider_trading', 'security_name', COUNT(*) 
FROM bse_insider_trading 
WHERE security_name IN ('null', 'nil', 'NULL', 'NIL', 'NONE', 'None', 'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 'empty', 'EMPTY', '-')

ORDER BY table_name, column_name;

-- Summary query to show NULL counts after cleanup
SELECT 
    'nse_insider_trading' as table_name,
    SUM(CASE WHEN symbol IS NULL THEN 1 ELSE 0 END) as symbol_nulls,
    SUM(CASE WHEN company IS NULL THEN 1 ELSE 0 END) as company_nulls,
    SUM(CASE WHEN mode_of_acquisition IS NULL THEN 1 ELSE 0 END) as mode_of_acquisition_nulls,
    SUM(CASE WHEN remark IS NULL THEN 1 ELSE 0 END) as remark_nulls,
    COUNT(*) as total_records
FROM nse_insider_trading

UNION ALL

SELECT 
    'nse_corporate_announcements',
    SUM(CASE WHEN symbol IS NULL THEN 1 ELSE 0 END),
    SUM(CASE WHEN company_name IS NULL THEN 1 ELSE 0 END),
    0, -- placeholder
    0, -- placeholder
    COUNT(*)
FROM nse_corporate_announcements

UNION ALL

SELECT 
    'bse_insider_trading',
    SUM(CASE WHEN security_code IS NULL THEN 1 ELSE 0 END),
    SUM(CASE WHEN security_name IS NULL THEN 1 ELSE 0 END),
    SUM(CASE WHEN mode_of_acquisition IS NULL THEN 1 ELSE 0 END),
    0, -- placeholder
    COUNT(*)
FROM bse_insider_trading;
