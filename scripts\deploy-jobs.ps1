# Enhanced PowerShell Deployment Script for NSE and BSE Scrapers
# This script builds and deploys the Cloud Run Jobs with ScraperAPI proxy integration

param(
    [string]$ProjectId = "webscrapers-463817",
    [string]$Region = "asia-south1"
)

# Configuration
$RepositoryName = "scrapers"
$RegistryUrl = "$Region-docker.pkg.dev/$ProjectId/$RepositoryName"

Write-Host "Enhanced Scraper Deployment with ScraperAPI Integration" -ForegroundColor Green
Write-Host "=======================================================" -ForegroundColor Green

# Set project
Write-Host "Setting project to $ProjectId..." -ForegroundColor Yellow
gcloud config set project $ProjectId

# Configure Docker authentication for Artifact Registry
Write-Host "Configuring Docker authentication..." -ForegroundColor Yellow
gcloud auth configure-docker "$Region-docker.pkg.dev" --quiet

# Build and push NSE scraper
Write-Host "Building NSE scraper Docker image..." -ForegroundColor Yellow
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$nseImageTag = "$RegistryUrl/nse-scraper:$timestamp"
$nseLatestTag = "$RegistryUrl/nse-scraper:latest"
docker build -t $nseImageTag -t $nseLatestTag -f nse_scraper/Dockerfile .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NSE scraper image built successfully" -ForegroundColor Green

    Write-Host "Pushing NSE scraper image..." -ForegroundColor Yellow
    docker push $nseImageTag
    docker push $nseLatestTag

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ NSE scraper image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to push NSE scraper image" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ Failed to build NSE scraper image" -ForegroundColor Red
    exit 1
}

# Build and push BSE scraper
Write-Host "Building BSE scraper Docker image..." -ForegroundColor Yellow
$bseImageTag = "$RegistryUrl/bse-scraper:$timestamp"
$bseLatestTag = "$RegistryUrl/bse-scraper:latest"
docker build -t $bseImageTag -t $bseLatestTag -f bse_scraper/Dockerfile .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ BSE scraper image built successfully" -ForegroundColor Green

    Write-Host "Pushing BSE scraper image..." -ForegroundColor Yellow
    docker push $bseImageTag
    docker push $bseLatestTag

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ BSE scraper image pushed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to push BSE scraper image" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ Failed to build BSE scraper image" -ForegroundColor Red
    exit 1
}

# Deploy NSE Cloud Run Job
Write-Host "Deploying NSE Cloud Run Job..." -ForegroundColor Yellow
$serviceAccount = "scraper-runner@$ProjectId.iam.gserviceaccount.com"

# Try to create NSE job, if it exists, update it
gcloud run jobs create nse-insider-trading-scraper `
    --image=$nseLatestTag `
    --region=$Region `
    --service-account=$serviceAccount `
    --cpu=1 `
    --memory=1Gi `
    --max-retries=3 `
    --parallelism=1 `
    --task-timeout=2400s `
    --execution-environment=gen2 `
    --set-env-vars PYTHONUNBUFFERED=1,PYTHONDONTWRITEBYTECODE=1,SCRAPER_TYPE=nse-insider,SCRAPERAPI_KEY=********************************,USE_PROXY=true `
    --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest,SUPABASE_SERVICE_KEY=supabase-service-key:latest `
    --args="--days,7" `
    --labels=scraper-type=nse-insider,environment=prod,managed-by=manual `
    --quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "Job exists, updating instead..." -ForegroundColor Yellow
    gcloud run jobs update nse-insider-trading-scraper `
        --image=$nseLatestTag `
        --region=$Region `
        --service-account=$serviceAccount `
        --cpu=1 `
        --memory=1Gi `
        --max-retries=3 `
        --parallelism=1 `
        --task-timeout=2400s `
        --execution-environment=gen2 `
        --set-env-vars PYTHONUNBUFFERED=1,PYTHONDONTWRITEBYTECODE=1,SCRAPER_TYPE=nse-insider,SCRAPERAPI_KEY=********************************,USE_PROXY=true `
        --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest,SUPABASE_SERVICE_KEY=supabase-service-key:latest `
        --args="--days,7" `
        --quiet
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NSE Cloud Run Job deployed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to deploy NSE Cloud Run Job" -ForegroundColor Red
}

# Deploy BSE Cloud Run Job
Write-Host "Deploying BSE Cloud Run Job..." -ForegroundColor Yellow

# Try to create BSE job, if it exists, update it
gcloud run jobs create bse-insider-trading-scraper `
    --image=$bseLatestTag `
    --region=$Region `
    --service-account=$serviceAccount `
    --cpu=1 `
    --memory=1Gi `
    --max-retries=3 `
    --parallelism=1 `
    --task-timeout=2400s `
    --execution-environment=gen2 `
    --set-env-vars PYTHONUNBUFFERED=1,PYTHONDONTWRITEBYTECODE=1,SCRAPER_TYPE=bse-insider,SCRAPERAPI_KEY=********************************,USE_PROXY=true `
    --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest,SUPABASE_SERVICE_KEY=supabase-service-key:latest `
    --args="--days,7" `
    --labels=scraper-type=bse-insider,environment=prod,managed-by=manual `
    --quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "Job exists, updating instead..." -ForegroundColor Yellow
    gcloud run jobs update bse-insider-trading-scraper `
        --image=$bseLatestTag `
        --region=$Region `
        --service-account=$serviceAccount `
        --cpu=1 `
        --memory=1Gi `
        --max-retries=3 `
        --parallelism=1 `
        --task-timeout=2400s `
        --execution-environment=gen2 `
        --set-env-vars PYTHONUNBUFFERED=1,PYTHONDONTWRITEBYTECODE=1,SCRAPER_TYPE=bse-insider,SCRAPERAPI_KEY=********************************,USE_PROXY=true `
        --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest,SUPABASE_SERVICE_KEY=supabase-service-key:latest `
        --args="--days,7" `
        --quiet
}

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ BSE Cloud Run Job deployed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to deploy BSE Cloud Run Job" -ForegroundColor Red
}

Write-Host "🎉 Enhanced Deployment process completed!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Test the deployments
Write-Host "Testing deployments..." -ForegroundColor Yellow

# Test NSE job
Write-Host "Testing NSE job execution..." -ForegroundColor Yellow
gcloud run jobs execute nse-insider-trading-scraper --region=$Region --async

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ NSE job execution started" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start NSE job execution" -ForegroundColor Red
}

# Test BSE job
Write-Host "Testing BSE job execution..." -ForegroundColor Yellow
gcloud run jobs execute bse-insider-trading-scraper --region=$Region --async

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ BSE job execution started" -ForegroundColor Green
} else {
    Write-Host "❌ Failed to start BSE job execution" -ForegroundColor Red
}

Write-Host ""
Write-Host "✅ Enhanced scrapers with ScraperAPI proxy integration deployed!" -ForegroundColor Green
Write-Host "Monitor executions with:" -ForegroundColor Cyan
Write-Host "gcloud run jobs executions list --job=nse-insider-trading-scraper --region=$Region" -ForegroundColor Cyan
Write-Host "gcloud run jobs executions list --job=bse-insider-trading-scraper --region=$Region" -ForegroundColor Cyan
Write-Host ""
Write-Host "View in Cloud Console:" -ForegroundColor Blue
Write-Host "https://console.cloud.google.com/run/jobs?project=$ProjectId" -ForegroundColor Cyan
