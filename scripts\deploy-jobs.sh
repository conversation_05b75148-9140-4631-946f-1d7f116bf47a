#!/bin/bash

# Deploy Cloud Run Jobs for Scrapers
# This script deploys all scrapers as Cloud Run jobs

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/scrapers-config.json"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ Error: jq is required but not installed. Please install jq first.${NC}"
    exit 1
fi

# Load configuration
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}❌ Error: Configuration file not found: $CONFIG_FILE${NC}"
    exit 1
fi

# Extract configuration values
PROJECT_ID=$(jq -r '.environment.project_id' "$CONFIG_FILE")
REGION=$(jq -r '.global.region' "$CONFIG_FILE")
REPOSITORY_NAME="scrapers"
REGISTRY_URL="${REGION}-docker.pkg.dev/${PROJECT_ID}/${REPOSITORY_NAME}"

# Cloud Run configuration
CPU=$(jq -r '.environment.cloud_run.cpu' "$CONFIG_FILE")
MEMORY=$(jq -r '.environment.cloud_run.memory' "$CONFIG_FILE")
MAX_INSTANCES=$(jq -r '.environment.cloud_run.max_instances' "$CONFIG_FILE")
TIMEOUT=$(jq -r '.environment.cloud_run.timeout' "$CONFIG_FILE")
MIN_INSTANCES=$(jq -r '.environment.cloud_run.min_instances' "$CONFIG_FILE")
CONCURRENCY=$(jq -r '.environment.cloud_run.concurrency' "$CONFIG_FILE")
EXECUTION_ENV=$(jq -r '.environment.cloud_run.execution_environment' "$CONFIG_FILE")

# Service account
SERVICE_ACCOUNT_NAME=$(jq -r '.security.service_accounts.scraper_runner.name' "$CONFIG_FILE")
SERVICE_ACCOUNT="${SERVICE_ACCOUNT_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

echo -e "${BLUE}☁️ Deploying Cloud Run Jobs${NC}"
echo -e "${BLUE}============================${NC}"
echo -e "Project ID: ${YELLOW}$PROJECT_ID${NC}"
echo -e "Region: ${YELLOW}$REGION${NC}"
echo -e "Service Account: ${YELLOW}$SERVICE_ACCOUNT${NC}"
echo ""

# Function to deploy a single Cloud Run job
deploy_job() {
    local scraper_key=$1
    local scraper_name=$(jq -r ".scrapers.$scraper_key.name" "$CONFIG_FILE")
    local image_name=$(jq -r ".scrapers.$scraper_key.image_name" "$CONFIG_FILE")
    local description=$(jq -r ".scrapers.$scraper_key.description" "$CONFIG_FILE")
    
    echo -e "${BLUE}🚀 Deploying $scraper_name...${NC}"
    
    # Check if image URL file exists
    local image_url_file="$PROJECT_ROOT/.${scraper_key}-image-url"
    if [[ ! -f "$image_url_file" ]]; then
        echo -e "${RED}❌ Error: Image URL file not found: $image_url_file${NC}"
        echo -e "Please run ./scripts/build-and-push.sh first"
        return 1
    fi
    
    local image_url=$(cat "$image_url_file")
    
    # Build environment variables
    local env_vars=""
    local env_var_keys=$(jq -r ".scrapers.$scraper_key.env_vars | keys[]" "$CONFIG_FILE" 2>/dev/null || echo "")
    
    for env_key in $env_var_keys; do
        local env_value=$(jq -r ".scrapers.$scraper_key.env_vars.$env_key" "$CONFIG_FILE")
        env_vars="$env_vars --set-env-vars $env_key=$env_value"
    done
    
    # Add Supabase secrets as environment variables
    env_vars="$env_vars --set-secrets SUPABASE_ANON_KEY=supabase-anon-key:latest"
    env_vars="$env_vars --set-secrets SUPABASE_SERVICE_KEY=supabase-service-key:latest"
    
    # Build command arguments
    local cmd_args=""
    local arg_keys=$(jq -r ".scrapers.$scraper_key.args | keys[]" "$CONFIG_FILE" 2>/dev/null || echo "")
    
    for arg_key in $arg_keys; do
        local arg_value=$(jq -r ".scrapers.$scraper_key.args.$arg_key" "$CONFIG_FILE")
        if [[ "$arg_value" == "true" ]]; then
            cmd_args="$cmd_args --$arg_key"
        elif [[ "$arg_value" != "false" ]]; then
            cmd_args="$cmd_args --$arg_key $arg_value"
        fi
    done
    
    # Deploy the Cloud Run job
    echo -e "  Creating Cloud Run job: $scraper_name"
    gcloud run jobs create "$scraper_name" \
        --image="$image_url" \
        --region="$REGION" \
        --service-account="$SERVICE_ACCOUNT" \
        --cpu="$CPU" \
        --memory="$MEMORY" \
        --max-retries=3 \
        --parallelism=1 \
        --task-count=1 \
        --task-timeout="${TIMEOUT}s" \
        --execution-environment="$EXECUTION_ENV" \
        --labels="scraper-type=$scraper_key,environment=prod,managed-by=manual" \
        $env_vars \
        --args="$cmd_args" \
        --quiet || {
        
        # If creation fails, try updating instead
        echo -e "${YELLOW}  Job exists, updating instead...${NC}"
        gcloud run jobs update "$scraper_name" \
            --image="$image_url" \
            --region="$REGION" \
            --service-account="$SERVICE_ACCOUNT" \
            --cpu="$CPU" \
            --memory="$MEMORY" \
            --max-retries=3 \
            --parallelism=1 \
            --task-count=1 \
            --task-timeout="${TIMEOUT}s" \
            --execution-environment="$EXECUTION_ENV" \
            $env_vars \
            --args="$cmd_args" \
            --quiet
    }
    
    echo -e "${GREEN}✅ $scraper_name deployed successfully${NC}"
    echo ""
}

# Function to deploy all jobs
deploy_all_jobs() {
    echo -e "${BLUE}🏗️ Deploying all Cloud Run jobs...${NC}"
    
    # Get list of scrapers from config
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        deploy_job "$scraper"
    done
}

# Function to verify deployments
verify_deployments() {
    echo -e "${BLUE}🔍 Verifying deployments...${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
        echo -e "  Checking $scraper_name..."
        
        if gcloud run jobs describe "$scraper_name" --region="$REGION" --quiet &>/dev/null; then
            echo -e "${GREEN}    ✅ $scraper_name deployed successfully${NC}"
        else
            echo -e "${RED}    ❌ $scraper_name deployment failed${NC}"
        fi
    done
}

# Function to display summary
display_summary() {
    echo -e "${GREEN}🎉 Cloud Run Jobs Deployment Summary${NC}"
    echo -e "${GREEN}====================================${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
        echo -e "${GREEN}✅ $scraper_name${NC}"
        echo -e "   Job URL: https://console.cloud.google.com/run/jobs/details/$REGION/$scraper_name"
    done
    
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "1. Test jobs manually: gcloud run jobs execute <job-name> --region=$REGION"
    echo -e "2. Setup Cloud Scheduler: ./scripts/setup-scheduler.sh"
}

# Main execution
main() {
    deploy_all_jobs
    verify_deployments
    display_summary
}

# Run main function
main "$@"
