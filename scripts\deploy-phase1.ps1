# PowerShell version of Phase 1 deployment script
# This script deploys all three scrapers as Cloud Run jobs with Cloud Scheduler

param(
    [switch]$SkipConfirmation
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput {
    param($Message, $Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$ConfigFile = Join-Path $ProjectRoot "config\scrapers-config.json"

Write-ColorOutput "🚀 Phase 1: Manual Deployment to Google Cloud Platform" $Blue
Write-ColorOutput "====================================================" $Blue

# Check if configuration file exists
if (-not (Test-Path $ConfigFile)) {
    Write-ColorOutput "❌ Error: Configuration file not found: $ConfigFile" $Red
    exit 1
}

# Load configuration
try {
    $Config = Get-Content $ConfigFile | ConvertFrom-Json
    $ProjectId = $Config.environment.project_id
    $Region = $Config.global.region
    $ArtifactRegistry = $Config.global.artifact_registry
    
    Write-ColorOutput "Project ID: $ProjectId" $Yellow
    Write-ColorOutput "Region: $Region" $Yellow
    Write-ColorOutput "Artifact Registry: $ArtifactRegistry" $Yellow
    Write-ColorOutput ""
} catch {
    Write-ColorOutput "❌ Error: Failed to parse configuration file: $_" $Red
    exit 1
}

# Function to check if user is authenticated
function Test-Authentication {
    Write-ColorOutput "🔐 Checking authentication..." $Blue
    $authList = gcloud auth list --filter=status:ACTIVE --format="value(account)" 2>$null
    if (-not $authList) {
        Write-ColorOutput "❌ Error: Not authenticated with gcloud. Please run 'gcloud auth login' first." $Red
        exit 1
    }
    Write-ColorOutput "✅ Authentication verified" $Green
}

# Function to set project
function Set-GCloudProject {
    Write-ColorOutput "📋 Setting project..." $Blue
    gcloud config set project $ProjectId
    Write-ColorOutput "✅ Project set to $ProjectId" $Green
}

# Function to enable required APIs
function Enable-RequiredAPIs {
    Write-ColorOutput "🔧 Enabling required APIs..." $Blue
    
    $apis = @(
        "run.googleapis.com",
        "cloudscheduler.googleapis.com",
        "artifactregistry.googleapis.com",
        "secretmanager.googleapis.com",
        "logging.googleapis.com",
        "monitoring.googleapis.com"
    )
    
    foreach ($api in $apis) {
        Write-ColorOutput "  Enabling $api..." $Blue
        gcloud services enable $api --quiet
    }
    
    Write-ColorOutput "✅ All APIs enabled" $Green
}

# Function to create Artifact Registry repository
function New-ArtifactRegistry {
    Write-ColorOutput "📦 Setting up Artifact Registry..." $Blue
    
    $repoName = "scrapers"
    
    # Check if repository exists
    $existingRepo = gcloud artifacts repositories describe $repoName --location=$Region 2>$null
    if ($existingRepo) {
        Write-ColorOutput "📋 Artifact Registry repository '$repoName' already exists" $Yellow
    } else {
        Write-ColorOutput "  Creating Artifact Registry repository..." $Blue
        gcloud artifacts repositories create $repoName `
            --repository-format=docker `
            --location=$Region `
            --description="Docker repository for scraper images"
        Write-ColorOutput "✅ Artifact Registry repository created" $Green
    }
}

# Function to create service accounts
function New-ServiceAccounts {
    Write-ColorOutput "👤 Creating service accounts..." $Blue
    
    $serviceAccounts = $Config.security.service_accounts
    
    foreach ($saProperty in $serviceAccounts.PSObject.Properties) {
        $saKey = $saProperty.Name
        $saConfig = $saProperty.Value
        $saName = $saConfig.name
        $saDisplayName = $saConfig.display_name
        $saDescription = $saConfig.description
        
        # Check if service account exists
        $existingSA = gcloud iam service-accounts describe "${saName}@${ProjectId}.iam.gserviceaccount.com" 2>$null
        if ($existingSA) {
            Write-ColorOutput "📋 Service account '$saName' already exists" $Yellow
        } else {
            Write-ColorOutput "  Creating service account: $saName..." $Blue
            gcloud iam service-accounts create $saName `
                --display-name="$saDisplayName" `
                --description="$saDescription"
            Write-ColorOutput "✅ Service account '$saName' created" $Green
        }
        
        # Assign roles
        foreach ($role in $saConfig.roles) {
            Write-ColorOutput "  Assigning role $role to $saName..." $Blue
            gcloud projects add-iam-policy-binding $ProjectId `
                --member="serviceAccount:${saName}@${ProjectId}.iam.gserviceaccount.com" `
                --role="$role" `
                --quiet
        }
    }
    
    Write-ColorOutput "✅ Service accounts configured" $Green
}

# Function to create secrets
function New-Secrets {
    Write-ColorOutput "🔐 Creating secrets..." $Blue
    
    foreach ($secret in $Config.security.secrets) {
        $secretName = $secret.name
        $secretDescription = $secret.description
        
        # Check if secret exists
        $existingSecret = gcloud secrets describe $secretName 2>$null
        if ($existingSecret) {
            Write-ColorOutput "📋 Secret '$secretName' already exists" $Yellow
        } else {
            Write-ColorOutput "  Creating secret: $secretName..." $Blue
            gcloud secrets create $secretName `
                --replication-policy="automatic" `
                --labels="environment=prod,managed-by=manual"
            Write-ColorOutput "✅ Secret '$secretName' created" $Green
            Write-ColorOutput "⚠️  Please set the secret value manually:" $Yellow
            Write-ColorOutput "    gcloud secrets versions add $secretName --data-file=<path-to-secret-file>" $Yellow
        }
    }
    
    Write-ColorOutput "✅ Secrets configured" $Green
}

# Main execution
function Main {
    if (-not $SkipConfirmation) {
        Write-ColorOutput "This will deploy scrapers to Google Cloud Platform." $Yellow
        Write-ColorOutput "Project: $ProjectId" $Yellow
        Write-ColorOutput "Region: $Region" $Yellow
        Write-ColorOutput ""
        $confirmation = Read-Host "Do you want to continue? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-ColorOutput "Deployment cancelled." $Yellow
            exit 0
        }
    }
    
    Test-Authentication
    Set-GCloudProject
    Enable-RequiredAPIs
    New-ArtifactRegistry
    New-ServiceAccounts
    New-Secrets
    
    Write-ColorOutput ""
    Write-ColorOutput "🎉 Phase 1 infrastructure setup completed!" $Green
    Write-ColorOutput ""
    Write-ColorOutput "Next steps:" $Blue
    Write-ColorOutput "1. Set secret values using: gcloud secrets versions add <secret-name> --data-file=<file>" $Blue
    Write-ColorOutput "2. Build and push Docker images using: .\scripts\build-and-push.ps1" $Blue
    Write-ColorOutput "3. Deploy Cloud Run jobs using: .\scripts\deploy-jobs.ps1" $Blue
    Write-ColorOutput "4. Setup Cloud Scheduler using: .\scripts\setup-scheduler.ps1" $Blue
}

# Run main function
Main
