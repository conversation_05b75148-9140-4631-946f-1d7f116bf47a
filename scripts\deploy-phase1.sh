#!/bin/bash

# Phase 1: Manual Deployment Script for Scrapers to Google Cloud Platform
# This script deploys all three scrapers as Cloud Run jobs with Cloud Scheduler

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/scrapers-config.json"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ Error: jq is required but not installed. Please install jq first.${NC}"
    exit 1
fi

# Load configuration
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}❌ Error: Configuration file not found: $CONFIG_FILE${NC}"
    exit 1
fi

# Extract configuration values
PROJECT_ID=$(jq -r '.environment.project_id' "$CONFIG_FILE")
REGION=$(jq -r '.global.region' "$CONFIG_FILE")
ARTIFACT_REGISTRY=$(jq -r '.global.artifact_registry' "$CONFIG_FILE")

echo -e "${BLUE}🚀 Phase 1: Manual Deployment to Google Cloud Platform${NC}"
echo -e "${BLUE}====================================================${NC}"
echo -e "Project ID: ${YELLOW}$PROJECT_ID${NC}"
echo -e "Region: ${YELLOW}$REGION${NC}"
echo -e "Artifact Registry: ${YELLOW}$ARTIFACT_REGISTRY${NC}"
echo ""

# Function to check if user is authenticated
check_authentication() {
    echo -e "${BLUE}🔐 Checking authentication...${NC}"
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo -e "${RED}❌ Error: Not authenticated with gcloud. Please run 'gcloud auth login' first.${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Authentication verified${NC}"
}

# Function to set project
set_project() {
    echo -e "${BLUE}📋 Setting project...${NC}"
    gcloud config set project "$PROJECT_ID"
    echo -e "${GREEN}✅ Project set to $PROJECT_ID${NC}"
}

# Function to enable required APIs
enable_apis() {
    echo -e "${BLUE}🔧 Enabling required APIs...${NC}"
    
    local apis=(
        "run.googleapis.com"
        "cloudscheduler.googleapis.com"
        "artifactregistry.googleapis.com"
        "secretmanager.googleapis.com"
        "logging.googleapis.com"
        "monitoring.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        echo -e "  Enabling $api..."
        gcloud services enable "$api" --quiet
    done
    
    echo -e "${GREEN}✅ All APIs enabled${NC}"
}

# Function to create Artifact Registry repository
create_artifact_registry() {
    echo -e "${BLUE}📦 Setting up Artifact Registry...${NC}"
    
    local repo_name="scrapers"
    
    # Check if repository exists
    if gcloud artifacts repositories describe "$repo_name" --location="$REGION" &>/dev/null; then
        echo -e "${YELLOW}📋 Artifact Registry repository '$repo_name' already exists${NC}"
    else
        echo -e "  Creating Artifact Registry repository..."
        gcloud artifacts repositories create "$repo_name" \
            --repository-format=docker \
            --location="$REGION" \
            --description="Docker repository for scraper images"
        echo -e "${GREEN}✅ Artifact Registry repository created${NC}"
    fi
}

# Function to create service accounts
create_service_accounts() {
    echo -e "${BLUE}👤 Creating service accounts...${NC}"
    
    # Get service accounts from config
    local service_accounts=$(jq -r '.security.service_accounts | keys[]' "$CONFIG_FILE")
    
    for sa_key in $service_accounts; do
        local sa_name=$(jq -r ".security.service_accounts.$sa_key.name" "$CONFIG_FILE")
        local sa_display_name=$(jq -r ".security.service_accounts.$sa_key.display_name" "$CONFIG_FILE")
        local sa_description=$(jq -r ".security.service_accounts.$sa_key.description" "$CONFIG_FILE")
        
        # Check if service account exists
        if gcloud iam service-accounts describe "${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com" &>/dev/null; then
            echo -e "${YELLOW}📋 Service account '$sa_name' already exists${NC}"
        else
            echo -e "  Creating service account: $sa_name..."
            gcloud iam service-accounts create "$sa_name" \
                --display-name="$sa_display_name" \
                --description="$sa_description"
            echo -e "${GREEN}✅ Service account '$sa_name' created${NC}"
        fi
        
        # Assign roles
        local roles=$(jq -r ".security.service_accounts.$sa_key.roles[]" "$CONFIG_FILE")
        for role in $roles; do
            echo -e "  Assigning role $role to $sa_name..."
            gcloud projects add-iam-policy-binding "$PROJECT_ID" \
                --member="serviceAccount:${sa_name}@${PROJECT_ID}.iam.gserviceaccount.com" \
                --role="$role" \
                --quiet
        done
    done
    
    echo -e "${GREEN}✅ Service accounts configured${NC}"
}

# Function to create secrets
create_secrets() {
    echo -e "${BLUE}🔐 Creating secrets...${NC}"
    
    local secrets=$(jq -r '.security.secrets[].name' "$CONFIG_FILE")
    
    for secret_name in $secrets; do
        local secret_description=$(jq -r ".security.secrets[] | select(.name==\"$secret_name\") | .description" "$CONFIG_FILE")
        
        # Check if secret exists
        if gcloud secrets describe "$secret_name" &>/dev/null; then
            echo -e "${YELLOW}📋 Secret '$secret_name' already exists${NC}"
        else
            echo -e "  Creating secret: $secret_name..."
            gcloud secrets create "$secret_name" \
                --replication-policy="automatic" \
                --labels="environment=prod,managed-by=manual"
            echo -e "${GREEN}✅ Secret '$secret_name' created${NC}"
            echo -e "${YELLOW}⚠️  Please set the secret value manually:${NC}"
            echo -e "    gcloud secrets versions add $secret_name --data-file=<path-to-secret-file>"
        fi
    done
    
    echo -e "${GREEN}✅ Secrets configured${NC}"
}

# Main execution
main() {
    check_authentication
    set_project
    enable_apis
    create_artifact_registry
    create_service_accounts
    create_secrets
    
    echo ""
    echo -e "${GREEN}🎉 Phase 1 infrastructure setup completed!${NC}"
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "1. Set secret values using: gcloud secrets versions add <secret-name> --data-file=<file>"
    echo -e "2. Build and push Docker images using: ./scripts/build-and-push.sh"
    echo -e "3. Deploy Cloud Run jobs using: ./scripts/deploy-jobs.sh"
    echo -e "4. Setup Cloud Scheduler using: ./scripts/setup-scheduler.sh"
}

# Run main function
main "$@"
