#!/bin/bash

# Fix CI/CD Issues Script
# This script addresses common CI/CD pipeline failures

set -e

PROJECT_ID="webscrapers-463817"
REGION="asia-south1"
REPOSITORY_NAME="scrapers"

echo "🔧 Fixing CI/CD Pipeline Issues..."

# 1. Check if Artifact Registry repository exists
echo "📦 Checking Artifact Registry repository..."
if ! gcloud artifacts repositories describe $REPOSITORY_NAME \
    --location=$REGION \
    --project=$PROJECT_ID \
    --quiet >/dev/null 2>&1; then
    
    echo "Creating Artifact Registry repository: $REPOSITORY_NAME"
    gcloud artifacts repositories create $REPOSITORY_NAME \
        --repository-format=docker \
        --location=$REGION \
        --description="Docker repository for scraper images" \
        --project=$PROJECT_ID
    
    echo "✅ Artifact Registry repository created"
else
    echo "✅ Artifact Registry repository already exists"
fi

# 2. Check if required service accounts exist
echo "👤 Checking service accounts..."

# GitHub Actions service account
GITHUB_SA="github-actions-deployer@${PROJECT_ID}.iam.gserviceaccount.com"
if ! gcloud iam service-accounts describe $GITHUB_SA --project=$PROJECT_ID --quiet >/dev/null 2>&1; then
    echo "Creating GitHub Actions service account..."
    gcloud iam service-accounts create github-actions-deployer \
        --display-name="GitHub Actions Deployer" \
        --description="Service account for GitHub Actions CI/CD pipeline" \
        --project=$PROJECT_ID
    
    # Assign required roles
    ROLES=(
        "roles/run.admin"
        "roles/artifactregistry.admin"
        "roles/cloudscheduler.admin"
        "roles/iam.serviceAccountUser"
        "roles/secretmanager.admin"
    )
    
    for role in "${ROLES[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$GITHUB_SA" \
            --role="$role" \
            --quiet
    done
    
    echo "✅ GitHub Actions service account created and configured"
else
    echo "✅ GitHub Actions service account already exists"
fi

# Scraper runner service account
SCRAPER_SA="scraper-runner@${PROJECT_ID}.iam.gserviceaccount.com"
if ! gcloud iam service-accounts describe $SCRAPER_SA --project=$PROJECT_ID --quiet >/dev/null 2>&1; then
    echo "Creating scraper runner service account..."
    gcloud iam service-accounts create scraper-runner \
        --display-name="Scraper Runner" \
        --description="Service account for running scraper Cloud Run jobs" \
        --project=$PROJECT_ID
    
    # Assign required roles
    SCRAPER_ROLES=(
        "roles/run.invoker"
        "roles/secretmanager.secretAccessor"
        "roles/logging.logWriter"
        "roles/monitoring.metricWriter"
    )
    
    for role in "${SCRAPER_ROLES[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$SCRAPER_SA" \
            --role="$role" \
            --quiet
    done
    
    echo "✅ Scraper runner service account created and configured"
else
    echo "✅ Scraper runner service account already exists"
fi

# Scheduler service account
SCHEDULER_SA="scraper-scheduler@${PROJECT_ID}.iam.gserviceaccount.com"
if ! gcloud iam service-accounts describe $SCHEDULER_SA --project=$PROJECT_ID --quiet >/dev/null 2>&1; then
    echo "Creating scheduler service account..."
    gcloud iam service-accounts create scraper-scheduler \
        --display-name="Scraper Scheduler" \
        --description="Service account for Cloud Scheduler to invoke jobs" \
        --project=$PROJECT_ID
    
    # Assign required roles
    SCHEDULER_ROLES=(
        "roles/run.invoker"
        "roles/cloudscheduler.jobRunner"
    )
    
    for role in "${SCHEDULER_ROLES[@]}"; do
        gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="serviceAccount:$SCHEDULER_SA" \
            --role="$role" \
            --quiet
    done
    
    echo "✅ Scheduler service account created and configured"
else
    echo "✅ Scheduler service account already exists"
fi

# 3. Check if required APIs are enabled
echo "🔌 Checking required APIs..."
REQUIRED_APIS=(
    "run.googleapis.com"
    "artifactregistry.googleapis.com"
    "cloudscheduler.googleapis.com"
    "secretmanager.googleapis.com"
    "logging.googleapis.com"
    "monitoring.googleapis.com"
)

for api in "${REQUIRED_APIS[@]}"; do
    if ! gcloud services list --enabled --filter="name:$api" --format="value(name)" --project=$PROJECT_ID | grep -q "$api"; then
        echo "Enabling API: $api"
        gcloud services enable $api --project=$PROJECT_ID
    else
        echo "✅ API already enabled: $api"
    fi
done

# 4. Check if secrets exist in Secret Manager
echo "🔐 Checking secrets in Secret Manager..."
REQUIRED_SECRETS=("supabase-anon-key" "supabase-service-key")

for secret in "${REQUIRED_SECRETS[@]}"; do
    if ! gcloud secrets describe $secret --project=$PROJECT_ID --quiet >/dev/null 2>&1; then
        echo "⚠️  Secret missing: $secret"
        echo "   Please create this secret manually with:"
        echo "   gcloud secrets create $secret --data-file=- --project=$PROJECT_ID"
        echo "   (Then paste the secret value and press Ctrl+D)"
    else
        echo "✅ Secret exists: $secret"
    fi
done

# 5. Generate GitHub Actions service account key
echo "🔑 Generating GitHub Actions service account key..."
KEY_FILE="github-actions-key.json"
if [[ -f "$KEY_FILE" ]]; then
    rm "$KEY_FILE"
fi

gcloud iam service-accounts keys create "$KEY_FILE" \
    --iam-account="$GITHUB_SA" \
    --project=$PROJECT_ID

echo "✅ Service account key generated: $KEY_FILE"
echo ""
echo "🎯 Next Steps:"
echo "1. Add the following GitHub secrets to your repository:"
echo "   - GCP_PROJECT_ID: $PROJECT_ID"
echo "   - GCP_SA_KEY: (contents of $KEY_FILE)"
echo ""
echo "2. If any secrets are missing, create them with:"
echo "   gcloud secrets create SECRET_NAME --data-file=- --project=$PROJECT_ID"
echo ""
echo "3. Test the CI/CD pipeline by pushing changes or manually triggering the workflow"
echo ""
echo "🔧 CI/CD Issues Fixed! ✅"
