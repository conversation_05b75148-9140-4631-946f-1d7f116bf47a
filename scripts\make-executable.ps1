# PowerShell script to make shell scripts executable on Windows/WSL
# This script sets the executable bit for all shell scripts

Write-Host "Making shell scripts executable..." -ForegroundColor Blue

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$shellScripts = Get-ChildItem -Path $scriptDir -Filter "*.sh"

foreach ($script in $shellScripts) {
    Write-Host "Setting executable permissions for: $($script.Name)" -ForegroundColor Green
    
    # For WSL/Linux subsystem
    if (Get-Command wsl -ErrorAction SilentlyContinue) {
        wsl chmod +x $script.FullName
    }
    
    # For Git Bash (if available)
    if (Get-Command bash -ErrorAction SilentlyContinue) {
        bash -c "chmod +x '$($script.FullName)'"
    }
}

Write-Host "Done! All shell scripts are now executable." -ForegroundColor Green
Write-Host ""
Write-Host "Available deployment scripts:" -ForegroundColor Yellow
Write-Host "  1. deploy-phase1.sh     - Infrastructure setup"
Write-Host "  2. build-and-push.sh    - Build and push Docker images"
Write-Host "  3. deploy-jobs.sh       - Deploy Cloud Run jobs"
Write-Host "  4. setup-scheduler.sh   - Configure Cloud Scheduler"
Write-Host ""
Write-Host "For detailed instructions, see:" -ForegroundColor Cyan
Write-Host "  - scripts/README.md"
Write-Host "  - docs/DEPLOYMENT_GUIDE.md"
