#!/usr/bin/env python3
"""
PDF Duplicate Processing Script
Processes pending PDF hash computations and updates duplicate flags.
"""

import asyncio
import argparse
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from shared.duplicate_detection_service import DuplicateDetectionService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pdf_duplicate_processing.log')
    ]
)
logger = logging.getLogger(__name__)

class PDFDuplicateProcessor:
    """Main processor for PDF duplicate detection"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """Initialize the processor"""
        self.service = DuplicateDetectionService(supabase_url, supabase_key)
    
    async def run_full_processing(self, batch_size: int = 50, max_concurrent: int = 5) -> Dict[str, Any]:
        """
        Run complete duplicate processing workflow
        
        Args:
            batch_size: Number of PDFs to process per batch
            max_concurrent: Maximum concurrent downloads
            
        Returns:
            Processing statistics
        """
        logger.info("Starting PDF duplicate processing workflow")
        start_time = datetime.utcnow()
        
        total_stats = {
            'batches_processed': 0,
            'total_processed': 0,
            'successful_hashes': 0,
            'failed_hashes': 0,
            'duplicates_found': 0,
            'bse_updated': 0,
            'nse_updated': 0,
            'processing_time_seconds': 0
        }
        
        try:
            # Process in batches until no more pending records
            batch_num = 0
            while True:
                batch_num += 1
                logger.info(f"Processing batch {batch_num}")
                
                batch_stats = await self.service.process_pending_hashes(
                    batch_size=batch_size,
                    max_concurrent=max_concurrent
                )
                
                # Update total stats
                total_stats['batches_processed'] += 1
                total_stats['total_processed'] += batch_stats['total_processed']
                total_stats['successful_hashes'] += batch_stats['successful_hashes']
                total_stats['failed_hashes'] += batch_stats['failed_hashes']
                total_stats['duplicates_found'] += batch_stats['duplicates_found']
                total_stats['bse_updated'] += batch_stats['bse_updated']
                total_stats['nse_updated'] += batch_stats['nse_updated']
                
                # Break if no records were processed
                if batch_stats['total_processed'] == 0:
                    logger.info("No more pending records to process")
                    break
                
                # Log batch completion
                logger.info(f"Batch {batch_num} complete: {batch_stats}")
                
                # Small delay between batches to avoid overwhelming the system
                await asyncio.sleep(1)
            
            # Calculate processing time
            end_time = datetime.utcnow()
            total_stats['processing_time_seconds'] = (end_time - start_time).total_seconds()
            
            logger.info(f"Processing workflow complete: {total_stats}")
            return total_stats
            
        except Exception as e:
            logger.error(f"Error in processing workflow: {e}")
            end_time = datetime.utcnow()
            total_stats['processing_time_seconds'] = (end_time - start_time).total_seconds()
            return total_stats
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get current duplicate detection statistics"""
        return self.service.get_duplicate_statistics()
    
    def cleanup_failed(self, max_age_hours: int = 24) -> int:
        """Cleanup failed computations"""
        return self.service.cleanup_failed_computations(max_age_hours)
    
    def print_statistics(self, stats: Dict[str, Any]):
        """Print formatted statistics"""
        print("\n" + "="*80)
        print("PDF DUPLICATE DETECTION STATISTICS")
        print("="*80)
        
        for table_name, table_stats in stats.items():
            if table_name == 'pdf_registry':
                print(f"\n📁 PDF Registry:")
                print(f"   Total PDFs tracked: {table_stats['total_pdfs']:,}")
                print(f"   Status breakdown:")
                for status, count in table_stats['status_breakdown'].items():
                    print(f"     {status}: {count:,}")
            else:
                print(f"\n📊 {table_name.replace('_', ' ').title()}:")
                print(f"   Total records: {table_stats['total_records']:,}")
                print(f"   Records with PDFs: {table_stats['records_with_pdfs']:,}")
                print(f"   Duplicate records: {table_stats['duplicate_records']:,}")
                print(f"   Unique PDFs: {table_stats['unique_pdfs']:,}")
                print(f"   Duplicate percentage: {table_stats['duplicate_percentage']:.2f}%")
        
        print("\n" + "="*80)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Process PDF duplicates for corporate announcements')
    parser.add_argument('--supabase-url', required=True, help='Supabase project URL')
    parser.add_argument('--supabase-key', required=True, help='Supabase API key')
    parser.add_argument('--batch-size', type=int, default=50, help='Batch size for processing')
    parser.add_argument('--max-concurrent', type=int, default=5, help='Maximum concurrent downloads')
    parser.add_argument('--stats-only', action='store_true', help='Only show statistics, do not process')
    parser.add_argument('--cleanup-failed', type=int, help='Cleanup failed computations older than N hours')
    
    args = parser.parse_args()
    
    # Create processor
    processor = PDFDuplicateProcessor(args.supabase_url, args.supabase_key)
    
    try:
        if args.stats_only:
            # Show statistics only
            logger.info("Fetching duplicate detection statistics...")
            stats = processor.get_statistics()
            processor.print_statistics(stats)
            
        elif args.cleanup_failed:
            # Cleanup failed computations
            logger.info(f"Cleaning up failed computations older than {args.cleanup_failed} hours...")
            reset_count = processor.cleanup_failed(args.cleanup_failed)
            print(f"Reset {reset_count} failed computations")
            
        else:
            # Run full processing
            logger.info("Starting full duplicate processing...")
            
            # Show initial statistics
            print("Initial statistics:")
            initial_stats = processor.get_statistics()
            processor.print_statistics(initial_stats)
            
            # Run processing
            processing_stats = asyncio.run(processor.run_full_processing(
                batch_size=args.batch_size,
                max_concurrent=args.max_concurrent
            ))
            
            # Show final statistics
            print("\nFinal statistics:")
            final_stats = processor.get_statistics()
            processor.print_statistics(final_stats)
            
            # Show processing summary
            print("\n" + "="*80)
            print("PROCESSING SUMMARY")
            print("="*80)
            print(f"Batches processed: {processing_stats['batches_processed']}")
            print(f"Total PDFs processed: {processing_stats['total_processed']:,}")
            print(f"Successful hash computations: {processing_stats['successful_hashes']:,}")
            print(f"Failed hash computations: {processing_stats['failed_hashes']:,}")
            print(f"Duplicates found: {processing_stats['duplicates_found']:,}")
            print(f"BSE records updated: {processing_stats['bse_updated']:,}")
            print(f"NSE records updated: {processing_stats['nse_updated']:,}")
            print(f"Processing time: {processing_stats['processing_time_seconds']:.2f} seconds")
            
            success_rate = (processing_stats['successful_hashes'] / processing_stats['total_processed'] * 100) if processing_stats['total_processed'] > 0 else 0
            print(f"Success rate: {success_rate:.2f}%")
            print("="*80)
            
    except KeyboardInterrupt:
        logger.info("Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

# Example usage:
# python scripts/process_pdf_duplicates.py \
#   --supabase-url "https://yvuwseolouiqhoxsieop.supabase.co" \
#   --supabase-key "your-key-here" \
#   --batch-size 50 \
#   --max-concurrent 5
#
# python scripts/process_pdf_duplicates.py \
#   --supabase-url "https://yvuwseolouiqhoxsieop.supabase.co" \
#   --supabase-key "your-key-here" \
#   --stats-only
#
# python scripts/process_pdf_duplicates.py \
#   --supabase-url "https://yvuwseolouiqhoxsieop.supabase.co" \
#   --supabase-key "your-key-here" \
#   --cleanup-failed 24
