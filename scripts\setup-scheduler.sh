#!/bin/bash

# Setup Cloud Scheduler for Scrapers
# This script creates Cloud Scheduler jobs to trigger Cloud Run jobs on schedule

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/scrapers-config.json"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ Error: jq is required but not installed. Please install jq first.${NC}"
    exit 1
fi

# Load configuration
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo -e "${RED}❌ Error: Configuration file not found: $CONFIG_FILE${NC}"
    exit 1
fi

# Extract configuration values
PROJECT_ID=$(jq -r '.environment.project_id' "$CONFIG_FILE")
REGION=$(jq -r '.global.region' "$CONFIG_FILE")
TIMEZONE=$(jq -r '.environment.cloud_scheduler.timezone' "$CONFIG_FILE")

# Service account for scheduler
SCHEDULER_SA_NAME=$(jq -r '.security.service_accounts.scheduler.name' "$CONFIG_FILE")
SCHEDULER_SA="${SCHEDULER_SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# Retry configuration
RETRY_COUNT=$(jq -r '.environment.cloud_scheduler.retry_config.retry_count' "$CONFIG_FILE")
MAX_RETRY_DURATION=$(jq -r '.environment.cloud_scheduler.retry_config.max_retry_duration' "$CONFIG_FILE")
MIN_BACKOFF_DURATION=$(jq -r '.environment.cloud_scheduler.retry_config.min_backoff_duration' "$CONFIG_FILE")
MAX_BACKOFF_DURATION=$(jq -r '.environment.cloud_scheduler.retry_config.max_backoff_duration' "$CONFIG_FILE")
MAX_DOUBLINGS=$(jq -r '.environment.cloud_scheduler.retry_config.max_doublings' "$CONFIG_FILE")

echo -e "${BLUE}⏰ Setting up Cloud Scheduler${NC}"
echo -e "${BLUE}=============================${NC}"
echo -e "Project ID: ${YELLOW}$PROJECT_ID${NC}"
echo -e "Region: ${YELLOW}$REGION${NC}"
echo -e "Timezone: ${YELLOW}$TIMEZONE${NC}"
echo -e "Scheduler Service Account: ${YELLOW}$SCHEDULER_SA${NC}"
echo ""

# Function to create a scheduler job
create_scheduler_job() {
    local scraper_key=$1
    local scraper_name=$(jq -r ".scrapers.$scraper_key.name" "$CONFIG_FILE")
    local cron_schedule=$(jq -r ".scrapers.$scraper_key.schedule.cron" "$CONFIG_FILE")
    local schedule_enabled=$(jq -r ".scrapers.$scraper_key.schedule.enabled" "$CONFIG_FILE")
    local schedule_description=$(jq -r ".scrapers.$scraper_key.schedule.description" "$CONFIG_FILE")
    
    if [[ "$schedule_enabled" != "true" ]]; then
        echo -e "${YELLOW}⏭️ Skipping $scraper_name (schedule disabled)${NC}"
        return 0
    fi
    
    echo -e "${BLUE}⏰ Creating scheduler job for $scraper_name...${NC}"
    
    local scheduler_job_name="${scraper_name}-scheduler"
    local cloud_run_job_url="https://${REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${PROJECT_ID}/jobs/${scraper_name}:run"
    
    # Check if scheduler job already exists
    if gcloud scheduler jobs describe "$scheduler_job_name" --location="$REGION" &>/dev/null; then
        echo -e "${YELLOW}📋 Scheduler job '$scheduler_job_name' already exists, updating...${NC}"
        
        gcloud scheduler jobs update http "$scheduler_job_name" \
            --location="$REGION" \
            --schedule="$cron_schedule" \
            --time-zone="$TIMEZONE" \
            --uri="$cloud_run_job_url" \
            --http-method=POST \
            --oidc-service-account-email="$SCHEDULER_SA" \
            --oidc-token-audience="$cloud_run_job_url" \
            --max-retry-attempts="$RETRY_COUNT" \
            --max-retry-duration="$MAX_RETRY_DURATION" \
            --min-backoff-duration="$MIN_BACKOFF_DURATION" \
            --max-backoff-duration="$MAX_BACKOFF_DURATION" \
            --max-doublings="$MAX_DOUBLINGS" \
            --description="$schedule_description" \
            --quiet
    else
        echo -e "  Creating new scheduler job: $scheduler_job_name"
        
        gcloud scheduler jobs create http "$scheduler_job_name" \
            --location="$REGION" \
            --schedule="$cron_schedule" \
            --time-zone="$TIMEZONE" \
            --uri="$cloud_run_job_url" \
            --http-method=POST \
            --oidc-service-account-email="$SCHEDULER_SA" \
            --oidc-token-audience="$cloud_run_job_url" \
            --max-retry-attempts="$RETRY_COUNT" \
            --max-retry-duration="$MAX_RETRY_DURATION" \
            --min-backoff-duration="$MIN_BACKOFF_DURATION" \
            --max-backoff-duration="$MAX_BACKOFF_DURATION" \
            --max-doublings="$MAX_DOUBLINGS" \
            --description="$schedule_description" \
            --quiet
    fi
    
    echo -e "${GREEN}✅ Scheduler job '$scheduler_job_name' configured${NC}"
    echo -e "   Schedule: $cron_schedule ($TIMEZONE)"
    echo -e "   Target: $scraper_name"
    echo ""
}

# Function to create all scheduler jobs
create_all_scheduler_jobs() {
    echo -e "${BLUE}🏗️ Creating all scheduler jobs...${NC}"
    
    # Get list of scrapers from config
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        create_scheduler_job "$scraper"
    done
}

# Function to verify scheduler jobs
verify_scheduler_jobs() {
    echo -e "${BLUE}🔍 Verifying scheduler jobs...${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
        local scheduler_job_name="${scraper_name}-scheduler"
        local schedule_enabled=$(jq -r ".scrapers.$scraper.schedule.enabled" "$CONFIG_FILE")
        
        if [[ "$schedule_enabled" != "true" ]]; then
            continue
        fi
        
        echo -e "  Checking $scheduler_job_name..."
        
        if gcloud scheduler jobs describe "$scheduler_job_name" --location="$REGION" --quiet &>/dev/null; then
            local state=$(gcloud scheduler jobs describe "$scheduler_job_name" --location="$REGION" --format="value(state)")
            echo -e "${GREEN}    ✅ $scheduler_job_name ($state)${NC}"
        else
            echo -e "${RED}    ❌ $scheduler_job_name not found${NC}"
        fi
    done
}

# Function to test scheduler jobs (optional)
test_scheduler_jobs() {
    echo -e "${BLUE}🧪 Testing scheduler jobs (optional)...${NC}"
    read -p "Do you want to test the scheduler jobs now? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
        
        for scraper in $scrapers; do
            local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
            local scheduler_job_name="${scraper_name}-scheduler"
            local schedule_enabled=$(jq -r ".scrapers.$scraper.schedule.enabled" "$CONFIG_FILE")
            
            if [[ "$schedule_enabled" != "true" ]]; then
                continue
            fi
            
            echo -e "${BLUE}  Testing $scheduler_job_name...${NC}"
            gcloud scheduler jobs run "$scheduler_job_name" --location="$REGION" --quiet
            echo -e "${GREEN}  ✅ Test job triggered for $scheduler_job_name${NC}"
        done
        
        echo ""
        echo -e "${YELLOW}⚠️ Check the Cloud Run jobs execution in the console:${NC}"
        echo -e "https://console.cloud.google.com/run/jobs?project=$PROJECT_ID"
    else
        echo -e "${BLUE}Skipping scheduler job tests${NC}"
    fi
}

# Function to display summary
display_summary() {
    echo -e "${GREEN}🎉 Cloud Scheduler Setup Summary${NC}"
    echo -e "${GREEN}================================${NC}"
    
    local scrapers=$(jq -r '.scrapers | keys[]' "$CONFIG_FILE")
    
    for scraper in $scrapers; do
        local scraper_name=$(jq -r ".scrapers.$scraper.name" "$CONFIG_FILE")
        local cron_schedule=$(jq -r ".scrapers.$scraper.schedule.cron" "$CONFIG_FILE")
        local schedule_enabled=$(jq -r ".scrapers.$scraper.schedule.enabled" "$CONFIG_FILE")
        local scheduler_job_name="${scraper_name}-scheduler"
        
        if [[ "$schedule_enabled" == "true" ]]; then
            echo -e "${GREEN}✅ $scraper_name${NC}"
            echo -e "   Schedule: $cron_schedule ($TIMEZONE)"
            echo -e "   Scheduler: $scheduler_job_name"
        else
            echo -e "${YELLOW}⏸️ $scraper_name (disabled)${NC}"
        fi
    done
    
    echo ""
    echo -e "${BLUE}Management URLs:${NC}"
    echo -e "Cloud Scheduler: https://console.cloud.google.com/cloudscheduler?project=$PROJECT_ID"
    echo -e "Cloud Run Jobs: https://console.cloud.google.com/run/jobs?project=$PROJECT_ID"
    echo -e "Cloud Logging: https://console.cloud.google.com/logs/query?project=$PROJECT_ID"
    
    echo ""
    echo -e "${BLUE}Next steps:${NC}"
    echo -e "1. Monitor job executions in Cloud Console"
    echo -e "2. Check logs for any issues"
    echo -e "3. Adjust schedules if needed using: gcloud scheduler jobs update"
}

# Main execution
main() {
    create_all_scheduler_jobs
    verify_scheduler_jobs
    test_scheduler_jobs
    display_summary
}

# Run main function
main "$@"
