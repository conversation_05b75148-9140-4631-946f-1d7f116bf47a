#!/usr/bin/env python3
"""
SQL-based database cleanup script to convert string representations of null values to actual NULL values.
This script uses direct SQL queries for more efficient bulk updates.
"""

import sys
import os
import requests
import json
from typing import List, Dict, Any

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.base_config import BaseConfig


class SQLDatabaseCleanup:
    """SQL-based cleanup for null-like string values in the database"""
    
    def __init__(self):
        """Initialize database cleanup with Supabase configuration"""
        self.supabase_url = BaseConfig.SUPABASE_URL
        self.supabase_key = BaseConfig.SUPABASE_ANON_KEY
        
        # Define null-like values that should be converted to NULL
        self.null_values = [
            'null', 'nil', 'NULL', 'NIL', 'NONE', 'None',
            'N/A', 'n/a', 'NA', 'na', '#N/A', '#NA', 
            'NaN', 'NAN', 'nan', 'undefined', 'UNDEFINED', 
            'empty', 'EMPTY', '-'
        ]
        
        # Define tables and their text columns to clean
        self.tables_config = {
            'nse_insider_trading': [
                'symbol', 'company', 'regulation', 'name_of_the_acquirer_disposer',
                'category_of_person', 'type_of_security_prior', 'no_of_security_prior',
                'shareholding_prior', 'type_of_security_acquired_displosed',
                'no_of_securities_acquired_displosed', 'value_of_security_acquired_displosed',
                'acquisition_disposal_transaction_type', 'type_of_security_post',
                'no_of_security_post', 'post_percentage', 'date_of_allotment_acquisition_from',
                'date_of_allotment_acquisition_to', 'date_of_intimation_to_company',
                'mode_of_acquisition', 'derivative_type_security', 'derivative_contract_specification',
                'notional_value_buy', 'number_of_units_contract_lot_size_buy',
                'notional_value_sell', 'number_of_units_contract_lot_size_sell',
                'exchange', 'remark', 'broadcast_date_and_time', 'xbrl'
            ],
            'nse_corporate_announcements': [
                'symbol', 'company_name', 'subject', 'details', 'attachment_url', 'file_size'
            ],
            'bse_insider_trading': [
                'security_code', 'security_name', 'name_of_person', 'category_of_person',
                'securities_held_pre_transaction', 'securities_acquired_disposed_type',
                'securities_acquired_disposed_number', 'securities_acquired_disposed_value',
                'securities_acquired_disposed_transaction_type', 'securities_held_post_transaction',
                'period', 'mode_of_acquisition', 'trading_derivatives_type_contract',
                'trading_derivatives_buy_value', 'trading_derivatives_sale_value',
                'reported_to_exchange'
            ]
        }
    
    def execute_sql_query(self, query: str) -> Dict[str, Any]:
        """
        Execute a SQL query using Supabase database API.
        
        Args:
            query (str): SQL query to execute
            
        Returns:
            Dict[str, Any]: Query result or error information
        """
        try:
            url = f"{self.supabase_url.replace('/rest/v1', '')}/rest/v1/rpc/exec_sql"
            headers = {
                'apikey': self.supabase_key,
                'Authorization': f'Bearer {self.supabase_key}',
                'Content-Type': 'application/json'
            }
            
            # Try using the database query endpoint instead
            url = f"{self.supabase_url.replace('supabase.co', 'supabase.co')}/rest/v1/rpc/query"
            
            response = requests.post(
                url,
                headers=headers,
                data=json.dumps({'query': query}),
                timeout=60
            )
            
            if response.status_code == 200:
                return {'success': True, 'data': response.json()}
            else:
                return {'success': False, 'error': f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def check_null_values_sql(self, table_name: str, columns: List[str]) -> Dict[str, int]:
        """
        Check for null-like string values using SQL query.
        
        Args:
            table_name (str): Name of the table to check
            columns (List[str]): List of columns to check
            
        Returns:
            Dict[str, int]: Dictionary with column names and count of null-like values
        """
        print(f"\n🔍 Checking table '{table_name}' for null-like string values...")
        
        results = {}
        
        # Create conditions for all null-like values
        null_conditions = " OR ".join([f"'{value}'" for value in self.null_values])
        
        for column in columns:
            try:
                query = f"""
                SELECT COUNT(*) as count 
                FROM {table_name} 
                WHERE {column} IN ({null_conditions})
                """
                
                result = self.execute_sql_query(query)
                
                if result['success'] and result['data']:
                    count = result['data'][0].get('count', 0)
                    if count > 0:
                        results[column] = count
                        print(f"   📊 Column '{column}': {count} null-like values found")
                else:
                    print(f"   ⚠️  Could not check column '{column}': {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ Error checking column '{column}': {e}")
        
        if not results:
            print(f"   ✅ No null-like string values found in table '{table_name}'")
        
        return results
    
    def cleanup_table_sql(self, table_name: str, columns: List[str]) -> Dict[str, Any]:
        """
        Clean up null-like string values in a table using SQL.
        
        Args:
            table_name (str): Name of the table to clean
            columns (List[str]): List of columns to clean
            
        Returns:
            Dict[str, Any]: Summary of cleanup results
        """
        print(f"\n🧹 Cleaning up table '{table_name}'...")
        
        total_updated = 0
        column_updates = {}
        
        # Create conditions for all null-like values
        null_conditions = " OR ".join([f"'{value}'" for value in self.null_values])
        
        for column in columns:
            try:
                # Update query to set null-like values to NULL
                query = f"""
                UPDATE {table_name} 
                SET {column} = NULL 
                WHERE {column} IN ({null_conditions})
                """
                
                result = self.execute_sql_query(query)
                
                if result['success']:
                    # Get the number of affected rows (this might not be available in all cases)
                    updated_count = 1  # Placeholder since we can't easily get affected row count
                    column_updates[column] = updated_count
                    total_updated += updated_count
                    print(f"   ✅ Column '{column}': Updated successfully")
                else:
                    print(f"   ❌ Error updating column '{column}': {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"   ❌ Error cleaning column '{column}': {e}")
        
        return {
            'table': table_name,
            'total_updated': total_updated,
            'column_updates': column_updates
        }
    
    def generate_cleanup_sql(self) -> str:
        """
        Generate SQL script for manual execution to clean up null-like values.
        
        Returns:
            str: Complete SQL script
        """
        sql_script = """-- Database Cleanup Script
-- This script converts string representations of null values to actual NULL values
-- Generated automatically by sql_database_cleanup.py

BEGIN;

"""
        
        # Create conditions for all null-like values
        null_conditions = ", ".join([f"'{value}'" for value in self.null_values])
        
        for table_name, columns in self.tables_config.items():
            sql_script += f"\n-- Cleaning table: {table_name}\n"
            
            for column in columns:
                sql_script += f"""UPDATE {table_name} 
SET {column} = NULL 
WHERE {column} IN ({null_conditions});

"""
        
        sql_script += """
COMMIT;

-- Verification queries to check the cleanup results
"""
        
        for table_name, columns in self.tables_config.items():
            sql_script += f"\n-- Check {table_name} for remaining null-like values\n"
            for column in columns:
                sql_script += f"SELECT COUNT(*) as {column}_null_count FROM {table_name} WHERE {column} IN ({null_conditions});\n"
        
        return sql_script
    
    def save_cleanup_sql(self, filename: str = "database_cleanup.sql") -> str:
        """
        Save the cleanup SQL script to a file.
        
        Args:
            filename (str): Name of the SQL file to create
            
        Returns:
            str: Path to the created file
        """
        sql_content = self.generate_cleanup_sql()
        
        # Create scripts directory if it doesn't exist
        script_dir = os.path.dirname(os.path.abspath(__file__))
        filepath = os.path.join(script_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        
        print(f"📄 SQL cleanup script saved to: {filepath}")
        return filepath
    
    def run_manual_cleanup_via_api(self) -> Dict[str, Any]:
        """
        Run cleanup using Supabase REST API with individual UPDATE requests.
        This is a fallback method when direct SQL execution is not available.
        
        Returns:
            Dict[str, Any]: Summary of cleanup results
        """
        print("🚀 Starting manual API-based database cleanup...")
        
        all_results = {}
        
        for table_name, columns in self.tables_config.items():
            print(f"\n🧹 Processing table: {table_name}")
            
            table_results = {'updated_columns': {}, 'total_updated': 0}
            
            for column in columns:
                updated_count = 0
                
                # Update each null-like value individually
                for null_value in self.null_values:
                    try:
                        url = f"{self.supabase_url}/rest/v1/{table_name}"
                        headers = {
                            'apikey': self.supabase_key,
                            'Authorization': f'Bearer {self.supabase_key}',
                            'Content-Type': 'application/json',
                            'Prefer': 'return=minimal'
                        }
                        
                        # Set up the filter and update data
                        params = {column: f"eq.{null_value}"}
                        update_data = {column: None}
                        
                        response = requests.patch(
                            url,
                            headers=headers,
                            params=params,
                            data=json.dumps(update_data),
                            timeout=30
                        )
                        
                        if response.status_code in [200, 204]:
                            updated_count += 1
                        elif response.status_code == 406:  # No content to update
                            pass  # This is fine, means no matching records
                        else:
                            print(f"   ⚠️  Warning updating {column}={null_value}: {response.status_code}")
                    
                    except Exception as e:
                        print(f"   ❌ Error updating {column}={null_value}: {e}")
                
                if updated_count > 0:
                    table_results['updated_columns'][column] = updated_count
                    table_results['total_updated'] += updated_count
                    print(f"   ✅ Column '{column}': {updated_count} null-like values cleaned")
            
            all_results[table_name] = table_results
        
        return all_results


def main():
    """Main function to run the database cleanup"""
    cleanup = SQLDatabaseCleanup()
    
    print("🚀 Database Cleanup Tool")
    print("=" * 50)
    print("This tool will clean up null-like string values in your database.")
    print("Options:")
    print("1. Generate SQL script for manual execution")
    print("2. Run cleanup via API (slower but automated)")
    print("3. Exit")
    
    choice = input("\nSelect an option (1-3): ").strip()
    
    if choice == "1":
        print("\n📄 Generating SQL cleanup script...")
        filepath = cleanup.save_cleanup_sql()
        print(f"\n✅ SQL script generated successfully!")
        print(f"📁 File location: {filepath}")
        print("\n💡 You can now execute this SQL script manually in your database.")
        
    elif choice == "2":
        print("\n🔧 Running automated cleanup via API...")
        results = cleanup.run_manual_cleanup_via_api()
        
        print("\n📋 CLEANUP SUMMARY")
        print("=" * 30)
        total_fixed = 0
        for table_name, table_results in results.items():
            updated = table_results.get('total_updated', 0)
            if updated > 0:
                print(f"✅ {table_name}: {updated} values cleaned")
                total_fixed += updated
        
        if total_fixed > 0:
            print(f"\n🎉 Total null-like values fixed: {total_fixed}")
        else:
            print("\n✅ No null-like values found to fix!")
    
    elif choice == "3":
        print("👋 Goodbye!")
        return
    
    else:
        print("❌ Invalid choice. Please run the script again.")
    
    print("\n✨ Database cleanup process completed!")


if __name__ == "__main__":
    main()
