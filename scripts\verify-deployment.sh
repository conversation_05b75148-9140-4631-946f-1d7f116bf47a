#!/bin/bash

# Deployment Verification Script for NSE and BSE Scrapers
# This script verifies that the Cloud Run Jobs are deployed correctly with proxy integration

set -e

# Configuration
PROJECT_ID="webscrapers-463817"
REGION="asia-south1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment verification for NSE and BSE scrapers${NC}"
echo "=================================================="

# Function to check if gcloud is authenticated
check_auth() {
    echo -e "${YELLOW}Checking Google Cloud authentication...${NC}"
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        echo -e "${RED}❌ Not authenticated with Google Cloud${NC}"
        echo "Please run: gcloud auth login"
        exit 1
    fi
    echo -e "${GREEN}✅ Google Cloud authentication verified${NC}"
}

# Function to set project
set_project() {
    echo -e "${YELLOW}Setting project to ${PROJECT_ID}...${NC}"
    gcloud config set project $PROJECT_ID
    echo -e "${GREEN}✅ Project set to ${PROJECT_ID}${NC}"
}

# Function to check if Cloud Run Jobs exist
check_jobs_exist() {
    echo -e "${YELLOW}Checking if Cloud Run Jobs exist...${NC}"
    
    # Check NSE job
    if gcloud run jobs describe nse-insider-trading-scraper --region=$REGION >/dev/null 2>&1; then
        echo -e "${GREEN}✅ NSE insider trading scraper job exists${NC}"
    else
        echo -e "${RED}❌ NSE insider trading scraper job not found${NC}"
        return 1
    fi
    
    # Check BSE job
    if gcloud run jobs describe bse-insider-trading-scraper --region=$REGION >/dev/null 2>&1; then
        echo -e "${GREEN}✅ BSE insider trading scraper job exists${NC}"
    else
        echo -e "${RED}❌ BSE insider trading scraper job not found${NC}"
        return 1
    fi
}

# Function to check job configurations
check_job_configs() {
    echo -e "${YELLOW}Checking job configurations...${NC}"
    
    # Check NSE job configuration
    echo "NSE Job Configuration:"
    gcloud run jobs describe nse-insider-trading-scraper --region=$REGION --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" | grep -E "(SCRAPERAPI_KEY|USE_PROXY)" || echo "Environment variables not found"
    
    # Check BSE job configuration
    echo "BSE Job Configuration:"
    gcloud run jobs describe bse-insider-trading-scraper --region=$REGION --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" | grep -E "(SCRAPERAPI_KEY|USE_PROXY)" || echo "Environment variables not found"
    
    echo -e "${GREEN}✅ Job configurations checked${NC}"
}

# Function to test job execution
test_job_execution() {
    echo -e "${YELLOW}Testing job execution (dry run)...${NC}"
    
    # Test NSE job
    echo "Testing NSE job execution..."
    JOB_NAME="nse-test-$(date +%s)"
    if gcloud run jobs execute nse-insider-trading-scraper --region=$REGION --wait --format="value(status.conditions[0].type,status.conditions[0].status)" 2>/dev/null; then
        echo -e "${GREEN}✅ NSE job execution test passed${NC}"
    else
        echo -e "${YELLOW}⚠️  NSE job execution test - check logs for details${NC}"
    fi
    
    # Test BSE job
    echo "Testing BSE job execution..."
    if gcloud run jobs execute bse-insider-trading-scraper --region=$REGION --wait --format="value(status.conditions[0].type,status.conditions[0].status)" 2>/dev/null; then
        echo -e "${GREEN}✅ BSE job execution test passed${NC}"
    else
        echo -e "${YELLOW}⚠️  BSE job execution test - check logs for details${NC}"
    fi
}

# Function to check recent executions
check_recent_executions() {
    echo -e "${YELLOW}Checking recent job executions...${NC}"
    
    echo "Recent NSE job executions:"
    gcloud run jobs executions list --job=nse-insider-trading-scraper --region=$REGION --limit=3 --format="table(metadata.name,status.conditions[0].type,status.conditions[0].status,status.startTime)" 2>/dev/null || echo "No recent executions found"
    
    echo "Recent BSE job executions:"
    gcloud run jobs executions list --job=bse-insider-trading-scraper --region=$REGION --limit=3 --format="table(metadata.name,status.conditions[0].type,status.conditions[0].status,status.startTime)" 2>/dev/null || echo "No recent executions found"
    
    echo -e "${GREEN}✅ Recent executions checked${NC}"
}

# Function to check logs
check_logs() {
    echo -e "${YELLOW}Checking recent logs for proxy integration...${NC}"
    
    # Get latest execution for NSE
    LATEST_NSE=$(gcloud run jobs executions list --job=nse-insider-trading-scraper --region=$REGION --limit=1 --format="value(metadata.name)" 2>/dev/null || echo "")
    if [ ! -z "$LATEST_NSE" ]; then
        echo "Latest NSE execution logs (last 10 lines):"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=nse-insider-trading-scraper" --limit=10 --format="value(textPayload)" 2>/dev/null | head -10 || echo "No logs found"
    fi
    
    # Get latest execution for BSE
    LATEST_BSE=$(gcloud run jobs executions list --job=bse-insider-trading-scraper --region=$REGION --limit=1 --format="value(metadata.name)" 2>/dev/null || echo "")
    if [ ! -z "$LATEST_BSE" ]; then
        echo "Latest BSE execution logs (last 10 lines):"
        gcloud logging read "resource.type=cloud_run_job AND resource.labels.job_name=bse-insider-trading-scraper" --limit=10 --format="value(textPayload)" 2>/dev/null | head -10 || echo "No logs found"
    fi
    
    echo -e "${GREEN}✅ Logs checked${NC}"
}

# Function to display summary
display_summary() {
    echo ""
    echo "=================================================="
    echo -e "${GREEN}🎉 Deployment Verification Complete${NC}"
    echo "=================================================="
    echo ""
    echo "Next steps:"
    echo "1. Monitor job executions: gcloud run jobs executions list --job=nse-insider-trading-scraper --region=$REGION"
    echo "2. View logs: gcloud logging read 'resource.type=cloud_run_job AND resource.labels.job_name=nse-insider-trading-scraper'"
    echo "3. Execute manually: gcloud run jobs execute nse-insider-trading-scraper --region=$REGION"
    echo ""
    echo "Proxy integration features:"
    echo "- ✅ ScraperAPI proxy with fallback mechanism"
    echo "- ✅ Enhanced session management"
    echo "- ✅ Improved error handling"
    echo "- ✅ Environment variable configuration"
    echo "- ✅ Extended timeout for proxy operations (40 minutes)"
}

# Main execution
main() {
    check_auth
    set_project
    check_jobs_exist
    check_job_configs
    check_recent_executions
    check_logs
    display_summary
}

# Run main function
main "$@"
