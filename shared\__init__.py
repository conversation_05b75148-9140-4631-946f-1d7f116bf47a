"""
Shared utilities and base classes for NSE and BSE scrapers.

This module provides common functionality to eliminate code duplication
between the NSE and BSE scraper implementations.

Components:
- utils: Common utility functions for delays, user agents, error handling
- base_cookie_manager: Base class for cookie management
- base_session_manager: Base class for session management
- base_config: Shared configuration patterns
- cli_framework: Unified CLI framework components
- data_processor: Data processing and hash calculation utilities
- error_handler: Standardized error handling and logging
"""

# Import legacy utility functions
from .utils import (
    get_random_user_agent,
    add_random_delay,
    handle_request_error,
    format_date_for_display,
    create_output_directory,
    get_common_headers,
    get_api_headers,
    validate_date_range,
    log_session_establishment,
    log_request_status,
    calculate_unified_hash,
    fetch_cross_reference_code
)

# Import base classes
from .base_cookie_manager import BaseCookieManager, NSECookieManager, BSECookieManager
from .base_session_manager import BaseSessionManager, NSESessionManager, BSESessionManager
from .base_config import BaseConfig, NSEConfig, BSEConfig

# Import new framework components
from .cli_framework import (
    BaseArgumentParser,
    BaseCommandExecutor,
    DataDisplayHelper,
    ValidationHelper
)

from .data_processor import (
    UnifiedHashCalculator,
    DataValidator,
    FileManager,
    DataCleaner
)

from .error_handler import (
    ErrorHandler,
    ScraperError,
    SessionError,
    DataFetchError,
    DataParsingError,
    DatabaseError,
    with_error_handling,
    RetryHandler,
    HealthChecker,
    GracefulShutdown
)

__version__ = "2.0.0"
__author__ = "Scraper Refactoring Team"
__description__ = "Enhanced shared utilities and framework for NSE and BSE scrapers"

__all__ = [
    # Legacy utility functions
    "get_random_user_agent",
    "add_random_delay",
    "handle_request_error",
    "format_date_for_display",
    "create_output_directory",
    "get_common_headers",
    "get_api_headers",
    "validate_date_range",
    "log_session_establishment",
    "log_request_status",
    "calculate_unified_hash",
    "fetch_cross_reference_code",

    # Base classes
    "BaseCookieManager",
    "NSECookieManager",
    "BSECookieManager",
    "BaseSessionManager",
    "NSESessionManager",
    "BSESessionManager",
    "BaseConfig",
    "NSEConfig",
    "BSEConfig",

    # CLI Framework
    "BaseArgumentParser",
    "BaseCommandExecutor",
    "DataDisplayHelper",
    "ValidationHelper",

    # Data Processing
    "UnifiedHashCalculator",
    "DataValidator",
    "FileManager",
    "DataCleaner",

    # Error Handling
    "ErrorHandler",
    "ScraperError",
    "SessionError",
    "DataFetchError",
    "DataParsingError",
    "DatabaseError",
    "with_error_handling",
    "RetryHandler",
    "HealthChecker",
    "GracefulShutdown"
]
