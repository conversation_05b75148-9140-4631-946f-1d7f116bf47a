"""
Base Cookie Manager for NSE and BSE scrapers.

This module provides a common base class for cookie management that eliminates
code duplication while allowing exchange-specific customization.
"""

import time
import random
from abc import ABC, abstractmethod
from .utils import add_random_delay, handle_request_error, log_session_establishment


class BaseCookieManager(ABC):
    """
    Base class for managing dynamic cookie acquisition across different exchanges.
    
    This class provides common cookie management functionality while allowing
    subclasses to customize exchange-specific behavior.
    """
    
    def __init__(self, session, config):
        """
        Initialize the cookie manager.
        
        Args:
            session: HTTP session object
            config: Configuration object with exchange-specific settings
        """
        self.session = session
        self.config = config
        self.base_url = config.BASE_URL
        self.exchange_name = config.EXCHANGE_NAME
    
    @abstractmethod
    def get_insider_trading_url(self):
        """
        Get the insider trading page URL for the specific exchange.
        
        Returns:
            str: Full URL to the insider trading page
        """
        pass
    
    def get_cookies(self):
        """
        Get necessary cookies by visiting exchange pages.
        
        This method implements the common pattern used by both exchanges:
        1. Visit homepage first
        2. Add random delay
        3. Visit insider trading page
        4. Add another delay
        
        Returns:
            bool: True if session establishment was successful, False otherwise
        """
        try:
            print(f"Getting {self.exchange_name} session cookies...")
            print("Attempting direct session establishment...")
            
            # Visit homepage first
            success = self._visit_homepage()
            if not success:
                return False
            
            # Add random delay between requests
            add_random_delay(1, 3)
            
            # Visit insider trading page
            success = self._visit_insider_trading_page()
            if not success:
                return False
            
            # Add final delay
            add_random_delay(1, 2)
            
            log_session_establishment(self.exchange_name, True)
            return True
            
        except Exception as e:
            handle_request_error(e, context=f"{self.exchange_name} session establishment")
            log_session_establishment(self.exchange_name, False)
            return False
    
    def _visit_homepage(self):
        """
        Visit the exchange homepage to establish initial session.
        
        Returns:
            bool: True if homepage visit was successful
        """
        try:
            response = self.session.get(self.base_url, timeout=30)
            print(f"{self.exchange_name} Homepage status: {response.status_code}")
            
            if response.status_code == 200:
                return True
            else:
                print(f"{self.exchange_name} homepage visit failed with status: {response.status_code}")
                return False
                
        except Exception as e:
            handle_request_error(e, self.base_url, f"{self.exchange_name} homepage visit")
            return False
    
    def _visit_insider_trading_page(self):
        """
        Visit the insider trading page to complete session establishment.
        
        Returns:
            bool: True if insider trading page visit was successful
        """
        try:
            insider_url = self.get_insider_trading_url()
            response = self.session.get(insider_url, timeout=30)
            print(f"{self.exchange_name} Insider trading page status: {response.status_code}")
            
            if response.status_code == 200:
                return True
            else:
                print(f"{self.exchange_name} insider trading page visit failed with status: {response.status_code}")
                return False
                
        except Exception as e:
            insider_url = self.get_insider_trading_url()
            handle_request_error(e, insider_url, f"{self.exchange_name} insider trading page visit")
            return False
    
    def refresh_cookies(self):
        """
        Refresh cookies by re-establishing the session.
        
        Returns:
            bool: True if cookie refresh was successful
        """
        print(f"Refreshing {self.exchange_name} session cookies...")
        return self.get_cookies()
    
    def validate_session(self):
        """
        Validate that the current session has valid cookies.
        
        Returns:
            bool: True if session appears to be valid
        """
        try:
            # Check if we have any cookies
            if not self.session.cookies:
                print(f"No cookies found in {self.exchange_name} session")
                return False
            
            # Try a simple request to validate session
            response = self.session.get(self.base_url, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            handle_request_error(e, context=f"{self.exchange_name} session validation")
            return False
    
    def get_session_info(self):
        """
        Get information about the current session.
        
        Returns:
            dict: Session information including cookie count and status
        """
        return {
            'exchange': self.exchange_name,
            'base_url': self.base_url,
            'cookie_count': len(self.session.cookies),
            'cookies': list(self.session.cookies.keys()) if self.session.cookies else [],
            'is_valid': self.validate_session()
        }
    
    def __str__(self):
        """String representation of the cookie manager"""
        return f"{self.exchange_name}CookieManager(base_url={self.base_url})"
    
    def __repr__(self):
        """Detailed representation of the cookie manager"""
        info = self.get_session_info()
        return (f"{self.__class__.__name__}("
                f"exchange={info['exchange']}, "
                f"cookie_count={info['cookie_count']}, "
                f"is_valid={info['is_valid']})")


class NSECookieManager(BaseCookieManager):
    """NSE-specific cookie manager implementation"""
    
    def get_insider_trading_url(self):
        """Get NSE insider trading page URL"""
        return f"{self.base_url}/companies-listing/corporate-filings-insider-trading"


class BSECookieManager(BaseCookieManager):
    """BSE-specific cookie manager implementation"""
    
    def get_insider_trading_url(self):
        """Get BSE insider trading page URL"""
        return f"{self.base_url}{self.config.INSIDER_TRADING_URL}"
