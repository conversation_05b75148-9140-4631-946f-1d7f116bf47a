"""
Base Session Manager for NSE and BSE scrapers.

This module provides a common base class for HTTP session management that eliminates
code duplication while allowing exchange-specific customization.
"""

import requests
import time
import urllib3
from abc import ABC, abstractmethod
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Suppress SSL warnings when using ScraperAPI proxy
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
from .utils import (
    get_random_user_agent, 
    get_common_headers, 
    get_api_headers,
    add_random_delay, 
    handle_request_error,
    log_request_status
)
from .base_cookie_manager import NSECookieManager, BSECookieManager


class BaseSessionManager(ABC):
    """
    Base class for managing HTTP sessions across different exchanges.
    
    This class provides common session management functionality including:
    - Session setup with retry strategies
    - Header management
    - Cookie management
    - Request handling with error recovery
    """
    
    def __init__(self, config):
        """
        Initialize the session manager.
        
        Args:
            config: Configuration object with exchange-specific settings
        """
        self.config = config
        self.session = requests.Session()
        self.setup_session()
        self.cookie_manager = self._create_cookie_manager()
        # Establish session by getting cookies
        self.cookie_manager.get_cookies()
        print(f"{config.EXCHANGE_NAME} Session manager initialized with enhanced reliability")
    
    @abstractmethod
    def _create_cookie_manager(self):
        """
        Create the appropriate cookie manager for the exchange.
        
        Returns:
            BaseCookieManager: Exchange-specific cookie manager instance
        """
        pass
    
    @abstractmethod
    def get_exchange_specific_headers(self):
        """
        Get exchange-specific headers that should be added to the base headers.
        
        Returns:
            dict: Exchange-specific headers
        """
        pass
    
    def setup_session(self):
        """Set up session with retry strategy, headers, and proxy configuration"""
        # Get base headers
        headers = get_common_headers(
            exchange_name=self.config.EXCHANGE_NAME,
            referer_url=self._get_referer_url()
        )

        # Add exchange-specific headers
        exchange_headers = self.get_exchange_specific_headers()
        headers.update(exchange_headers)

        # Setup retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)
        self.session.headers.update(headers)

        # Configure proxy if enabled
        proxy_config = self.config.get_proxy_config()
        if proxy_config:
            self.session.proxies.update(proxy_config)
            # Disable SSL verification for ScraperAPI proxy
            self.session.verify = False
            print(f"{self.config.EXCHANGE_NAME} session configured with ScraperAPI proxy")
        else:
            print(f"{self.config.EXCHANGE_NAME} session configured without proxy")

        print(f"{self.config.EXCHANGE_NAME} session configured with dynamic headers")
    
    def _get_referer_url(self):
        """
        Get the referer URL for the exchange.
        
        Returns:
            str: Referer URL
        """
        # Default implementation - subclasses can override
        return self.config.BASE_URL

    def _make_scraperapi_request(self, method, url, **kwargs):
        """
        Make request through ScraperAPI using the working script's approach.

        Args:
            method (str): HTTP method (GET, POST)
            url (str): Target URL
            **kwargs: Additional arguments for the request

        Returns:
            requests.Response or None: Response object or None if failed
        """
        try:
            # ScraperAPI configuration from working script
            scraper_api_key = self.config.SCRAPERAPI_KEY

            if method.upper() == 'GET':
                # For GET requests, use ScraperAPI endpoint with parameters
                payload = {
                    'api_key': scraper_api_key,
                    'url': url,
                    'render': 'false',  # Don't render JavaScript for API calls
                    'country_code': 'IN',  # Use Indian IP
                    'premium': 'true'  # Use premium proxy pool
                }

                # Merge any additional params
                params = kwargs.get('params')
                if params:
                    payload.update(params)
                kwargs.pop('params', None)

                print(f"Making ScraperAPI request to: {url}")
                return self.session.get('https://api.scraperapi.com', params=payload, **kwargs)

            elif method.upper() == 'POST':
                # For POST requests, construct URL with API key and target URL
                scraperapi_url = f"https://api.scraperapi.com?api_key={scraper_api_key}&url={url}"
                return self.session.post(scraperapi_url, **kwargs)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

        except Exception as e:
            print(f"❌ ScraperAPI request failed: {e}")
            print(f"   Target URL: {url}")
            print(f"   ScraperAPI Key: {scraper_api_key[:10]}..." if scraper_api_key else "   ScraperAPI Key: None")
            return None

    def _prepare_url_for_request(self, url):
        """
        Prepare URL for request - either use ScraperAPI endpoint or direct URL.

        Args:
            url (str): Original URL to request

        Returns:
            str: URL to use for the actual request
        """
        if hasattr(self.config, 'should_use_endpoint_mode') and self.config.should_use_endpoint_mode():
            # Use ScraperAPI endpoint mode for better reliability
            return self.config.get_scraperapi_url(url)
        return url

    def make_get_request(self, url, params=None, **kwargs):
        """
        Make a GET request with error handling and random delays.

        Args:
            url (str): URL to request
            params (dict, optional): Query parameters
            **kwargs: Additional arguments for requests.get

        Returns:
            requests.Response or None: Response object or None if failed
        """
        try:
            # Add random delay to avoid being detected as bot
            add_random_delay(1, 3)

            # Set default timeout if not provided
            kwargs.setdefault('timeout', self.config.REQUEST_TIMEOUT)

            # Use ScraperAPI endpoint mode if proxy is enabled
            if self.config.USE_PROXY and hasattr(self.config, 'PROXY_MODE') and self.config.PROXY_MODE == 'endpoint':
                # Use ScraperAPI endpoint mode like the working script
                # Pass params in kwargs for ScraperAPI method to handle correctly
                if params:
                    kwargs['params'] = params
                response = self._make_scraperapi_request('GET', url, **kwargs)
            else:
                # Direct request or proxy mode
                response = self.session.get(url, params=params, **kwargs)

            # Check if response is None (ScraperAPI request failed)
            if response is None:
                print(f"❌ Request failed: No response received for URL: {url}")
                return None

            log_request_status(url, response.status_code, self.config.EXCHANGE_NAME)
            return response

        except Exception as e:
            handle_request_error(e, url, f"{self.config.EXCHANGE_NAME} GET request")
            return None
    
    def make_post_request(self, url, data=None, json=None, **kwargs):
        """
        Make a POST request with error handling and random delays.

        Args:
            url (str): URL to request
            data (dict, optional): Form data
            json (dict, optional): JSON data
            **kwargs: Additional arguments for requests.post

        Returns:
            requests.Response or None: Response object or None if failed
        """
        try:
            # Add random delay to avoid being detected as bot
            add_random_delay(1, 2)

            # Set default timeout if not provided
            kwargs.setdefault('timeout', self.config.REQUEST_TIMEOUT)

            # Update headers for POST request if needed
            if data and 'Content-Type' not in kwargs.get('headers', {}):
                post_headers = kwargs.get('headers', {}).copy()
                post_headers.update({
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Origin": self.config.BASE_URL,
                    "Referer": self._get_referer_url()
                })
                kwargs['headers'] = post_headers

            # Prepare URL for proxy if needed
            request_url = self._prepare_url_for_request(url)

            # Note: For POST requests with ScraperAPI endpoint mode,
            # we need to be careful about form data handling
            response = self.session.post(request_url, data=data, json=json, **kwargs)
            log_request_status(url, response.status_code, self.config.EXCHANGE_NAME)
            return response

        except Exception as e:
            handle_request_error(e, url, f"{self.config.EXCHANGE_NAME} POST request")
            return None
    
    def test_connection(self, url=None):
        """
        Test if we can connect to the exchange website.
        
        Args:
            url (str, optional): URL to test. Defaults to base URL.
            
        Returns:
            bool: True if successful, False otherwise
        """
        test_url = url or self.config.BASE_URL
        response = self.make_get_request(test_url)
        
        if response and response.status_code == 200:
            print(f"✓ Connection to {self.config.EXCHANGE_NAME} website successful")
            return True
        else:
            print(f"✗ Failed to connect to {self.config.EXCHANGE_NAME} website")
            return False
    
    def refresh_session(self):
        """
        Refresh the session by re-establishing cookies and headers.
        
        Returns:
            bool: True if refresh was successful
        """
        try:
            print(f"Refreshing {self.config.EXCHANGE_NAME} session...")
            
            # Refresh cookies
            success = self.cookie_manager.refresh_cookies()
            if not success:
                return False
            
            # Update headers with new user agent
            self.session.headers.update({
                'User-Agent': get_random_user_agent()
            })
            
            print(f"✅ {self.config.EXCHANGE_NAME} session refreshed successfully")
            return True
            
        except Exception as e:
            handle_request_error(e, context=f"{self.config.EXCHANGE_NAME} session refresh")
            return False
    
    def get_session(self):
        """Return the configured session"""
        return self.session
    
    def close_session(self):
        """Close the HTTP session"""
        self.session.close()
        print(f"{self.config.EXCHANGE_NAME} session closed")
    
    def __enter__(self):
        """Support for context manager"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Cleanup when exiting context manager"""
        self.close_session()
    
    def get_session_info(self):
        """
        Get information about the current session.
        
        Returns:
            dict: Session information
        """
        return {
            'exchange': self.config.EXCHANGE_NAME,
            'base_url': self.config.BASE_URL,
            'cookie_info': self.cookie_manager.get_session_info(),
            'headers': dict(self.session.headers),
            'is_connected': self.test_connection()
        }
    
    def __str__(self):
        """String representation of the session manager"""
        return f"{self.config.EXCHANGE_NAME}SessionManager(base_url={self.config.BASE_URL})"
    
    def __repr__(self):
        """Detailed representation of the session manager"""
        info = self.get_session_info()
        return (f"{self.__class__.__name__}("
                f"exchange={info['exchange']}, "
                f"is_connected={info['is_connected']})")


class NSESessionManager(BaseSessionManager):
    """NSE-specific session manager implementation"""
    
    def _create_cookie_manager(self):
        """Create NSE cookie manager"""
        return NSECookieManager(self.session, self.config)
    
    def get_exchange_specific_headers(self):
        """Get NSE-specific headers"""
        return get_api_headers(
            exchange_name=self.config.EXCHANGE_NAME,
            referer_url=self._get_referer_url()
        )
    
    def _get_referer_url(self):
        """Get NSE referer URL"""
        return f"{self.config.BASE_URL}/companies-listing/corporate-filings-insider-trading"


class BSESessionManager(BaseSessionManager):
    """BSE-specific session manager implementation"""
    
    def _create_cookie_manager(self):
        """Create BSE cookie manager"""
        return BSECookieManager(self.session, self.config)
    
    def get_exchange_specific_headers(self):
        """Get BSE-specific headers"""
        return {
            "Origin": self.config.BASE_URL,
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
        }
    
    def _get_referer_url(self):
        """Get BSE referer URL"""
        return f"{self.config.BASE_URL}/corporates/Insider_Trading_new.aspx"
