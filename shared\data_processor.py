"""
Shared Data Processing Module for NSE and BSE Scrapers

This module provides unified data processing functionality including:
- Standardized hash calculation for duplicate detection
- Data validation and cleaning utilities
- CSV file management with consistent patterns
- Database interaction helpers
"""

import pandas as pd
import hashlib
import os
import csv
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
from abc import ABC, abstractmethod


class UnifiedHashCalculator:
    """
    Unified hash calculation for both NSE and BSE scrapers.
    
    This class standardizes hash generation while maintaining exchange-specific
    field mappings for duplicate detection.
    """
    
    # Exchange-specific field mappings for hash calculation
    NSE_HASH_FIELDS = {
        'security_code': 'symbol',
        'person_name': 'name_of_the_acquirer_disposer',
        'securities_number': 'no_of_securities_acquired_displosed',
        'cross_reference_code': 'bse_code'
    }
    
    BSE_HASH_FIELDS = {
        'security_code': 'Security Code',
        'person_name': 'Name of Person',
        'securities_number': 'Securities Acquired/Disposed - Number',
        'cross_reference_code': 'nse_code'
    }
    
    @classmethod
    def calculate_hash(cls, row: Dict[str, Any], exchange_type: str) -> str:
        """
        Calculate standardized hash for a record using the existing shared hash logic.

        Args:
            row (Dict[str, Any]): Record data as dictionary
            exchange_type (str): 'nse' or 'bse'

        Returns:
            str: SHA256 hash string
        """
        try:
            # Import the existing hash function from shared utils
            from .utils import calculate_unified_hash

            # Use the existing standardized hash calculation
            return calculate_unified_hash(exchange_type, **row)

        except Exception as e:
            # Generate fallback hash to prevent complete failure
            fallback_input = f"{exchange_type.upper()}_ERROR_{int(datetime.now().timestamp())}_{str(e)[:50]}"
            return hashlib.sha256(fallback_input.encode('utf-8')).hexdigest()
    
    @classmethod
    def add_hashes_to_dataframe(cls, dataframe: pd.DataFrame, exchange_type: str) -> pd.DataFrame:
        """
        Add hash column to DataFrame for duplicate detection.
        
        Args:
            dataframe (pd.DataFrame): Input DataFrame
            exchange_type (str): 'nse' or 'bse'
            
        Returns:
            pd.DataFrame: DataFrame with added 'record_hash' column
        """
        if dataframe is None or dataframe.empty:
            return dataframe
        
        print(f"Generating hashes for {len(dataframe)} {exchange_type.upper()} records...")
        
        # Generate hash for each row
        dataframe['record_hash'] = dataframe.apply(
            lambda row: cls.calculate_hash(row.to_dict(), exchange_type), axis=1
        )
        
        # Check for duplicate hashes within this dataset
        duplicate_count = dataframe['record_hash'].duplicated().sum()
        if duplicate_count > 0:
            print(f"⚠️ Found {duplicate_count} duplicate hashes within dataset")
        
        print(f"✅ Generated {len(dataframe)} hashes for {exchange_type.upper()} records")
        return dataframe


class DataValidator:
    """
    Data validation utilities for both scrapers.
    """
    
    @staticmethod
    def validate_dataframe(dataframe: pd.DataFrame, exchange_type: str) -> Tuple[bool, List[str]]:
        """
        Validate DataFrame structure and content.
        
        Args:
            dataframe (pd.DataFrame): DataFrame to validate
            exchange_type (str): 'nse' or 'bse'
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_issues)
        """
        issues = []
        
        if dataframe is None:
            issues.append("DataFrame is None")
            return False, issues
        
        if dataframe.empty:
            issues.append("DataFrame is empty")
            return False, issues
        
        # Check for required columns based on exchange type
        required_columns = DataValidator._get_required_columns(exchange_type)
        missing_columns = [col for col in required_columns if col not in dataframe.columns]
        
        if missing_columns:
            issues.append(f"Missing required columns: {missing_columns}")
        
        # Check for completely empty columns
        empty_columns = [col for col in dataframe.columns if dataframe[col].isna().all()]
        if empty_columns:
            issues.append(f"Completely empty columns: {empty_columns}")
        
        # Check data quality
        if len(dataframe) > 0:
            # Check for excessive null values
            null_percentages = (dataframe.isnull().sum() / len(dataframe)) * 100
            high_null_columns = null_percentages[null_percentages > 80].index.tolist()
            if high_null_columns:
                issues.append(f"Columns with >80% null values: {high_null_columns}")
        
        return len(issues) == 0, issues
    
    @staticmethod
    def _get_required_columns(exchange_type: str) -> List[str]:
        """Get required columns for each exchange type"""
        if exchange_type.lower() == 'nse':
            return ['symbol', 'name_of_the_acquirer_disposer']
        elif exchange_type.lower() == 'bse':
            return ['Security Code', 'Name of Person']
        else:
            return []


class FileManager:
    """
    Unified file management for both scrapers.
    """
    
    def __init__(self, output_folder: str, exchange_name: str):
        """
        Initialize file manager.
        
        Args:
            output_folder (str): Path to output folder
            exchange_name (str): Name of exchange for file naming
        """
        self.output_folder = output_folder
        self.exchange_name = exchange_name.lower()
        self._ensure_output_folder()
    
    def _ensure_output_folder(self):
        """Create output folder if it doesn't exist"""
        if not os.path.exists(self.output_folder):
            os.makedirs(self.output_folder)
            print(f"📁 Created output directory: {self.output_folder}")
    
    def save_dataframe_to_csv(self, dataframe: pd.DataFrame, 
                             start_date: str = None, end_date: str = None,
                             custom_filename: str = None) -> Optional[str]:
        """
        Save DataFrame to CSV file with standardized naming.
        
        Args:
            dataframe (pd.DataFrame): Data to save
            start_date (str, optional): Start date for filename
            end_date (str, optional): End date for filename
            custom_filename (str, optional): Custom filename
            
        Returns:
            Optional[str]: Path to saved file or None if failed
        """
        try:
            if dataframe is None or dataframe.empty:
                print("⚠️ No data to save")
                return None
            
            # Generate filename
            if custom_filename:
                filename = self._ensure_csv_extension(custom_filename)
            else:
                filename = self._generate_filename(start_date, end_date)
            
            # Create full file path
            file_path = os.path.join(self.output_folder, filename)
            
            # Save the DataFrame
            dataframe.to_csv(file_path, index=False)
            
            print(f"💾 Data saved to: {file_path}")
            return file_path
            
        except Exception as e:
            print(f"❌ Error saving DataFrame to CSV: {e}")
            return None
    
    def _generate_filename(self, start_date: str = None, end_date: str = None) -> str:
        """Generate filename based on dates or timestamp"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        if start_date and end_date:
            # Try to create date-based filename
            try:
                start_clean = start_date.replace('-', '').replace('/', '')
                end_clean = end_date.replace('-', '').replace('/', '')
                return f"{self.exchange_name}_insider_trading_{start_clean}_to_{end_clean}.csv"
            except:
                pass
        
        # Fallback to timestamp-based filename
        return f"{self.exchange_name}_insider_trading_{timestamp}.csv"
    
    def _ensure_csv_extension(self, filename: str) -> str:
        """Ensure filename has .csv extension"""
        if not filename.lower().endswith('.csv'):
            filename += '.csv'
        return filename
    
    def list_saved_files(self) -> List[Dict[str, Any]]:
        """
        List all files in the output folder.
        
        Returns:
            List[Dict[str, Any]]: List of file information dictionaries
        """
        try:
            if not os.path.exists(self.output_folder):
                return []
            
            files_info = []
            
            for filename in os.listdir(self.output_folder):
                file_path = os.path.join(self.output_folder, filename)
                
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    
                    file_info = {
                        'name': filename,
                        'path': file_path,
                        'size_bytes': file_size,
                        'size_kb': round(file_size / 1024, 1),
                        'size_mb': round(file_size / (1024 * 1024), 2)
                    }
                    
                    files_info.append(file_info)
            
            return files_info
            
        except Exception as e:
            print(f"❌ Error listing files: {e}")
            return []


class DataCleaner:
    """
    Data cleaning utilities for both scrapers.
    """
    
    @staticmethod
    def clean_dataframe(dataframe: pd.DataFrame, exchange_type: str) -> pd.DataFrame:
        """
        Clean DataFrame with exchange-specific rules.
        
        Args:
            dataframe (pd.DataFrame): Input DataFrame
            exchange_type (str): 'nse' or 'bse'
            
        Returns:
            pd.DataFrame: Cleaned DataFrame
        """
        if dataframe is None or dataframe.empty:
            return dataframe
        
        # Make a copy to avoid modifying original
        cleaned_df = dataframe.copy()
        
        # Common cleaning operations
        cleaned_df = DataCleaner._clean_whitespace(cleaned_df)
        cleaned_df = DataCleaner._standardize_null_values(cleaned_df)
        
        # Exchange-specific cleaning
        if exchange_type.lower() == 'nse':
            cleaned_df = DataCleaner._clean_nse_specific(cleaned_df)
        elif exchange_type.lower() == 'bse':
            cleaned_df = DataCleaner._clean_bse_specific(cleaned_df)
        
        return cleaned_df
    
    @staticmethod
    def _clean_whitespace(dataframe: pd.DataFrame) -> pd.DataFrame:
        """Remove extra whitespace from string columns"""
        for col in dataframe.select_dtypes(include=['object']).columns:
            dataframe[col] = dataframe[col].astype(str).str.strip()
        return dataframe
    
    @staticmethod
    def _standardize_null_values(dataframe: pd.DataFrame) -> pd.DataFrame:
        """Standardize null value representations"""
        # Replace common null representations with actual NaN
        null_values = ['', 'N/A', 'NA', 'null', 'NULL', 'None', '-', '--']
        return dataframe.replace(null_values, pd.NA)
    
    @staticmethod
    def _clean_nse_specific(dataframe: pd.DataFrame) -> pd.DataFrame:
        """NSE-specific data cleaning"""
        # Clean column names by removing newlines and extra spaces
        dataframe.columns = [col.strip().replace('\n', '').replace('\r', '') for col in dataframe.columns]
        return dataframe
    
    @staticmethod
    def _clean_bse_specific(dataframe: pd.DataFrame) -> pd.DataFrame:
        """BSE-specific data cleaning"""
        # Handle BSE-specific formatting issues
        for col in dataframe.select_dtypes(include=['object']).columns:
            # Remove extra whitespace and normalize line breaks
            dataframe[col] = dataframe[col].astype(str).str.replace('\n', ' ').str.replace('\r', ' ')
            dataframe[col] = dataframe[col].str.replace(r'\s+', ' ', regex=True)
        
        return dataframe
