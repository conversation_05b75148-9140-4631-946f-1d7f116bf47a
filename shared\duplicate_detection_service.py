"""
Duplicate Detection Service for Corporate Announcements
Integrates PDF hash computation with database operations.
"""

import requests
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import asyncio
from .pdf_duplicate_detector import PDFDuplicateDetector, compute_pdf_hashes_batch

logger = logging.getLogger(__name__)

class DuplicateDetectionService:
    """Service for managing PDF duplicate detection in corporate announcements"""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        """
        Initialize the duplicate detection service
        
        Args:
            supabase_url: Supabase project URL
            supabase_key: Supabase API key
        """
        self.supabase_url = supabase_url.rstrip('/')
        self.supabase_key = supabase_key
        self.pdf_detector = PDFDuplicateDetector()
        
        self.headers = {
            'apikey': supabase_key,
            'Authorization': f'Bearer {supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        }
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> requests.Response:
        """Make HTTP request to Supabase API"""
        url = f"{self.supabase_url}/rest/v1/{endpoint}"
        
        if method.upper() == 'GET':
            response = requests.get(url, headers=self.headers, params=data)
        elif method.upper() == 'POST':
            response = requests.post(url, headers=self.headers, json=data)
        elif method.upper() == 'PATCH':
            response = requests.patch(url, headers=self.headers, json=data)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        return response
    
    def get_pending_hash_computations(self, limit: int = 100) -> List[Dict]:
        """
        Get PDFs that need hash computation
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            List of PDF registry records needing hash computation
        """
        try:
            response = self._make_request('GET', 'pdf_registry', {
                'hash_computation_status': 'eq.pending',
                'limit': limit,
                'order': 'created_at.asc'
            })
            
            if response.ok:
                return response.json()
            else:
                logger.error(f"Failed to get pending computations: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting pending computations: {e}")
            return []
    
    def update_pdf_hash(self, pdf_url: str, pdf_hash: Optional[str], status: str = 'computed') -> bool:
        """
        Update PDF hash in the registry
        
        Args:
            pdf_url: PDF URL to update
            pdf_hash: Computed hash (None if computation failed)
            status: Computation status ('computed' or 'failed')
            
        Returns:
            True if update successful
        """
        try:
            update_data = {
                'pdf_hash': pdf_hash,
                'hash_computation_status': status,
                'hash_computed_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat()
            }
            
            response = self._make_request('PATCH', f'pdf_registry?pdf_url=eq.{pdf_url}', update_data)
            
            if response.ok:
                logger.info(f"Updated hash for {pdf_url}: {pdf_hash}")
                return True
            else:
                logger.error(f"Failed to update hash for {pdf_url}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating hash for {pdf_url}: {e}")
            return False
    
    def update_duplicate_flags_by_hash(self, pdf_hash: str) -> Dict[str, int]:
        """
        Update is_duplicate flags for records with the same PDF hash
        
        Args:
            pdf_hash: PDF hash to check for duplicates
            
        Returns:
            Dictionary with update counts for each table
        """
        results = {'bse_updated': 0, 'nse_updated': 0}
        
        if not pdf_hash:
            return results
        
        try:
            # Get all records with this hash from registry
            response = self._make_request('GET', 'pdf_registry', {
                'pdf_hash': f'eq.{pdf_hash}',
                'order': 'first_seen_at.asc'
            })
            
            if not response.ok:
                logger.error(f"Failed to get records for hash {pdf_hash}")
                return results
            
            records = response.json()
            if len(records) <= 1:
                return results  # No duplicates
            
            # First record is original, rest are duplicates
            original_record = records[0]
            duplicate_records = records[1:]
            
            logger.info(f"Found {len(duplicate_records)} duplicates for hash {pdf_hash}")
            
            # Update duplicate flags
            for record in duplicate_records:
                table_name = record['source_table']
                record_id = record['source_record_id']
                
                if table_name == 'bse_corporate_announcements':
                    update_response = self._make_request('PATCH', 
                        f'bse_corporate_announcements?id=eq.{record_id}',
                        {'is_duplicate': True, 'pdf_hash': pdf_hash}
                    )
                    if update_response.ok:
                        results['bse_updated'] += 1
                        
                elif table_name == 'nse_corporate_announcements':
                    update_response = self._make_request('PATCH',
                        f'nse_corporate_announcements?id=eq.{record_id}',
                        {'is_duplicate': True, 'pdf_hash': pdf_hash}
                    )
                    if update_response.ok:
                        results['nse_updated'] += 1
            
            # Ensure original is marked as not duplicate
            original_table = original_record['source_table']
            original_id = original_record['source_record_id']
            
            if original_table == 'bse_corporate_announcements':
                self._make_request('PATCH',
                    f'bse_corporate_announcements?id=eq.{original_id}',
                    {'is_duplicate': False, 'pdf_hash': pdf_hash}
                )
            elif original_table == 'nse_corporate_announcements':
                self._make_request('PATCH',
                    f'nse_corporate_announcements?id=eq.{original_id}',
                    {'is_duplicate': False, 'pdf_hash': pdf_hash}
                )
            
            return results
            
        except Exception as e:
            logger.error(f"Error updating duplicate flags for hash {pdf_hash}: {e}")
            return results
    
    async def process_pending_hashes(self, batch_size: int = 50, max_concurrent: int = 5) -> Dict[str, Any]:
        """
        Process pending PDF hash computations
        
        Args:
            batch_size: Number of PDFs to process in each batch
            max_concurrent: Maximum concurrent PDF downloads
            
        Returns:
            Processing statistics
        """
        stats = {
            'total_processed': 0,
            'successful_hashes': 0,
            'failed_hashes': 0,
            'duplicates_found': 0,
            'bse_updated': 0,
            'nse_updated': 0
        }
        
        try:
            # Get pending computations
            pending_records = self.get_pending_hash_computations(batch_size)
            
            if not pending_records:
                logger.info("No pending hash computations found")
                return stats
            
            logger.info(f"Processing {len(pending_records)} pending hash computations")
            
            # Extract URLs for batch processing
            pdf_urls = [record['pdf_url'] for record in pending_records]
            
            # Compute hashes in batch
            hash_results = await compute_pdf_hashes_batch(pdf_urls, max_concurrent=max_concurrent)
            
            stats['total_processed'] = len(hash_results)
            
            # Update database with results
            hash_groups = {}  # Group by hash to find duplicates
            
            for pdf_url, pdf_hash in hash_results.items():
                if pdf_hash:
                    stats['successful_hashes'] += 1
                    self.update_pdf_hash(pdf_url, pdf_hash, 'computed')
                    
                    # Group by hash for duplicate detection
                    if pdf_hash not in hash_groups:
                        hash_groups[pdf_hash] = []
                    hash_groups[pdf_hash].append(pdf_url)
                    
                else:
                    stats['failed_hashes'] += 1
                    self.update_pdf_hash(pdf_url, None, 'failed')
            
            # Process duplicates
            for pdf_hash, urls in hash_groups.items():
                if len(urls) > 1:
                    stats['duplicates_found'] += len(urls) - 1
                    update_results = self.update_duplicate_flags_by_hash(pdf_hash)
                    stats['bse_updated'] += update_results['bse_updated']
                    stats['nse_updated'] += update_results['nse_updated']
            
            logger.info(f"Hash processing complete: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error processing pending hashes: {e}")
            return stats
    
    def get_duplicate_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive duplicate detection statistics
        
        Returns:
            Dictionary with duplicate statistics
        """
        try:
            # Use the database function we created
            response = self._make_request('POST', 'rpc/get_duplicate_stats', {})
            
            if response.ok:
                stats_data = response.json()
                
                # Format the response
                formatted_stats = {}
                for row in stats_data:
                    table_name = row['table_name']
                    formatted_stats[table_name] = {
                        'total_records': row['total_records'],
                        'records_with_pdfs': row['records_with_pdfs'],
                        'duplicate_records': row['duplicate_records'],
                        'unique_pdfs': row['unique_pdfs'],
                        'duplicate_percentage': (
                            (row['duplicate_records'] / row['records_with_pdfs'] * 100) 
                            if row['records_with_pdfs'] > 0 else 0
                        )
                    }
                
                # Add registry statistics
                registry_response = self._make_request('GET', 'pdf_registry', {
                    'select': 'hash_computation_status',
                    'limit': 10000  # Adjust as needed
                })
                
                if registry_response.ok:
                    registry_data = registry_response.json()
                    status_counts = {}
                    for record in registry_data:
                        status = record['hash_computation_status']
                        status_counts[status] = status_counts.get(status, 0) + 1
                    
                    formatted_stats['pdf_registry'] = {
                        'total_pdfs': len(registry_data),
                        'status_breakdown': status_counts
                    }
                
                return formatted_stats
                
            else:
                logger.error(f"Failed to get duplicate statistics: {response.status_code}")
                return {}
                
        except Exception as e:
            logger.error(f"Error getting duplicate statistics: {e}")
            return {}
    
    def cleanup_failed_computations(self, max_age_hours: int = 24) -> int:
        """
        Reset failed hash computations older than specified age
        
        Args:
            max_age_hours: Maximum age in hours for failed computations
            
        Returns:
            Number of records reset
        """
        try:
            cutoff_time = datetime.utcnow().replace(microsecond=0)
            cutoff_time = cutoff_time.replace(hour=cutoff_time.hour - max_age_hours)
            
            response = self._make_request('PATCH', 
                f'pdf_registry?hash_computation_status=eq.failed&hash_computed_at=lt.{cutoff_time.isoformat()}',
                {
                    'hash_computation_status': 'pending',
                    'hash_computed_at': None,
                    'updated_at': datetime.utcnow().isoformat()
                }
            )
            
            if response.ok:
                # Count affected rows (this is approximate)
                count_response = self._make_request('GET', 'pdf_registry', {
                    'hash_computation_status': 'eq.pending',
                    'select': 'count'
                })
                
                logger.info(f"Reset failed computations older than {max_age_hours} hours")
                return len(response.json()) if response.json() else 0
            else:
                logger.error(f"Failed to cleanup failed computations: {response.status_code}")
                return 0
                
        except Exception as e:
            logger.error(f"Error cleaning up failed computations: {e}")
            return 0

# Convenience functions
def create_service(supabase_url: str, supabase_key: str) -> DuplicateDetectionService:
    """Create a duplicate detection service instance"""
    return DuplicateDetectionService(supabase_url, supabase_key)

async def process_duplicates(supabase_url: str, supabase_key: str, **kwargs) -> Dict[str, Any]:
    """Process pending duplicate detections"""
    service = create_service(supabase_url, supabase_key)
    return await service.process_pending_hashes(**kwargs)
