"""
Unified Error Handling Module for NSE and BSE Scrapers

This module provides standardized error handling, logging, and recovery mechanisms
across both scrapers to improve reliability and maintainability.
"""

import sys
import traceback
import logging
from datetime import datetime
from typing import Optional, Dict, Any, Callable
from functools import wraps
import requests


class ScraperError(Exception):
    """Base exception class for scraper-related errors"""
    pass


class SessionError(ScraperError):
    """Exception for session-related errors"""
    pass


class DataFetchError(ScraperError):
    """Exception for data fetching errors"""
    pass


class DataParsingError(ScraperError):
    """Exception for data parsing errors"""
    pass


class DatabaseError(ScraperError):
    """Exception for database-related errors"""
    pass


class ErrorHandler:
    """
    Centralized error handling and logging for both scrapers.
    """
    
    def __init__(self, exchange_name: str, log_level: str = "INFO"):
        """
        Initialize error handler.
        
        Args:
            exchange_name (str): Name of the exchange for logging context
            log_level (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.exchange_name = exchange_name
        self.logger = self._setup_logger(log_level)
    
    def _setup_logger(self, log_level: str) -> logging.Logger:
        """Setup logger with appropriate formatting"""
        logger = logging.getLogger(f"{self.exchange_name.lower()}_scraper")
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Avoid duplicate handlers
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                f'%(asctime)s - {self.exchange_name} - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def handle_request_error(self, error: Exception, url: Optional[str] = None, 
                           context: str = "request") -> None:
        """
        Handle and log HTTP request errors consistently.
        
        Args:
            error (Exception): The exception that occurred
            url (Optional[str]): URL that caused the error
            context (str): Context description for the error
        """
        url_info = f" for URL: {url}" if url else ""
        
        if isinstance(error, requests.exceptions.Timeout):
            message = f"⏱️ {context.title()} timed out{url_info}"
            self.logger.warning(message)
            print(message)
        elif isinstance(error, requests.exceptions.ConnectionError):
            message = f"🔌 Connection error during {context}{url_info}"
            self.logger.error(message)
            print(message)
        elif isinstance(error, requests.exceptions.HTTPError):
            message = f"🌐 HTTP error during {context}: {error}{url_info}"
            self.logger.error(message)
            print(message)
        else:
            message = f"❌ Unexpected error during {context}: {error}{url_info}"
            self.logger.error(message)
            print(message)
    
    def handle_session_error(self, error: Exception, context: str = "session management") -> None:
        """Handle session-related errors"""
        message = f"🔐 Session error during {context}: {error}"
        self.logger.error(message)
        print(message)
    
    def handle_parsing_error(self, error: Exception, context: str = "data parsing") -> None:
        """Handle data parsing errors"""
        message = f"📊 Parsing error during {context}: {error}"
        self.logger.error(message)
        print(message)
    
    def handle_database_error(self, error: Exception, context: str = "database operation") -> None:
        """Handle database-related errors"""
        message = f"🗄️ Database error during {context}: {error}"
        self.logger.error(message)
        print(message)
    
    def log_success(self, message: str) -> None:
        """Log success messages"""
        success_message = f"✅ {message}"
        self.logger.info(success_message)
        print(success_message)
    
    def log_warning(self, message: str) -> None:
        """Log warning messages"""
        warning_message = f"⚠️ {message}"
        self.logger.warning(warning_message)
        print(warning_message)
    
    def log_info(self, message: str) -> None:
        """Log informational messages"""
        self.logger.info(message)
        print(message)


def with_error_handling(error_handler: ErrorHandler, error_type: type = ScraperError):
    """
    Decorator for adding consistent error handling to methods.
    
    Args:
        error_handler (ErrorHandler): Error handler instance
        error_type (type): Type of exception to catch and handle
    
    Returns:
        Decorator function
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except error_type as e:
                error_handler.handle_request_error(e, context=func.__name__)
                return None
            except Exception as e:
                error_handler.handle_request_error(e, context=f"unexpected error in {func.__name__}")
                return None
        return wrapper
    return decorator


class RetryHandler:
    """
    Retry mechanism for handling transient failures.
    """
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0, backoff_factor: float = 2.0):
        """
        Initialize retry handler.
        
        Args:
            max_retries (int): Maximum number of retry attempts
            delay (float): Initial delay between retries in seconds
            backoff_factor (float): Factor to multiply delay by after each retry
        """
        self.max_retries = max_retries
        self.delay = delay
        self.backoff_factor = backoff_factor
    
    def retry_on_failure(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with retry logic.
        
        Args:
            func (Callable): Function to execute
            *args: Arguments to pass to function
            **kwargs: Keyword arguments to pass to function
            
        Returns:
            Any: Result of successful function execution or None if all retries failed
        """
        last_exception = None
        current_delay = self.delay
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    print(f"🔄 Attempt {attempt + 1} failed, retrying in {current_delay:.1f}s...")
                    import time
                    time.sleep(current_delay)
                    current_delay *= self.backoff_factor
                else:
                    print(f"❌ All {self.max_retries + 1} attempts failed")
        
        # If we get here, all retries failed
        if last_exception:
            raise last_exception
        
        return None


class HealthChecker:
    """
    Health checking utilities for monitoring scraper status.
    """
    
    @staticmethod
    def check_internet_connection(timeout: int = 10) -> bool:
        """
        Check if internet connection is available.
        
        Args:
            timeout (int): Timeout in seconds
            
        Returns:
            bool: True if connection is available
        """
        try:
            response = requests.get("https://www.google.com", timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    @staticmethod
    def check_exchange_availability(base_url: str, timeout: int = 10) -> bool:
        """
        Check if exchange website is available.
        
        Args:
            base_url (str): Base URL of the exchange
            timeout (int): Timeout in seconds
            
        Returns:
            bool: True if exchange is available
        """
        try:
            response = requests.get(base_url, timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    @staticmethod
    def run_health_check(exchange_name: str, base_url: str) -> Dict[str, bool]:
        """
        Run comprehensive health check.
        
        Args:
            exchange_name (str): Name of the exchange
            base_url (str): Base URL of the exchange
            
        Returns:
            Dict[str, bool]: Health check results
        """
        print(f"🏥 Running health check for {exchange_name}...")
        
        results = {
            'internet_connection': HealthChecker.check_internet_connection(),
            'exchange_availability': HealthChecker.check_exchange_availability(base_url)
        }
        
        print(f"Internet connection: {'✅' if results['internet_connection'] else '❌'}")
        print(f"{exchange_name} availability: {'✅' if results['exchange_availability'] else '❌'}")
        
        return results


class GracefulShutdown:
    """
    Handles graceful shutdown of scraper operations.
    """
    
    def __init__(self):
        self.shutdown_requested = False
        self.cleanup_functions = []
    
    def register_cleanup(self, cleanup_func: Callable) -> None:
        """
        Register a cleanup function to be called on shutdown.
        
        Args:
            cleanup_func (Callable): Function to call during cleanup
        """
        self.cleanup_functions.append(cleanup_func)
    
    def request_shutdown(self) -> None:
        """Request graceful shutdown"""
        self.shutdown_requested = True
        print("🛑 Graceful shutdown requested...")
    
    def cleanup(self) -> None:
        """Execute all registered cleanup functions"""
        print("🧹 Performing cleanup...")
        
        for cleanup_func in self.cleanup_functions:
            try:
                cleanup_func()
            except Exception as e:
                print(f"⚠️ Error during cleanup: {e}")
        
        print("✅ Cleanup completed")
    
    def is_shutdown_requested(self) -> bool:
        """Check if shutdown has been requested"""
        return self.shutdown_requested
