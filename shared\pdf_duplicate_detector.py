"""
PDF Duplicate Detection Service
Handles PDF hash computation and duplicate detection for corporate announcements.
"""

import hashlib
import requests
import asyncio
import aiohttp
import logging
from typing import Optional, Dict, List, Tuple, Union
from datetime import datetime
import time
from urllib.parse import urlparse
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFDuplicateDetector:
    """Service for detecting duplicate PDFs using SHA-256 hashing"""
    
    def __init__(self, timeout: int = 30, max_retries: int = 3, retry_delay: int = 2):
        """
        Initialize the PDF duplicate detector
        
        Args:
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Headers for PDF requests (similar to compare_pdfs.ts)
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/pdf,*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def compute_pdf_hash_sync(self, pdf_url: str) -> Optional[str]:
        """
        Compute SHA-256 hash of PDF from URL (synchronous version)
        
        Args:
            pdf_url: URL of the PDF to hash
            
        Returns:
            SHA-256 hash as hex string, or None if failed
        """
        if not pdf_url or not pdf_url.strip():
            return None
            
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Fetching PDF (attempt {attempt + 1}): {pdf_url}")
                
                response = requests.get(
                    pdf_url,
                    headers=self.headers,
                    timeout=self.timeout,
                    stream=True  # Stream to handle large files
                )
                
                if not response.ok:
                    logger.warning(f"HTTP {response.status_code} for {pdf_url}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    return None
                
                # Compute hash incrementally for memory efficiency
                hash_obj = hashlib.sha256()
                content_length = response.headers.get('content-length')
                
                if content_length:
                    logger.info(f"PDF size: {content_length} bytes")
                
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        hash_obj.update(chunk)
                
                pdf_hash = hash_obj.hexdigest()
                logger.info(f"Computed hash for {pdf_url}: {pdf_hash}")
                return pdf_hash
                
            except requests.exceptions.RequestException as e:
                logger.error(f"Request error for {pdf_url} (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                return None
            except Exception as e:
                logger.error(f"Unexpected error for {pdf_url}: {e}")
                return None
        
        return None
    
    async def compute_pdf_hash_async(self, pdf_url: str, session: aiohttp.ClientSession) -> Optional[str]:
        """
        Compute SHA-256 hash of PDF from URL (asynchronous version)
        
        Args:
            pdf_url: URL of the PDF to hash
            session: aiohttp session
            
        Returns:
            SHA-256 hash as hex string, or None if failed
        """
        if not pdf_url or not pdf_url.strip():
            return None
            
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Fetching PDF async (attempt {attempt + 1}): {pdf_url}")
                
                async with session.get(
                    pdf_url,
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    
                    if response.status != 200:
                        logger.warning(f"HTTP {response.status} for {pdf_url}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay)
                            continue
                        return None
                    
                    # Compute hash incrementally
                    hash_obj = hashlib.sha256()
                    content_length = response.headers.get('content-length')
                    
                    if content_length:
                        logger.info(f"PDF size: {content_length} bytes")
                    
                    async for chunk in response.content.iter_chunked(8192):
                        hash_obj.update(chunk)
                    
                    pdf_hash = hash_obj.hexdigest()
                    logger.info(f"Computed hash for {pdf_url}: {pdf_hash}")
                    return pdf_hash
                    
            except asyncio.TimeoutError:
                logger.error(f"Timeout for {pdf_url} (attempt {attempt + 1})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                    continue
                return None
            except Exception as e:
                logger.error(f"Error for {pdf_url} (attempt {attempt + 1}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(self.retry_delay)
                    continue
                return None
        
        return None
    
    async def compute_batch_hashes(self, pdf_urls: List[str], max_concurrent: int = 5) -> Dict[str, Optional[str]]:
        """
        Compute hashes for multiple PDFs concurrently
        
        Args:
            pdf_urls: List of PDF URLs to process
            max_concurrent: Maximum concurrent requests
            
        Returns:
            Dictionary mapping URLs to their hashes (or None if failed)
        """
        results = {}
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_url(url: str, session: aiohttp.ClientSession) -> Tuple[str, Optional[str]]:
            async with semaphore:
                hash_result = await self.compute_pdf_hash_async(url, session)
                return url, hash_result
        
        # Process URLs in batches
        connector = aiohttp.TCPConnector(limit=max_concurrent, limit_per_host=max_concurrent)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            tasks = [process_url(url, session) for url in pdf_urls if url and url.strip()]
            
            # Process with progress logging
            completed = 0
            total = len(tasks)
            
            for coro in asyncio.as_completed(tasks):
                url, hash_result = await coro
                results[url] = hash_result
                completed += 1
                
                if completed % 10 == 0 or completed == total:
                    logger.info(f"Processed {completed}/{total} PDFs")
        
        return results
    
    def validate_pdf_url(self, url: str) -> bool:
        """
        Validate if URL looks like a PDF URL
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL appears to be a PDF
        """
        if not url or not url.strip():
            return False
        
        try:
            parsed = urlparse(url.strip())
            if not parsed.scheme or not parsed.netloc:
                return False
            
            # Check if URL ends with .pdf or contains pdf in path
            path_lower = parsed.path.lower()
            return path_lower.endswith('.pdf') or 'pdf' in path_lower
            
        except Exception:
            return False
    
    def get_processing_stats(self, results: Dict[str, Optional[str]]) -> Dict[str, int]:
        """
        Get statistics from batch processing results
        
        Args:
            results: Results from compute_batch_hashes
            
        Returns:
            Dictionary with processing statistics
        """
        total = len(results)
        successful = sum(1 for hash_val in results.values() if hash_val is not None)
        failed = total - successful
        
        return {
            'total_processed': total,
            'successful': successful,
            'failed': failed,
            'success_rate': (successful / total * 100) if total > 0 else 0
        }

# Utility functions for easy integration
def compute_pdf_hash(pdf_url: str, **kwargs) -> Optional[str]:
    """
    Convenience function to compute a single PDF hash
    
    Args:
        pdf_url: URL of the PDF
        **kwargs: Additional arguments for PDFDuplicateDetector
        
    Returns:
        SHA-256 hash or None if failed
    """
    detector = PDFDuplicateDetector(**kwargs)
    return detector.compute_pdf_hash_sync(pdf_url)

async def compute_pdf_hashes_batch(pdf_urls: List[str], **kwargs) -> Dict[str, Optional[str]]:
    """
    Convenience function to compute multiple PDF hashes
    
    Args:
        pdf_urls: List of PDF URLs
        **kwargs: Additional arguments for PDFDuplicateDetector
        
    Returns:
        Dictionary mapping URLs to hashes
    """
    detector = PDFDuplicateDetector(**kwargs)
    return await detector.compute_batch_hashes(pdf_urls)

# Example usage
if __name__ == "__main__":
    # Test with sample URLs
    test_urls = [
        "https://nsearchives.nseindia.com/corporate/ICICIPRULI_15072025134258_Outcome_BM_July_15_2025_Financials_.pdf",
        "https://nsearchives.nseindia.com/corporate/MANKIND2_14072025222935_BMIntimation14072025.pdf"
    ]
    
    # Synchronous example
    detector = PDFDuplicateDetector()
    for url in test_urls:
        hash_result = detector.compute_pdf_hash_sync(url)
        print(f"URL: {url}")
        print(f"Hash: {hash_result}")
        print("-" * 80)
    
    # Asynchronous example
    async def test_async():
        results = await compute_pdf_hashes_batch(test_urls)
        for url, hash_result in results.items():
            print(f"URL: {url}")
            print(f"Hash: {hash_result}")
            print("-" * 80)
    
    # Uncomment to run async test
    # asyncio.run(test_async())
