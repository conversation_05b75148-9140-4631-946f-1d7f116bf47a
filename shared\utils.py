"""
Shared utility functions for NSE and BSE scrapers.

This module contains common functionality used by both scrapers to eliminate
code duplication and provide consistent behavior across exchanges.

Note: This module is being refactored. New functionality should be added to:
- shared/cli_framework.py for CLI-related utilities
- shared/data_processor.py for data processing utilities
- shared/error_handler.py for error handling utilities
"""

import time
import random
import os
import requests
import hashlib
from datetime import datetime


def get_random_user_agent():
    """
    Get a random user agent string for HTTP requests.
    
    Returns:
        str: Random user agent string
    """
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
    ]
    return random.choice(user_agents)


def add_random_delay(min_seconds=1, max_seconds=3):
    """
    Add a random delay to avoid being detected as a bot.
    
    Args:
        min_seconds (float): Minimum delay in seconds
        max_seconds (float): Maximum delay in seconds
    """
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


def handle_request_error(error, url=None, context="request"):
    """
    Handle and log request errors consistently.
    
    Args:
        error (Exception): The exception that occurred
        url (str, optional): URL that caused the error
        context (str): Context description for the error
        
    Returns:
        None
    """
    if isinstance(error, requests.exceptions.Timeout):
        print(f"⏱️ {context.title()} timed out" + (f" for URL: {url}" if url else ""))
    elif isinstance(error, requests.exceptions.ConnectionError):
        print(f"🔌 Connection error during {context}" + (f" for URL: {url}" if url else ""))
    elif isinstance(error, requests.exceptions.HTTPError):
        print(f"🌐 HTTP error during {context}: {error}" + (f" for URL: {url}" if url else ""))
    else:
        print(f"❌ Unexpected error during {context}: {error}" + (f" for URL: {url}" if url else ""))


def format_date_for_display(date_obj):
    """
    Format a datetime object for consistent display across scrapers.
    
    Args:
        date_obj (datetime): Date to format
        
    Returns:
        str: Formatted date string
    """
    if isinstance(date_obj, datetime):
        return date_obj.strftime('%Y-%m-%d')
    return str(date_obj)


def create_output_directory(directory_path):
    """
    Create output directory if it doesn't exist.
    
    Args:
        directory_path (str): Path to the directory to create
        
    Returns:
        bool: True if directory exists or was created successfully
    """
    try:
        if not os.path.exists(directory_path):
            os.makedirs(directory_path)
            print(f"📁 Created output directory: {directory_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to create directory {directory_path}: {e}")
        return False


def get_common_headers(exchange_name="", referer_url=""):
    """
    Get common HTTP headers used by both scrapers.
    
    Args:
        exchange_name (str): Name of the exchange for logging
        referer_url (str): Referer URL for the request
        
    Returns:
        dict: Common HTTP headers
    """
    headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'max-age=0',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    
    if referer_url:
        headers['Referer'] = referer_url
        
    return headers


def get_api_headers(exchange_name="", referer_url=""):
    """
    Get API-specific headers for JSON requests.
    
    Args:
        exchange_name (str): Name of the exchange for logging
        referer_url (str): Referer URL for the request
        
    Returns:
        dict: API-specific HTTP headers
    """
    headers = {
        'User-Agent': get_random_user_agent(),
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest',
    }
    
    if referer_url:
        headers['Referer'] = referer_url
        
    return headers


def validate_date_range(start_date, end_date):
    """
    Validate that date range is logical.
    
    Args:
        start_date (datetime): Start date
        end_date (datetime): End date
        
    Returns:
        bool: True if date range is valid
    """
    if start_date > end_date:
        print(f"❌ Invalid date range: start date {format_date_for_display(start_date)} is after end date {format_date_for_display(end_date)}")
        return False
    
    if (end_date - start_date).days > 365:
        print(f"⚠️ Warning: Large date range ({(end_date - start_date).days} days) may cause performance issues")
    
    return True


def log_session_establishment(exchange_name, success=True):
    """
    Log session establishment status consistently.
    
    Args:
        exchange_name (str): Name of the exchange
        success (bool): Whether session establishment was successful
    """
    if success:
        print(f"✅ {exchange_name} session establishment successful")
    else:
        print(f"❌ {exchange_name} session establishment failed")


def log_request_status(url, status_code, exchange_name=""):
    """
    Log HTTP request status consistently.

    Args:
        url (str): URL that was requested
        status_code (int): HTTP status code
        exchange_name (str): Name of the exchange for context
    """
    exchange_prefix = f"{exchange_name} " if exchange_name else ""
    print(f"{exchange_prefix}Request to {url}: {status_code}")


def calculate_unified_hash(exchange_type, **kwargs):
    """
    Calculate unified hash for insider trading records using consistent algorithm.

    This function replaces database-level hash calculation with Python implementation
    for better control and consistency across both exchanges.

    STANDARDIZED HASH FORMAT: security_code|person_name|securities_number|cross_reference_code

    Args:
        exchange_type (str): 'nse' or 'bse' to determine field mapping
        **kwargs: Field values for hash calculation

    For NSE:
        - symbol (str): NSE symbol (used as security_code)
        - name_of_the_acquirer_disposer (str): Name of the acquirer/disposer (person_name)
        - no_of_securities_acquired_displosed (str): Number of securities
        - value_of_security_acquired_displosed (str): Value of securities (NOT USED IN HASH)
        - bse_code (str, optional): BSE code for cross-reference

    For BSE:
        - security_code (str): BSE security code
        - name_of_person (str): Name of the person
        - securities_acquired_disposed_number (str): Number of securities
        - securities_acquired_disposed_value (str): Value of securities (NOT USED IN HASH)
        - nse_code (str, optional): NSE code for cross-reference

    Hash Format (Both Exchanges):
        security_code|person_name|securities_number|cross_reference_code

    Returns:
        str: SHA256 hash string for duplicate detection

    Raises:
        ValueError: If required fields are missing or exchange_type is invalid
    """
    try:
        if exchange_type.lower() == 'nse':
            # NSE hash inputs: symbol + name_of_the_acquirer_disposer + no_of_securities + bse_code
            # Note: value_of_security_acquired_displosed is commented out from hash calculation
            # Format: security_code|person_name|securities_number|cross_reference_code (same as BSE)
            symbol = kwargs.get('symbol', '')
            name_of_the_acquirer_disposer = kwargs.get('name_of_the_acquirer_disposer', '')
            no_of_securities = kwargs.get('no_of_securities_acquired_displosed', '')
            # value = kwargs.get('value_of_security_acquired_displosed', '')  # Commented out from hash
            bse_code = kwargs.get('bse_code', '')

            # Normalize inputs: lowercase and remove all spaces
            normalized_symbol = str(symbol or '').lower().replace(' ', '')
            normalized_name = str(name_of_the_acquirer_disposer or '').lower().replace(' ', '')
            normalized_number = str(no_of_securities or '').lower().replace(' ', '')
            # normalized_value = str(value or '').lower().replace(' ', '')  # Commented out from hash
            normalized_bse_code = str(bse_code or '').lower().replace(' ', '')

            # Create hash input with standardized format: security_code|person_name|securities_number|cross_reference_code
            hash_input = f"{normalized_bse_code}|{normalized_name}|{normalized_number}|{normalized_symbol}"

        elif exchange_type.lower() == 'bse':
            # BSE hash inputs: security_code + name_of_person + number + nse_code
            # Note: securities_acquired_disposed_value is commented out from hash calculation
            # Format: security_code|person_name|securities_number|cross_reference_code (standardized)
            security_code = kwargs.get('security_code', '')
            name_of_person = kwargs.get('name_of_person', '')
            number = kwargs.get('securities_acquired_disposed_number', '')
            # value = kwargs.get('securities_acquired_disposed_value', '')  # Commented out from hash
            nse_code = kwargs.get('nse_code', '')

            # Normalize inputs: lowercase and remove all spaces
            normalized_security_code = str(security_code or '').lower().replace(' ', '')
            normalized_name = str(name_of_person or '').lower().replace(' ', '')
            normalized_number = str(number or '').lower().replace(' ', '')
            # normalized_value = str(value or '').lower().replace(' ', '')  # Commented out from hash
            normalized_nse_code = str(nse_code or '').lower().replace(' ', '')

            # Create hash input with standardized format: security_code|person_name|securities_number|cross_reference_code
            hash_input = f"{normalized_security_code}|{normalized_name}|{normalized_number}|{normalized_nse_code}"

        else:
            raise ValueError(f"Invalid exchange_type: {exchange_type}. Must be 'nse' or 'bse'")

        # Generate SHA256 hash for better collision resistance
        hash_object = hashlib.sha256(hash_input.encode('utf-8'))
        result_hash = hash_object.hexdigest()

        return result_hash

    except Exception as e:
        # Generate fallback hash to prevent complete failure
        import time
        fallback_input = f"{exchange_type.upper()}_ERROR_{int(time.time())}_{str(e)[:50]}"
        fallback_hash = hashlib.sha256(fallback_input.encode('utf-8')).hexdigest()
        print(f"⚠️ Error calculating hash for {exchange_type}: {e}. Using fallback hash.")
        return fallback_hash


def fetch_cross_reference_code(exchange_type, code, supabase_url, supabase_key, max_retries=3, retry_delay=2):
    """
    Fetch cross-reference exchange code using REST API call to master_company_details.
    Includes retry mechanism with exponential backoff for better reliability.

    Args:
        exchange_type (str): 'nse' or 'bse' - the source exchange
        code (str): The code to look up (NSE symbol or BSE security code)
        supabase_url (str): Supabase project URL
        supabase_key (str): Supabase anon key
        max_retries (int): Maximum number of retry attempts (default: 3)
        retry_delay (int): Initial delay between retries in seconds (default: 2)

    Returns:
        str or None: Cross-reference code if found, None otherwise
    """
    import time

    if not code or not code.strip():
        return None

    headers = {
        'apikey': supabase_key,
        'Authorization': f'Bearer {supabase_key}',
        'Content-Type': 'application/json'
    }

    if exchange_type.lower() == 'nse':
        # NSE -> BSE: Look up BSE code using NSE symbol
        url = f"{supabase_url}/rest/v1/master_company_details"
        params = {
            'select': 'bse_code',
            'nse_code': f'eq.{code.strip()}',
            'bse_code': 'not.is.null',
            'limit': 1
        }
    elif exchange_type.lower() == 'bse':
        # BSE -> NSE: Look up NSE code using BSE security code
        url = f"{supabase_url}/rest/v1/master_company_details"
        params = {
            'select': 'nse_code',
            'bse_code': f'eq.{code.strip()}',
            'nse_code': 'not.is.null',
            'limit': 1
        }
    else:
        print(f"❌ Invalid exchange_type: {exchange_type}")
        return None

    # Retry mechanism with exponential backoff
    for attempt in range(max_retries + 1):
        try:
            if attempt > 0:
                delay = retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                print(f"🔄 Retrying cross-reference lookup for {exchange_type} code {code} (attempt {attempt + 1}/{max_retries + 1}) after {delay}s delay...")
                time.sleep(delay)

            response = requests.get(url, headers=headers, params=params, timeout=45)

            if response.status_code == 200:
                data = response.json()
                if data and len(data) > 0:
                    result = data[0].get('bse_code') if exchange_type.lower() == 'nse' else data[0].get('nse_code')
                    if attempt > 0:
                        print(f"✅ Cross-reference lookup succeeded on attempt {attempt + 1}")
                    return result
                else:
                    # No data found - this is not a retry-able error
                    return None
            else:
                print(f"⚠️ API call failed for {exchange_type} code {code}: {response.status_code}")
                if attempt == max_retries:
                    break
                continue

        except requests.exceptions.Timeout:
            if attempt == max_retries:
                print(f"⏱️ Final timeout fetching cross-reference for {exchange_type} code {code} after {max_retries + 1} attempts")
            else:
                print(f"⏱️ Timeout fetching cross-reference for {exchange_type} code {code} (attempt {attempt + 1})")
            if attempt == max_retries:
                break
            continue
        except requests.exceptions.RequestException as e:
            if attempt == max_retries:
                print(f"🔌 Final request error fetching cross-reference for {exchange_type} code {code}: {e}")
            else:
                print(f"🔌 Request error fetching cross-reference for {exchange_type} code {code} (attempt {attempt + 1}): {e}")
            if attempt == max_retries:
                break
            continue
        except Exception as e:
            print(f"❌ Unexpected error fetching cross-reference for {exchange_type} code {code}: {e}")
            break

    return None
