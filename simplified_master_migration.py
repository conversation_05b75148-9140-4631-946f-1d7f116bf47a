#!/usr/bin/env python3
"""
Simplified Master Corporate Announcements Migration
Works with existing table structure and migrates data from NSE and BSE tables
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

class SimplifiedMasterMigration:
    """Simplified migration for master corporate announcements"""
    
    def __init__(self):
        self.headers = headers
        
        # Existing columns in master table (based on test results)
        self.master_columns = [
            'id', 'pdf_hash', 'source_exchange', 'has_nse_data', 'has_bse_data',
            'created_at', 'updated_at', 'nse_source_id', 'bse_source_id',
            'company_name', 'nse_symbol', 'bse_code', 'subject', 'headline',
            'details', 'category', 'subcategory', 'classification', 'parsed_subject'
        ]
    
    def map_nse_record(self, nse_record: Dict) -> Dict:
        """Map NSE record to master table format using existing columns only"""
        
        master_record = {
            'pdf_hash': nse_record.get('pdf_hash'),
            'source_exchange': 'NSE',
            'has_nse_data': True,
            'has_bse_data': False,
            'nse_source_id': nse_record.get('id'),
            'company_name': nse_record.get('company_name'),
            'nse_symbol': nse_record.get('symbol'),
            'subject': nse_record.get('subject'),
            'headline': nse_record.get('subject'),  # Use subject as headline for NSE
            'details': nse_record.get('details')
        }
        
        # Remove None values
        return {k: v for k, v in master_record.items() if v is not None}
    
    def map_bse_record(self, bse_record: Dict) -> Dict:
        """Map BSE record to master table format using existing columns only"""
        
        master_record = {
            'pdf_hash': bse_record.get('pdf_hash'),
            'source_exchange': 'BSE',
            'has_nse_data': False,
            'has_bse_data': True,
            'bse_source_id': bse_record.get('id'),
            'company_name': bse_record.get('company_name'),
            'bse_code': bse_record.get('bse_code'),
            'subject': bse_record.get('subject'),
            'headline': bse_record.get('headline'),
            'details': bse_record.get('more'),  # BSE 'more' field maps to details
            'category': bse_record.get('categoryname'),
            'subcategory': bse_record.get('subcatname'),
            'classification': bse_record.get('classification'),
            'parsed_subject': bse_record.get('parsed_subject')
        }
        
        # Remove None values
        return {k: v for k, v in master_record.items() if v is not None}
    
    def get_existing_record(self, pdf_hash: str) -> Optional[Dict]:
        """Check if record with pdf_hash already exists"""
        
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                headers=self.headers,
                params={'pdf_hash': f'eq.{pdf_hash}', 'limit': 1},
                timeout=30
            )
            
            if response.status_code == 200:
                results = response.json()
                return results[0] if results else None
            else:
                print(f"❌ Error checking existing record: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return None
    
    def insert_record(self, record: Dict) -> bool:
        """Insert new record into master table"""
        
        try:
            response = requests.post(
                f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                headers=self.headers,
                json=record,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                return True
            else:
                print(f"❌ Insert failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error inserting record: {e}")
            return False
    
    def update_record(self, pdf_hash: str, update_data: Dict) -> bool:
        """Update existing record"""
        
        try:
            response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                headers=self.headers,
                params={'pdf_hash': f'eq.{pdf_hash}'},
                json=update_data,
                timeout=30
            )
            
            if response.status_code in [200, 204]:
                return True
            else:
                print(f"❌ Update failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating record: {e}")
            return False
    
    def upsert_record(self, record: Dict) -> bool:
        """Upsert record with merge logic"""
        
        pdf_hash = record.get('pdf_hash')
        if not pdf_hash:
            print("❌ No pdf_hash provided")
            return False
        
        existing = self.get_existing_record(pdf_hash)
        
        if existing:
            # Merge logic: only update empty/null fields
            update_data = {}
            
            for key, value in record.items():
                if key in ['id', 'pdf_hash', 'created_at']:
                    continue
                
                existing_value = existing.get(key)
                if existing_value is None or (isinstance(existing_value, str) and existing_value.strip() == ''):
                    if value is not None and str(value).strip() != '':
                        update_data[key] = value
            
            # Update source tracking flags
            if record.get('source_exchange') == 'NSE':
                update_data['has_nse_data'] = True
                if not existing.get('nse_source_id'):
                    update_data['nse_source_id'] = record.get('nse_source_id')
            elif record.get('source_exchange') == 'BSE':
                update_data['has_bse_data'] = True
                if not existing.get('bse_source_id'):
                    update_data['bse_source_id'] = record.get('bse_source_id')
            
            if update_data:
                update_data['updated_at'] = datetime.now().isoformat()
                print(f"🔄 Updating existing record with {len(update_data)} fields")
                return self.update_record(pdf_hash, update_data)
            else:
                print("ℹ️ No fields to update")
                return True
        else:
            # Insert new record
            print("➕ Inserting new record")
            return self.insert_record(record)
    
    def migrate_nse_data(self, batch_size: int = 50) -> int:
        """Migrate NSE data to master table"""
        
        print("📊 Migrating NSE data...")
        migrated_count = 0
        offset = 0
        
        while True:
            try:
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements",
                    headers=self.headers,
                    params={
                        'is_duplicate': 'eq.false',
                        'duplicate_check_status': 'eq.completed',
                        'pdf_hash': 'not.is.null',
                        'limit': batch_size,
                        'offset': offset,
                        'order': 'created_at'
                    },
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"❌ Error fetching NSE data: {response.status_code}")
                    break
                
                records = response.json()
                if not records:
                    break
                
                print(f"Processing NSE batch: {offset + 1} to {offset + len(records)}")
                
                batch_success = 0
                for record in records:
                    master_record = self.map_nse_record(record)
                    if self.upsert_record(master_record):
                        batch_success += 1
                
                migrated_count += batch_success
                offset += len(records)
                
                print(f"✅ Processed {batch_success}/{len(records)} records in batch")
                
                if len(records) < batch_size:
                    break
                    
            except Exception as e:
                print(f"❌ Error in NSE migration: {e}")
                break
        
        return migrated_count
    
    def migrate_bse_data(self, batch_size: int = 50) -> int:
        """Migrate BSE data to master table"""
        
        print("📊 Migrating BSE data...")
        migrated_count = 0
        offset = 0
        
        while True:
            try:
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements",
                    headers=self.headers,
                    params={
                        'is_duplicate': 'eq.false',
                        'duplicate_check_status': 'eq.completed',
                        'pdf_hash': 'not.is.null',
                        'limit': batch_size,
                        'offset': offset,
                        'order': 'created_at'
                    },
                    timeout=30
                )
                
                if response.status_code != 200:
                    print(f"❌ Error fetching BSE data: {response.status_code}")
                    break
                
                records = response.json()
                if not records:
                    break
                
                print(f"Processing BSE batch: {offset + 1} to {offset + len(records)}")
                
                batch_success = 0
                for record in records:
                    master_record = self.map_bse_record(record)
                    if self.upsert_record(master_record):
                        batch_success += 1
                
                migrated_count += batch_success
                offset += len(records)
                
                print(f"✅ Processed {batch_success}/{len(records)} records in batch")
                
                if len(records) < batch_size:
                    break
                    
            except Exception as e:
                print(f"❌ Error in BSE migration: {e}")
                break
        
        return migrated_count
    
    def get_master_stats(self) -> Dict:
        """Get statistics about the master table"""
        
        try:
            # Total records
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                headers=self.headers,
                params={'select': 'count'},
                timeout=30
            )
            
            stats = {'total_records': 0, 'nse_records': 0, 'bse_records': 0, 'merged_records': 0}
            
            if response.status_code == 200:
                stats['total_records'] = len(response.json())
                
                # NSE records
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                    headers=self.headers,
                    params={'has_nse_data': 'eq.true', 'select': 'count'},
                    timeout=30
                )
                if response.status_code == 200:
                    stats['nse_records'] = len(response.json())
                
                # BSE records
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                    headers=self.headers,
                    params={'has_bse_data': 'eq.true', 'select': 'count'},
                    timeout=30
                )
                if response.status_code == 200:
                    stats['bse_records'] = len(response.json())
                
                # Merged records (both NSE and BSE)
                response = requests.get(
                    f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
                    headers=self.headers,
                    params={'has_nse_data': 'eq.true', 'has_bse_data': 'eq.true', 'select': 'count'},
                    timeout=30
                )
                if response.status_code == 200:
                    stats['merged_records'] = len(response.json())
            
            return stats
            
        except Exception as e:
            print(f"❌ Error getting stats: {e}")
            return {}


def main():
    """Main migration function"""
    
    print("🏢 Simplified Master Corporate Announcements Migration")
    print("=" * 60)
    
    migrator = SimplifiedMasterMigration()
    
    # Get initial stats
    print("📊 Initial master table statistics...")
    initial_stats = migrator.get_master_stats()
    print(f"   Total records: {initial_stats.get('total_records', 0)}")
    print(f"   NSE records: {initial_stats.get('nse_records', 0)}")
    print(f"   BSE records: {initial_stats.get('bse_records', 0)}")
    print(f"   Merged records: {initial_stats.get('merged_records', 0)}")
    
    # Migrate NSE data
    print("\n1️⃣ Migrating NSE data...")
    nse_migrated = migrator.migrate_nse_data()
    print(f"✅ NSE migration completed: {nse_migrated} records")
    
    # Migrate BSE data
    print("\n2️⃣ Migrating BSE data...")
    bse_migrated = migrator.migrate_bse_data()
    print(f"✅ BSE migration completed: {bse_migrated} records")
    
    # Get final stats
    print("\n📊 Final master table statistics...")
    final_stats = migrator.get_master_stats()
    print(f"   Total records: {final_stats.get('total_records', 0)}")
    print(f"   NSE records: {final_stats.get('nse_records', 0)}")
    print(f"   BSE records: {final_stats.get('bse_records', 0)}")
    print(f"   Merged records: {final_stats.get('merged_records', 0)}")
    
    print(f"\n🎉 Migration completed!")
    print(f"📊 Total records processed: {nse_migrated + bse_migrated}")
    print(f"📊 Net new records in master table: {final_stats.get('total_records', 0) - initial_stats.get('total_records', 0)}")


if __name__ == "__main__":
    main()
