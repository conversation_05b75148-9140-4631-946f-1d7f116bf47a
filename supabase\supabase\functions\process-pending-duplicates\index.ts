import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface BatchProcessRequest {
  batch_size?: number
  max_concurrent?: number
  table_filter?: 'bse_corporate_announcements' | 'nse_corporate_announcements' | 'both'
}

interface BatchProcessResponse {
  success: boolean
  total_processed: number
  successful: number
  failed: number
  processing_time_ms: number
  details?: {
    bse_processed: number
    nse_processed: number
  }
  error?: string
}

interface PendingRecord {
  id: string
  table_name: string
  pdf_url: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const requestBody: BatchProcessRequest = await req.json().catch(() => ({}))
    const { 
      batch_size = 50, 
      max_concurrent = 5,
      table_filter = 'both'
    } = requestBody

    console.log(`Starting batch processing: batch_size=${batch_size}, max_concurrent=${max_concurrent}, table_filter=${table_filter}`)

    // Get pending records
    const pendingRecords = await getPendingRecords(supabaseClient, batch_size, table_filter)
    
    if (pendingRecords.length === 0) {
      console.log('No pending records found')
      return new Response(
        JSON.stringify({
          success: true,
          total_processed: 0,
          successful: 0,
          failed: 0,
          processing_time_ms: Date.now() - startTime,
          message: 'No pending records to process'
        } as BatchProcessResponse),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200
        }
      )
    }

    console.log(`Found ${pendingRecords.length} pending records`)

    // Process records with concurrency control
    const results = await processRecordsConcurrently(pendingRecords, max_concurrent)

    // Calculate statistics
    const successful = results.filter(r => r.status === 'fulfilled').length
    const failed = results.filter(r => r.status === 'rejected').length
    
    const bseProcessed = results.filter((r, i) => 
      r.status === 'fulfilled' && pendingRecords[i].table_name === 'bse_corporate_announcements'
    ).length
    
    const nseProcessed = results.filter((r, i) => 
      r.status === 'fulfilled' && pendingRecords[i].table_name === 'nse_corporate_announcements'
    ).length

    const processingTime = Date.now() - startTime

    console.log(`Batch processing completed: ${successful} successful, ${failed} failed in ${processingTime}ms`)

    const response: BatchProcessResponse = {
      success: true,
      total_processed: pendingRecords.length,
      successful,
      failed,
      processing_time_ms: processingTime,
      details: {
        bse_processed: bseProcessed,
        nse_processed: nseProcessed
      }
    }

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error) {
    console.error('Error in batch processing:', error)
    
    const errorResponse: BatchProcessResponse = {
      success: false,
      total_processed: 0,
      successful: 0,
      failed: 0,
      processing_time_ms: Date.now() - startTime,
      error: error.message
    }

    return new Response(
      JSON.stringify(errorResponse),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})

async function getPendingRecords(
  supabaseClient: any,
  batchSize: number,
  tableFilter: string
): Promise<PendingRecord[]> {
  const pendingRecords: PendingRecord[] = []

  try {
    // Get pending BSE records
    if (tableFilter === 'both' || tableFilter === 'bse_corporate_announcements') {
      const { data: bseData, error: bseError } = await supabaseClient
        .from('bse_corporate_announcements')
        .select('id, attachmentfile')
        .eq('duplicate_check_status', 'pending')
        .not('attachmentfile', 'is', null)
        .neq('attachmentfile', '')
        .limit(Math.floor(batchSize / 2))
        .order('created_at', { ascending: true })

      if (bseError) {
        console.error('Error fetching BSE pending records:', bseError)
      } else if (bseData) {
        bseData.forEach(record => {
          if (record.attachmentfile && record.attachmentfile.trim() !== '') {
            pendingRecords.push({
              id: record.id,
              table_name: 'bse_corporate_announcements',
              pdf_url: record.attachmentfile
            })
          }
        })
      }
    }

    // Get pending NSE records
    if (tableFilter === 'both' || tableFilter === 'nse_corporate_announcements') {
      const remainingBatchSize = batchSize - pendingRecords.length
      
      if (remainingBatchSize > 0) {
        const { data: nseData, error: nseError } = await supabaseClient
          .from('nse_corporate_announcements')
          .select('id, attachment_url')
          .eq('duplicate_check_status', 'pending')
          .not('attachment_url', 'is', null)
          .neq('attachment_url', '')
          .limit(remainingBatchSize)
          .order('created_at', { ascending: true })

        if (nseError) {
          console.error('Error fetching NSE pending records:', nseError)
        } else if (nseData) {
          nseData.forEach(record => {
            if (record.attachment_url && record.attachment_url.trim() !== '') {
              pendingRecords.push({
                id: record.id,
                table_name: 'nse_corporate_announcements',
                pdf_url: record.attachment_url
              })
            }
          })
        }
      }
    }

    console.log(`Retrieved ${pendingRecords.length} pending records`)
    return pendingRecords

  } catch (error) {
    console.error('Error getting pending records:', error)
    return []
  }
}

async function processRecordsConcurrently(
  records: PendingRecord[],
  maxConcurrent: number
): Promise<PromiseSettledResult<any>[]> {
  const results: PromiseSettledResult<any>[] = []
  
  // Process records in chunks to control concurrency
  for (let i = 0; i < records.length; i += maxConcurrent) {
    const chunk = records.slice(i, i + maxConcurrent)
    
    console.log(`Processing chunk ${Math.floor(i / maxConcurrent) + 1}: ${chunk.length} records`)
    
    const chunkPromises = chunk.map(record => processSingleRecord(record))
    const chunkResults = await Promise.allSettled(chunkPromises)
    
    results.push(...chunkResults)
    
    // Small delay between chunks to avoid overwhelming the system
    if (i + maxConcurrent < records.length) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }
  
  return results
}

async function processSingleRecord(record: PendingRecord): Promise<any> {
  try {
    console.log(`Processing ${record.table_name}:${record.id}`)
    
    // Call the main duplicate detector function
    const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/pdf-duplicate-detector`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
      },
      body: JSON.stringify({
        record_id: record.id,
        table_name: record.table_name,
        pdf_url: record.pdf_url
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.error || 'Unknown error')
    }

    console.log(`Successfully processed ${record.table_name}:${record.id}`)
    return result

  } catch (error) {
    console.error(`Failed to process ${record.table_name}:${record.id}:`, error)
    throw error
  }
}
