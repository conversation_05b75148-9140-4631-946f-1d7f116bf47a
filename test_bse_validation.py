#!/usr/bin/env python3
"""
Test script to verify BSE scraper validation fixes.
This script tests the validation logic to ensure malformed records are filtered out.
"""

import sys
import os
import pandas as pd
from datetime import datetime, timed<PERSON>ta

# Add the bse_scraper directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'bse_scraper'))

def test_validation_logic():
    """Test the validation logic with sample data"""
    print("🧪 Testing BSE scraper validation logic...")
    
    try:
        from scraper import BSEScraper
        
        # Create a test scraper instance
        scraper = BSEScraper()
        
        # Create test DataFrame with both valid and invalid records
        test_data = {
            'Security Code': ['544151', '544309', '', '544151', '544309'],
            'Security Name': ['Chatha Foods Ltd', 'Inventurus Knowledge Solutions Ltd', 'Test Company', '', 'Another Company'],
            'Name of Person': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '', 'Valid Person', None],
            'Category of Person': ['Promoter & Director', 'Designated Person', '', None, 'Promoter'],
            'Securities Acquired/Disposed - Number': ['38000', '6000', '1000', '2000', '3000'],
            'Securities Acquired/Disposed - Value': ['3952000', '1885800', '100000', '200000', '300000'],
            'Period ##': ['11 Jul 2025 to 11 Jul 2025', '14 Jul 2025 to 14 Jul 2025', 'to', '01 Jul 2025 to 01 Jul 2025', '02 Jul 2025 to 02 Jul 2025'],
            'Reported to Exchange': ['15 Jul 2025', '15 Jul 2025', '15/07/2025', '16 Jul 2025', '17 Jul 2025']
        }
        
        test_df = pd.DataFrame(test_data)
        print(f"📊 Created test DataFrame with {len(test_df)} records")
        print("Test data:")
        for i, row in test_df.iterrows():
            print(f"  Record {i+1}: Code='{row['Security Code']}', Name='{row['Security Name']}', Person='{row['Name of Person']}', Category='{row['Category of Person']}'")
        
        # Test the validation method
        print("\n🔍 Running validation...")
        validated_df = scraper._validate_and_clean_dataframe(test_df)
        
        print(f"\n📊 Validation results:")
        print(f"   Original records: {len(test_df)}")
        print(f"   Valid records: {len(validated_df)}")
        print(f"   Filtered out: {len(test_df) - len(validated_df)}")
        
        if len(validated_df) > 0:
            print("\nValid records after filtering:")
            for i, row in validated_df.iterrows():
                print(f"  Record {i+1}: Code='{row['Security Code']}', Name='{row['Security Name']}', Person='{row['Name of Person']}', Category='{row['Category of Person']}'")
        
        # Expected: Should keep records 1, 2, and 4 (records with all required fields)
        # Should filter out records 3 and 5 (missing required fields)
        expected_valid_count = 2  # Records 1 and 2 should be valid
        
        if len(validated_df) == expected_valid_count:
            print("✅ Validation test PASSED - Correct number of records filtered")
        else:
            print(f"❌ Validation test FAILED - Expected {expected_valid_count} valid records, got {len(validated_df)}")
            
        return len(validated_df) == expected_valid_count
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_validation():
    """Test the database manager validation logic"""
    print("\n🧪 Testing BSE database manager validation logic...")
    
    try:
        from database_manager import BSEDatabaseManager
        
        # Create a test database manager instance
        db_manager = BSEDatabaseManager()
        
        # Create test records with both valid and invalid data
        test_records = [
            {
                'Security Code': '544151',
                'Security Name': 'Chatha Foods Ltd',
                'Name of Person': 'Paramjit Singh Chatha',
                'Category of Person': 'Promoter & Director',
                'Securities Acquired/Disposed - Number': '38000'
            },
            {
                'Security Code': '544309',
                'Security Name': 'Inventurus Knowledge Solutions Ltd',
                'Name of Person': 'Dinesh Rijhwani',
                'Category of Person': 'Designated Person',
                'Securities Acquired/Disposed - Number': '6000'
            },
            {
                'Security Code': '',  # Invalid - empty security code
                'Security Name': 'Test Company',
                'Name of Person': 'Test Person',
                'Category of Person': 'Promoter',
                'Securities Acquired/Disposed - Number': '1000'
            },
            {
                'Security Code': '544151',
                'Security Name': 'Chatha Foods Ltd',
                'Name of Person': '',  # Invalid - empty person name
                'Category of Person': 'Promoter',
                'Securities Acquired/Disposed - Number': '2000'
            },
            {
                'Security Code': '544309',
                'Security Name': '',  # Invalid - empty security name
                'Name of Person': 'Another Person',
                'Category of Person': 'Designated Person',
                'Securities Acquired/Disposed - Number': '3000'
            }
        ]
        
        print(f"📊 Created {len(test_records)} test records for database validation")
        
        # Test the database conversion and validation
        print("🔍 Running database validation...")
        db_records = db_manager._convert_records_to_db_format(test_records)
        
        print(f"\n📊 Database validation results:")
        print(f"   Original records: {len(test_records)}")
        print(f"   Valid records: {len(db_records)}")
        print(f"   Filtered out: {len(test_records) - len(db_records)}")
        
        # Expected: Should keep records 1 and 2 (records with all required fields)
        # Should filter out records 3, 4, and 5 (missing required fields)
        expected_valid_count = 2
        
        if len(db_records) == expected_valid_count:
            print("✅ Database validation test PASSED - Correct number of records filtered")
        else:
            print(f"❌ Database validation test FAILED - Expected {expected_valid_count} valid records, got {len(db_records)}")
            
        return len(db_records) == expected_valid_count
        
    except Exception as e:
        print(f"❌ Database validation test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting BSE scraper validation tests...\n")
    
    # Run validation tests
    validation_test_passed = test_validation_logic()
    database_test_passed = test_database_validation()
    
    print(f"\n📊 Test Results Summary:")
    print(f"   Validation Logic Test: {'✅ PASSED' if validation_test_passed else '❌ FAILED'}")
    print(f"   Database Validation Test: {'✅ PASSED' if database_test_passed else '❌ FAILED'}")
    
    if validation_test_passed and database_test_passed:
        print("\n🎉 All tests PASSED! The BSE scraper validation fixes are working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests FAILED. Please check the validation logic.")
        sys.exit(1)
