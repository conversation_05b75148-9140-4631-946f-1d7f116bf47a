#!/usr/bin/env python3
"""
Simple test for PDF duplicate detection Edge Function
Tests with proper request format to debug the missing fields error.
"""

import requests
import json
import sys

# Configuration - UPDATE THESE VALUES
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_ANON_KEY = "your-anon-key-here"  # Replace with actual key

def test_edge_function():
    """Test the Edge Function with proper request format"""
    
    # Test data - using actual BSE record format
    test_payload = {
        "record_id": "test-record-123",
        "table_name": "bse_corporate_announcements", 
        "pdf_url": "https://www.bseindia.com/xml-data/corpfiling/AttachLive/30e825bf-ce6a-492a-ba90-a185b4335e0a.pdf"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    
    url = f"{SUPABASE_URL}/functions/v1/pdf-duplicate-detector"
    
    print("🧪 Testing PDF Duplicate Detection Edge Function")
    print("=" * 60)
    print(f"📤 URL: {url}")
    print(f"📋 Payload: {json.dumps(test_payload, indent=2)}")
    print(f"📋 Headers: {json.dumps({k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()}, indent=2)}")
    print("=" * 60)
    
    try:
        print("🚀 Sending request...")
        
        response = requests.post(
            url,
            headers=headers,
            json=test_payload,
            timeout=60
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.ok:
            try:
                result = response.json()
                print("✅ SUCCESS!")
                print(f"📋 Response: {json.dumps(result, indent=2)}")
                
                if result.get('success'):
                    print(f"\n🎯 Results:")
                    print(f"   PDF Hash: {result.get('pdf_hash', 'N/A')}")
                    print(f"   Is Duplicate: {result.get('is_duplicate', 'N/A')}")
                    print(f"   Processing Time: {result.get('processing_time_ms', 'N/A')}ms")
                else:
                    print(f"❌ Function Error: {result.get('error', 'Unknown error')}")
                    
            except json.JSONDecodeError:
                print("✅ SUCCESS (Non-JSON response)")
                print(f"📋 Response Text: {response.text}")
                
        else:
            print("❌ HTTP ERROR")
            print(f"📋 Response Text: {response.text}")
            
            try:
                error_data = response.json()
                print(f"📋 Error Data: {json.dumps(error_data, indent=2)}")
            except:
                pass
                
    except requests.exceptions.Timeout:
        print("⏰ REQUEST TIMEOUT")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")

def test_with_invalid_data():
    """Test with invalid data to see error handling"""
    
    print("\n🧪 Testing with invalid data...")
    
    invalid_payloads = [
        {},  # Empty payload
        {"record_id": "123"},  # Missing fields
        {"record_id": "", "table_name": "", "pdf_url": ""},  # Empty fields
        {"record_id": "123", "table_name": "invalid_table", "pdf_url": "not-a-url"},  # Invalid data
    ]
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}'
    }
    
    url = f"{SUPABASE_URL}/functions/v1/pdf-duplicate-detector"
    
    for i, payload in enumerate(invalid_payloads, 1):
        print(f"\n📋 Test {i}: {json.dumps(payload)}")
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.ok:
                result = response.json()
                print(f"   Result: {result.get('success', False)} - {result.get('error', 'No error')}")
            else:
                print(f"   Error: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   Exception: {e}")

def main():
    """Main test function"""
    
    if SUPABASE_ANON_KEY == "your-anon-key-here":
        print("❌ Please update SUPABASE_ANON_KEY in the script")
        print("   You can find it in your Supabase project settings")
        return
    
    # Test 1: Valid request
    test_edge_function()
    
    # Test 2: Invalid requests
    test_with_invalid_data()
    
    print("\n" + "=" * 60)
    print("🏁 Testing completed!")
    print("\nIf you see 'Missing required fields' error:")
    print("1. Check that the Edge Function is deployed correctly")
    print("2. Verify the request payload format")
    print("3. Check Supabase Edge Function logs for more details")

if __name__ == "__main__":
    main()

# Usage:
# 1. Update SUPABASE_ANON_KEY with your actual key
# 2. Run: python test_edge_function_simple.py
