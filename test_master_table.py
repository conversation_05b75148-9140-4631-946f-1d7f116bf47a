#!/usr/bin/env python3
"""
Test script for master corporate announcements table
"""

import requests
import json
from datetime import datetime

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def check_table_structure():
    """Check the current structure of master_corporate_announcements table"""
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/rpc/query",
            headers=headers,
            json={"query": "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'master_corporate_announcements' ORDER BY ordinal_position;"},
            timeout=30
        )
        
        if response.status_code == 200:
            columns = response.json()
            print("📋 Current table structure:")
            for col in columns:
                print(f"   {col['column_name']} ({col['data_type']})")
            return columns
        else:
            print(f"❌ Error checking table structure: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_insert_simple_record():
    """Test inserting a simple record with existing columns only"""
    
    try:
        # Create a simple test record with only the columns we know exist
        test_record = {
            'pdf_hash': f'test-hash-{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'source_exchange': 'NSE',
            'has_nse_data': True,
            'has_bse_data': False,
            'company_name': 'Test Company Ltd',
            'nse_symbol': 'TESTCO',
            'subject': 'Test Announcement',
            'headline': 'Test Headline',
            'details': 'Test details for the announcement'
        }
        
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            json=test_record,
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            print("✅ Successfully inserted test record")
            return True
        else:
            print(f"❌ Insert failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error inserting test record: {e}")
        return False

def get_sample_nse_record():
    """Get a sample NSE record for testing"""
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements",
            headers=headers,
            params={
                'is_duplicate': 'eq.false',
                'duplicate_check_status': 'eq.completed',
                'pdf_hash': 'not.is.null',
                'limit': 1
            },
            timeout=30
        )
        
        if response.status_code == 200:
            records = response.json()
            if records:
                print("📊 Sample NSE record structure:")
                sample = records[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__}")
                return sample
            else:
                print("❌ No NSE records found")
                return None
        else:
            print(f"❌ Error fetching NSE record: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def get_sample_bse_record():
    """Get a sample BSE record for testing"""
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements",
            headers=headers,
            params={
                'is_duplicate': 'eq.false',
                'duplicate_check_status': 'eq.completed',
                'pdf_hash': 'not.is.null',
                'limit': 1
            },
            timeout=30
        )
        
        if response.status_code == 200:
            records = response.json()
            if records:
                print("📊 Sample BSE record structure:")
                sample = records[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__}")
                return sample
            else:
                print("❌ No BSE records found")
                return None
        else:
            print(f"❌ Error fetching BSE record: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_master_table_query():
    """Test querying the master table"""
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            params={'limit': 5},
            timeout=30
        )
        
        if response.status_code == 200:
            records = response.json()
            print(f"📊 Master table has {len(records)} records")
            if records:
                print("Sample record:")
                for key, value in records[0].items():
                    print(f"   {key}: {value}")
            return records
        else:
            print(f"❌ Error querying master table: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main test function"""
    
    print("🧪 Testing Master Corporate Announcements Table")
    print("=" * 60)
    
    # Check table structure
    print("\n1️⃣ Checking table structure...")
    structure = check_table_structure()
    
    # Test simple insert
    print("\n2️⃣ Testing simple record insert...")
    test_insert_simple_record()
    
    # Query master table
    print("\n3️⃣ Querying master table...")
    test_master_table_query()
    
    # Get sample records from source tables
    print("\n4️⃣ Getting sample NSE record...")
    nse_sample = get_sample_nse_record()
    
    print("\n5️⃣ Getting sample BSE record...")
    bse_sample = get_sample_bse_record()
    
    print("\n✅ Testing completed!")

if __name__ == "__main__":
    main()
