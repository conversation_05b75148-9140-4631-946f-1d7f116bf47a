import requests
import csv
from urllib.parse import quote

# Define the categories to fetch
categories = [
    "AGM/EGM",
    "Board Meeting",
    "Company Update",
    "Corp. Action",
    "Insider Trading / SAST",
    "New Listing",
    "Result",
    "Integrated Filing",
    "Others"
]

# Headers to mimic the browser request
headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "en-US,en;q=0.9",
    "origin": "https://www.bseindia.com",
    "priority": "u=1, i",
    "referer": "https://www.bseindia.com/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
}

base_url = "https://api.bseindia.com/BseIndiaAPI/api/DDLSubCategoryData/w?categoryname="

# Prepare to store all results
all_data = []

for category in categories:
    # Special handling for each category based on how the API expects it
    if category == "AGM/EGM":
        encoded_category = "AGM%2FEGM"
    elif category == "Board Meeting":
        encoded_category = "Board+Meeting"
    elif category == "Company Update":
        encoded_category = "Company+Update"
    elif category == "Corp. Action":
        encoded_category = "Corp.+Action"
    elif category == "Insider Trading / SAST":
        encoded_category = "Insider+Trading+%2F+SAST"
    elif category == "New Listing":
        encoded_category = "New+Listing"
    elif category == "Result":
        encoded_category = "Result"
    elif category == "Integrated Filing":
        encoded_category = "Integrated+Filing"
    elif category == "Others":
        encoded_category = "Others"
    else:
        encoded_category = quote(category.replace(' ', '+'))
    
    url = base_url + encoded_category
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an error for bad status codes
        
        data = response.json()
        
        # Extract subcategories
        if "Table" in data and data["Table"]:
            for item in data["Table"]:
                all_data.append({
                    "Category": category,
                    "Subcategory": item["subcategory"]
                })
        else:
            all_data.append({
                "Category": category,
                "Subcategory": "No subcategories available"
            })
            
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data for category '{category}': {e}")
        all_data.append({
            "Category": category,
            "Subcategory": "Error fetching data"
        })

# Write to CSV
csv_filename = "bse_subcategories.csv"
with open(csv_filename, mode='w', newline='', encoding='utf-8') as file:
    writer = csv.DictWriter(file, fieldnames=["Category", "Subcategory"])
    writer.writeheader()
    writer.writerows(all_data)

print(f"Data successfully written to {csv_filename}")